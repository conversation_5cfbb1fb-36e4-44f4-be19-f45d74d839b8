#!/bin/bash
# Health Check Script for Yemen Market Integration

set -euo pipefail

# Configuration
API_URL="${API_URL:-http://localhost:8000}"
TIMEOUT="${HEALTH_CHECK_TIMEOUT:-30}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Health check results
HEALTH_STATUS="HEALTHY"
FAILED_CHECKS=()

log_success() {
    echo -e "${GREEN}✓${NC} $1"
}

log_error() {
    echo -e "${RED}✗${NC} $1"
    HEALTH_STATUS="UNHEALTHY"
    FAILED_CHECKS+=("$1")
}

log_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Check API health endpoint
check_api_health() {
    echo "Checking API health..."
    
    if curl -sf --max-time ${TIMEOUT} "${API_URL}/health" > /dev/null; then
        log_success "API health endpoint"
    else
        log_error "API health endpoint unreachable"
    fi
}

# Check hypothesis testing endpoints
check_hypothesis_endpoints() {
    echo "Checking hypothesis testing endpoints..."
    
    # Check hypothesis health
    if curl -sf --max-time ${TIMEOUT} "${API_URL}/api/v1/hypothesis/health" > /dev/null; then
        log_success "Hypothesis health endpoint"
    else
        log_error "Hypothesis health endpoint unreachable"
    fi
    
    # Check hypothesis listing
    RESPONSE=$(curl -sf --max-time ${TIMEOUT} "${API_URL}/api/v1/hypothesis/" || echo "")
    if [ -n "$RESPONSE" ]; then
        HYPOTHESIS_COUNT=$(echo "$RESPONSE" | jq '.hypotheses | length' 2>/dev/null || echo "0")
        if [ "$HYPOTHESIS_COUNT" -eq 13 ]; then
            log_success "All 13 hypothesis tests available"
        else
            log_warning "Only $HYPOTHESIS_COUNT hypothesis tests available (expected 13)"
        fi
    else
        log_error "Cannot retrieve hypothesis list"
    fi
}

# Check database connectivity
check_database() {
    echo "Checking database connectivity..."
    
    if docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T postgres \
        pg_isready -U yemen_user -d yemen_market > /dev/null 2>&1; then
        log_success "PostgreSQL database"
        
        # Check table count
        TABLE_COUNT=$(docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T postgres \
            psql -U yemen_user -d yemen_market -t -c \
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "0")
        
        if [ "$TABLE_COUNT" -gt 0 ]; then
            log_success "Database schema initialized ($TABLE_COUNT tables)"
        else
            log_error "Database schema not initialized"
        fi
    else
        log_error "PostgreSQL database unreachable"
    fi
}

# Check Redis connectivity
check_redis() {
    echo "Checking Redis connectivity..."
    
    if docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T redis \
        redis-cli ping > /dev/null 2>&1; then
        log_success "Redis cache"
        
        # Check Redis memory usage
        MEMORY_USAGE=$(docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T redis \
            redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r' || echo "unknown")
        log_success "Redis memory usage: $MEMORY_USAGE"
    else
        log_error "Redis cache unreachable"
    fi
}

# Check Celery workers
check_celery_workers() {
    echo "Checking Celery workers..."
    
    if docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T worker \
        celery -A src.infrastructure.tasks.celery_app inspect ping > /dev/null 2>&1; then
        log_success "Celery workers responding"
        
        # Check active workers
        WORKER_COUNT=$(docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T worker \
            celery -A src.infrastructure.tasks.celery_app inspect active_queues | \
            grep -c "hypothesis\|analysis\|default" || echo "0")
        
        if [ "$WORKER_COUNT" -gt 0 ]; then
            log_success "Workers processing queues: hypothesis, analysis, default"
        else
            log_warning "No active worker queues found"
        fi
    else
        log_error "Celery workers not responding"
    fi
}

# Check disk space
check_disk_space() {
    echo "Checking disk space..."
    
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 80 ]; then
        log_success "Disk usage: ${DISK_USAGE}%"
    elif [ "$DISK_USAGE" -lt 90 ]; then
        log_warning "Disk usage high: ${DISK_USAGE}%"
    else
        log_error "Disk usage critical: ${DISK_USAGE}%"
    fi
}

# Check container health
check_containers() {
    echo "Checking container health..."
    
    UNHEALTHY_CONTAINERS=$(docker-compose -f docker-compose.${ENVIRONMENT}.yml ps | \
        grep -E "(Exit|unhealthy)" | wc -l || echo "0")
    
    if [ "$UNHEALTHY_CONTAINERS" -eq 0 ]; then
        log_success "All containers healthy"
    else
        log_error "$UNHEALTHY_CONTAINERS unhealthy containers found"
        docker-compose -f docker-compose.${ENVIRONMENT}.yml ps
    fi
}

# Check SSL certificate
check_ssl_certificate() {
    echo "Checking SSL certificate..."
    
    if [ "${ENVIRONMENT}" = "production" ]; then
        CERT_EXPIRY=$(echo | openssl s_client -servername api.yemen-market.org \
            -connect api.yemen-market.org:443 2>/dev/null | \
            openssl x509 -noout -dates 2>/dev/null | \
            grep notAfter | cut -d= -f2)
        
        if [ -n "$CERT_EXPIRY" ]; then
            DAYS_UNTIL_EXPIRY=$(( ($(date -d "$CERT_EXPIRY" +%s) - $(date +%s)) / 86400 ))
            if [ "$DAYS_UNTIL_EXPIRY" -gt 30 ]; then
                log_success "SSL certificate valid for $DAYS_UNTIL_EXPIRY days"
            elif [ "$DAYS_UNTIL_EXPIRY" -gt 7 ]; then
                log_warning "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
            else
                log_error "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
            fi
        else
            log_warning "Cannot check SSL certificate"
        fi
    fi
}

# Performance check
check_performance() {
    echo "Checking API performance..."
    
    START_TIME=$(date +%s%N)
    if curl -sf --max-time ${TIMEOUT} "${API_URL}/health" > /dev/null; then
        END_TIME=$(date +%s%N)
        RESPONSE_TIME=$(( ($END_TIME - $START_TIME) / 1000000 ))
        
        if [ "$RESPONSE_TIME" -lt 100 ]; then
            log_success "API response time: ${RESPONSE_TIME}ms"
        elif [ "$RESPONSE_TIME" -lt 500 ]; then
            log_warning "API response time slow: ${RESPONSE_TIME}ms"
        else
            log_error "API response time critical: ${RESPONSE_TIME}ms"
        fi
    fi
}

# Main health check
main() {
    echo "==================================="
    echo "Yemen Market Integration Health Check"
    echo "Environment: ${ENVIRONMENT}"
    echo "Timestamp: $(date)"
    echo "==================================="
    echo
    
    # Run all health checks
    check_api_health
    check_hypothesis_endpoints
    check_database
    check_redis
    check_celery_workers
    check_disk_space
    check_containers
    check_ssl_certificate
    check_performance
    
    echo
    echo "==================================="
    
    if [ "$HEALTH_STATUS" = "HEALTHY" ]; then
        echo -e "${GREEN}Overall Status: HEALTHY${NC}"
        exit 0
    else
        echo -e "${RED}Overall Status: UNHEALTHY${NC}"
        echo
        echo "Failed checks:"
        for check in "${FAILED_CHECKS[@]}"; do
            echo "  - $check"
        done
        exit 1
    fi
}

# Run main function
main