#!/bin/bash
# Production Deployment Script for Yemen Market Integration

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="${1:-production}"
ACTION="${2:-deploy}"
VERSION="${3:-latest}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker not found. Please install Docker."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose not found. Please install Docker Compose."
        exit 1
    fi
    
    # Check environment file
    if [ ! -f ".env.${ENVIRONMENT}" ]; then
        print_error "Environment file .env.${ENVIRONMENT} not found."
        print_status "Creating from example..."
        cp .env.production.example ".env.${ENVIRONMENT}"
        print_warning "Please edit .env.${ENVIRONMENT} with your configuration."
        exit 1
    fi
    
    print_status "Prerequisites check passed."
}

# Build images
build_images() {
    print_status "Building Docker images..."
    
    # Build API image
    print_status "Building API image..."
    docker build -f Dockerfile.production -t yemen-market-integration:api-${VERSION} .
    docker tag yemen-market-integration:api-${VERSION} yemen-market-integration:api-latest
    
    # Build Worker image
    print_status "Building Worker image..."
    docker build -f Dockerfile.worker.production -t yemen-market-integration:worker-${VERSION} .
    docker tag yemen-market-integration:worker-${VERSION} yemen-market-integration:worker-latest
    
    print_status "Docker images built successfully."
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    docker-compose -f docker-compose.${ENVIRONMENT}.yml run --rm api \
        python -m alembic upgrade head
    
    print_status "Database migrations completed."
}

# Deploy services
deploy_services() {
    print_status "Deploying services..."
    
    # Pull latest images if using registry
    if [ "${USE_REGISTRY:-false}" = "true" ]; then
        print_status "Pulling latest images from registry..."
        docker-compose -f docker-compose.${ENVIRONMENT}.yml pull
    fi
    
    # Deploy with zero-downtime
    docker-compose -f docker-compose.${ENVIRONMENT}.yml up -d --scale api=2
    
    # Wait for services to be healthy
    print_status "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    docker-compose -f docker-compose.${ENVIRONMENT}.yml ps
    
    # Scale down to normal after health check
    docker-compose -f docker-compose.${ENVIRONMENT}.yml up -d --scale api=1
    
    print_status "Services deployed successfully."
}

# Run health checks
run_health_checks() {
    print_status "Running health checks..."
    
    # Check API health
    if curl -f http://localhost/health > /dev/null 2>&1; then
        print_status "API health check passed."
    else
        print_error "API health check failed."
        return 1
    fi
    
    # Check worker health
    if docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T worker \
        celery -A src.infrastructure.tasks.celery_app inspect ping > /dev/null 2>&1; then
        print_status "Worker health check passed."
    else
        print_error "Worker health check failed."
        return 1
    fi
    
    # Check database connection
    if docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T postgres \
        pg_isready -U yemen_user -d yemen_market > /dev/null 2>&1; then
        print_status "Database health check passed."
    else
        print_error "Database health check failed."
        return 1
    fi
    
    print_status "All health checks passed."
}

# Backup database
backup_database() {
    print_status "Backing up database..."
    
    BACKUP_FILE="backup-$(date +'%Y%m%d-%H%M%S').sql"
    
    docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T postgres \
        pg_dump -U yemen_user yemen_market > "backups/${BACKUP_FILE}"
    
    # Compress backup
    gzip "backups/${BACKUP_FILE}"
    
    print_status "Database backed up to backups/${BACKUP_FILE}.gz"
}

# Rollback deployment
rollback_deployment() {
    print_status "Rolling back deployment..."
    
    # Stop current services
    docker-compose -f docker-compose.${ENVIRONMENT}.yml down
    
    # Restore previous version
    docker tag yemen-market-integration:api-previous yemen-market-integration:api-latest
    docker tag yemen-market-integration:worker-previous yemen-market-integration:worker-latest
    
    # Restart services
    docker-compose -f docker-compose.${ENVIRONMENT}.yml up -d
    
    print_status "Rollback completed."
}

# Main deployment logic
main() {
    print_status "Starting ${ACTION} for ${ENVIRONMENT} environment..."
    
    check_prerequisites
    
    case "${ACTION}" in
        deploy)
            build_images
            backup_database
            deploy_services
            run_migrations
            run_health_checks
            print_status "Deployment completed successfully!"
            ;;
        build)
            build_images
            ;;
        migrate)
            run_migrations
            ;;
        backup)
            backup_database
            ;;
        rollback)
            rollback_deployment
            ;;
        health)
            run_health_checks
            ;;
        *)
            print_error "Unknown action: ${ACTION}"
            echo "Usage: $0 [environment] [action] [version]"
            echo "Actions: deploy, build, migrate, backup, rollback, health"
            exit 1
            ;;
    esac
}

# Create necessary directories
mkdir -p backups logs

# Run main function
main