#!/bin/bash
# Automated Backup Script for Yemen Market Integration

set -euo pipefail

# Configuration
BACKUP_DIR="/backups"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
S3_BUCKET="${S3_BACKUP_BUCKET:-yemen-market-backups}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ENVIRONMENT="${ENVIRONMENT:-production}"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

# Create backup directory
mkdir -p "${BACKUP_DIR}"

# Backup PostgreSQL
backup_postgres() {
    log "Starting PostgreSQL backup..."
    
    DB_BACKUP_FILE="${BACKUP_DIR}/postgres_${ENVIRONMENT}_${TIMESTAMP}.sql"
    
    # Dump database
    docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T postgres \
        pg_dump -U yemen_user -d yemen_market --clean --if-exists \
        > "${DB_BACKUP_FILE}"
    
    # Compress backup
    gzip "${DB_BACKUP_FILE}"
    
    log "PostgreSQL backup completed: ${DB_BACKUP_FILE}.gz"
}

# Backup Redis
backup_redis() {
    log "Starting Redis backup..."
    
    REDIS_BACKUP_FILE="${BACKUP_DIR}/redis_${ENVIRONMENT}_${TIMESTAMP}.rdb"
    
    # Save Redis snapshot
    docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T redis \
        redis-cli BGSAVE
    
    # Wait for save to complete
    sleep 5
    
    # Copy backup file
    docker-compose -f docker-compose.${ENVIRONMENT}.yml cp \
        redis:/data/dump.rdb "${REDIS_BACKUP_FILE}"
    
    # Compress backup
    gzip "${REDIS_BACKUP_FILE}"
    
    log "Redis backup completed: ${REDIS_BACKUP_FILE}.gz"
}

# Backup hypothesis results
backup_hypothesis_results() {
    log "Starting hypothesis results backup..."
    
    RESULTS_BACKUP_FILE="${BACKUP_DIR}/hypothesis_results_${ENVIRONMENT}_${TIMESTAMP}.tar.gz"
    
    # Create tarball of results
    docker-compose -f docker-compose.${ENVIRONMENT}.yml exec -T api \
        tar czf - -C /app/data hypothesis_results \
        > "${RESULTS_BACKUP_FILE}"
    
    log "Hypothesis results backup completed: ${RESULTS_BACKUP_FILE}"
}

# Backup configuration
backup_config() {
    log "Starting configuration backup..."
    
    CONFIG_BACKUP_FILE="${BACKUP_DIR}/config_${ENVIRONMENT}_${TIMESTAMP}.tar.gz"
    
    # Create tarball of configuration files
    tar czf "${CONFIG_BACKUP_FILE}" \
        .env.${ENVIRONMENT} \
        docker-compose.${ENVIRONMENT}.yml \
        deployment/nginx/ \
        config/ \
        2>/dev/null || true
    
    log "Configuration backup completed: ${CONFIG_BACKUP_FILE}"
}

# Upload to S3
upload_to_s3() {
    if [ "${BACKUP_ENABLED:-true}" != "true" ]; then
        log "S3 upload disabled"
        return
    fi
    
    log "Uploading backups to S3..."
    
    # Check if AWS CLI is available
    if ! command -v aws &> /dev/null; then
        error "AWS CLI not found. Skipping S3 upload."
        return
    fi
    
    # Upload all backup files from today
    for file in "${BACKUP_DIR}"/*_${TIMESTAMP}*; do
        if [ -f "$file" ]; then
            aws s3 cp "$file" "s3://${S3_BUCKET}/${ENVIRONMENT}/$(basename "$file")" \
                --storage-class STANDARD_IA
            log "Uploaded: $(basename "$file")"
        fi
    done
    
    log "S3 upload completed"
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups (older than ${RETENTION_DAYS} days)..."
    
    # Clean local backups
    find "${BACKUP_DIR}" -name "*_${ENVIRONMENT}_*" -type f -mtime +${RETENTION_DAYS} -delete
    
    # Clean S3 backups
    if [ "${BACKUP_ENABLED:-true}" = "true" ] && command -v aws &> /dev/null; then
        aws s3api list-objects-v2 \
            --bucket "${S3_BUCKET}" \
            --prefix "${ENVIRONMENT}/" \
            --query "Contents[?LastModified<='$(date -d "${RETENTION_DAYS} days ago" --iso-8601)'].Key" \
            --output text | xargs -I {} aws s3 rm "s3://${S3_BUCKET}/{}"
    fi
    
    log "Cleanup completed"
}

# Verify backups
verify_backups() {
    log "Verifying backups..."
    
    VERIFICATION_PASSED=true
    
    # Check if backup files exist and have size > 0
    for pattern in "postgres_" "redis_" "hypothesis_results_" "config_"; do
        if ! ls "${BACKUP_DIR}"/${pattern}*${TIMESTAMP}* 1> /dev/null 2>&1; then
            error "Missing backup: ${pattern}"
            VERIFICATION_PASSED=false
        else
            FILE=$(ls -1 "${BACKUP_DIR}"/${pattern}*${TIMESTAMP}* | head -1)
            if [ ! -s "$FILE" ]; then
                error "Empty backup file: $FILE"
                VERIFICATION_PASSED=false
            fi
        fi
    done
    
    if [ "$VERIFICATION_PASSED" = true ]; then
        log "All backups verified successfully"
    else
        error "Backup verification failed"
        exit 1
    fi
}

# Send notification
send_notification() {
    STATUS=$1
    MESSAGE=$2
    
    if [ -n "${NOTIFICATION_EMAIL:-}" ] && [ -n "${SMTP_HOST:-}" ]; then
        echo "Subject: Yemen Market Integration Backup ${STATUS}" | \
        echo -e "Environment: ${ENVIRONMENT}\nTimestamp: ${TIMESTAMP}\n\n${MESSAGE}" | \
        docker run --rm -i \
            -e SMTP_HOST="${SMTP_HOST}" \
            -e SMTP_PORT="${SMTP_PORT:-587}" \
            -e SMTP_USER="${SMTP_USER}" \
            -e SMTP_PASSWORD="${SMTP_PASSWORD}" \
            boky/postfix \
            sendmail "${NOTIFICATION_EMAIL}"
    fi
}

# Main execution
main() {
    log "Starting backup process for ${ENVIRONMENT} environment..."
    
    # Trap errors
    trap 'error "Backup failed"; send_notification "FAILED" "Backup process failed. Check logs for details."; exit 1' ERR
    
    # Run backups
    backup_postgres
    backup_redis
    backup_hypothesis_results
    backup_config
    
    # Verify backups
    verify_backups
    
    # Upload to S3
    upload_to_s3
    
    # Clean old backups
    cleanup_old_backups
    
    # Send success notification
    send_notification "SUCCESS" "All backups completed successfully."
    
    log "Backup process completed successfully!"
}

# Run main function
main