apiVersion: v1
kind: Namespace
metadata:
  name: yemen-market-prod
---
apiVersion: v1
kind: Secret
metadata:
  name: yemen-secrets
  namespace: yemen-market-prod
type: Opaque
data:
  db-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-jwt-secret>
  api-key-secret: <base64-encoded-api-key-secret>
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: yemen-config
  namespace: yemen-market-prod
data:
  LOG_LEVEL: "info"
  WORKERS: "4"
  MAX_REQUESTS: "1000"
  HYPOTHESIS_TEST_TIMEOUT: "300"
  ENABLE_REAL_TIME_MONITORING: "true"
  ENABLE_BATCH_TESTING: "true"
  ENABLE_SSE_STREAMING: "true"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yemen-api
  namespace: yemen-market-prod
  labels:
    app: yemen-api
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yemen-api
  template:
    metadata:
      labels:
        app: yemen-api
        version: v1
    spec:
      containers:
      - name: api
        image: ghcr.io/yemen-market-integration:api-latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-secrets
              key: database-url
        - name: REDIS_URL
          value: "redis://redis-service:6379/0"
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: yemen-secrets
              key: jwt-secret
        - name: API_KEY_SECRET
          valueFrom:
            secretKeyRef:
              name: yemen-secrets
              key: api-key-secret
        envFrom:
        - configMapRef:
            name: yemen-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          readOnlyRootFilesystem: true
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: tmp
        emptyDir: {}
      - name: logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: yemen-api-service
  namespace: yemen-market-prod
  labels:
    app: yemen-api
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: yemen-api
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yemen-worker
  namespace: yemen-market-prod
  labels:
    app: yemen-worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: yemen-worker
  template:
    metadata:
      labels:
        app: yemen-worker
    spec:
      containers:
      - name: worker
        image: ghcr.io/yemen-market-integration:worker-latest
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: yemen-secrets
              key: database-url
        - name: REDIS_URL
          value: "redis://redis-service:6379/0"
        - name: CELERY_BROKER_URL
          value: "redis://redis-service:6379/1"
        - name: CELERY_RESULT_BACKEND
          value: "redis://redis-service:6379/2"
        envFrom:
        - configMapRef:
            name: yemen-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "4000m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
        volumeMounts:
        - name: hypothesis-results
          mountPath: /app/data/hypothesis_results
      volumes:
      - name: hypothesis-results
        persistentVolumeClaim:
          claimName: hypothesis-results-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: hypothesis-results-pvc
  namespace: yemen-market-prod
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 100Gi
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: yemen-api-ingress
  namespace: yemen-market-prod
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
spec:
  tls:
  - hosts:
    - api.yemen-market.org
    secretName: yemen-api-tls
  rules:
  - host: api.yemen-market.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: yemen-api-service
            port:
              number: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: yemen-api-hpa
  namespace: yemen-market-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: yemen-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: yemen-api-pdb
  namespace: yemen-market-prod
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: yemen-api