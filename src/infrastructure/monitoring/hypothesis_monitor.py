"""
Real-time monitoring for hypothesis testing framework.

This module provides monitoring capabilities for:
- Hypothesis test execution tracking
- Performance metrics collection
- Alert generation for test failures
- Resource usage monitoring
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import psutil
import json

from ...core.models.hypothesis_testing import HypothesisRegistry, HypothesisOutcome
from src.core.utils.logging import get_logger
from ...infrastructure.observability import MetricsCollector, BusinessMetrics

logger = get_logger(__name__)


class TestStatus(Enum):
    """Hypothesis test execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


@dataclass
class TestMetrics:
    """Metrics for a hypothesis test execution."""
    test_id: str
    hypothesis_id: str
    status: TestStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    outcome: Optional[HypothesisOutcome] = None
    cpu_usage_percent: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MonitoringAlert:
    """Alert generated by the monitoring system."""
    alert_id: str
    severity: str  # info, warning, error, critical
    alert_type: str
    message: str
    timestamp: datetime
    test_id: Optional[str] = None
    hypothesis_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class HypothesisTestMonitor:
    """Monitor hypothesis test execution and performance."""
    
    def __init__(
        self,
        metrics_collector: Optional[MetricsCollector] = None,
        business_metrics: Optional[BusinessMetrics] = None,
        alert_threshold_seconds: int = 300,  # 5 minutes
        max_concurrent_tests: int = 10
    ):
        """Initialize the hypothesis test monitor."""
        self.metrics_collector = metrics_collector
        self.business_metrics = business_metrics
        self.alert_threshold_seconds = alert_threshold_seconds
        self.max_concurrent_tests = max_concurrent_tests
        
        # Track active tests
        self.active_tests: Dict[str, TestMetrics] = {}
        self.completed_tests: List[TestMetrics] = []
        self.alerts: List[MonitoringAlert] = []
        
        # Performance tracking
        self.performance_history: Dict[str, List[float]] = {}  # hypothesis_id -> durations
        
        # Resource monitoring
        self._resource_monitor_task = None
        self._alert_check_task = None
    
    async def start(self):
        """Start monitoring tasks."""
        logger.info("Starting hypothesis test monitor")
        self._resource_monitor_task = asyncio.create_task(self._monitor_resources())
        self._alert_check_task = asyncio.create_task(self._check_alerts())
    
    async def stop(self):
        """Stop monitoring tasks."""
        logger.info("Stopping hypothesis test monitor")
        if self._resource_monitor_task:
            self._resource_monitor_task.cancel()
        if self._alert_check_task:
            self._alert_check_task.cancel()
    
    async def track_test_start(
        self,
        test_id: str,
        hypothesis_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Track the start of a hypothesis test."""
        metrics = TestMetrics(
            test_id=test_id,
            hypothesis_id=hypothesis_id,
            status=TestStatus.RUNNING,
            start_time=datetime.utcnow(),
            metadata=metadata or {}
        )
        
        self.active_tests[test_id] = metrics
        
        # Track concurrent tests
        if self.metrics_collector:
            self.metrics_collector.gauge(
                "hypothesis_tests_active",
                len(self.active_tests),
                {"type": "concurrent"}
            )
        
        # Check if too many concurrent tests
        if len(self.active_tests) > self.max_concurrent_tests:
            await self._create_alert(
                severity="warning",
                alert_type="high_concurrency",
                message=f"High concurrent test count: {len(self.active_tests)}",
                test_id=test_id,
                hypothesis_id=hypothesis_id
            )
        
        logger.info(f"Started tracking test {test_id} for hypothesis {hypothesis_id}")
    
    async def track_test_complete(
        self,
        test_id: str,
        outcome: HypothesisOutcome,
        error: Optional[str] = None
    ):
        """Track the completion of a hypothesis test."""
        if test_id not in self.active_tests:
            logger.warning(f"Test {test_id} not found in active tests")
            return
        
        metrics = self.active_tests[test_id]
        metrics.end_time = datetime.utcnow()
        metrics.duration_seconds = (metrics.end_time - metrics.start_time).total_seconds()
        metrics.status = TestStatus.FAILED if error else TestStatus.COMPLETED
        metrics.outcome = outcome
        metrics.error = error
        
        # Move to completed
        self.completed_tests.append(metrics)
        del self.active_tests[test_id]
        
        # Update performance history
        if metrics.hypothesis_id not in self.performance_history:
            self.performance_history[metrics.hypothesis_id] = []
        self.performance_history[metrics.hypothesis_id].append(metrics.duration_seconds)
        
        # Track metrics
        if self.metrics_collector:
            self.metrics_collector.histogram(
                "hypothesis_test_duration",
                metrics.duration_seconds,
                {"hypothesis_id": metrics.hypothesis_id, "outcome": outcome.value}
            )
            
            self.metrics_collector.counter(
                "hypothesis_tests_completed",
                1,
                {"hypothesis_id": metrics.hypothesis_id, "outcome": outcome.value}
            )
        
        # Business metrics
        if self.business_metrics and not error:
            await self.business_metrics.track_hypothesis_test(
                hypothesis_id=metrics.hypothesis_id,
                outcome=outcome.value,
                duration=metrics.duration_seconds
            )
        
        logger.info(
            f"Test {test_id} completed: {outcome.value} in {metrics.duration_seconds:.2f}s"
        )
    
    async def get_test_status(self, test_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a test."""
        if test_id in self.active_tests:
            metrics = self.active_tests[test_id]
            elapsed = (datetime.utcnow() - metrics.start_time).total_seconds()
            
            return {
                "test_id": test_id,
                "hypothesis_id": metrics.hypothesis_id,
                "status": metrics.status.value,
                "elapsed_seconds": elapsed,
                "cpu_usage_percent": metrics.cpu_usage_percent,
                "memory_usage_mb": metrics.memory_usage_mb
            }
        
        # Check completed tests
        for metrics in reversed(self.completed_tests):
            if metrics.test_id == test_id:
                return {
                    "test_id": test_id,
                    "hypothesis_id": metrics.hypothesis_id,
                    "status": metrics.status.value,
                    "duration_seconds": metrics.duration_seconds,
                    "outcome": metrics.outcome.value if metrics.outcome else None,
                    "error": metrics.error
                }
        
        return None
    
    def get_performance_summary(self, hypothesis_id: Optional[str] = None) -> Dict[str, Any]:
        """Get performance summary for hypothesis tests."""
        if hypothesis_id:
            if hypothesis_id not in self.performance_history:
                return {"error": f"No performance data for {hypothesis_id}"}
            
            durations = self.performance_history[hypothesis_id]
            return {
                "hypothesis_id": hypothesis_id,
                "test_count": len(durations),
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "last_duration": durations[-1] if durations else None
            }
        
        # Overall summary
        all_durations = []
        for durations in self.performance_history.values():
            all_durations.extend(durations)
        
        if not all_durations:
            return {"error": "No performance data available"}
        
        return {
            "total_tests": len(all_durations),
            "active_tests": len(self.active_tests),
            "avg_duration": sum(all_durations) / len(all_durations),
            "min_duration": min(all_durations),
            "max_duration": max(all_durations),
            "hypotheses_tested": len(self.performance_history)
        }
    
    def get_recent_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent monitoring alerts."""
        recent_alerts = sorted(
            self.alerts,
            key=lambda a: a.timestamp,
            reverse=True
        )[:limit]
        
        return [
            {
                "alert_id": alert.alert_id,
                "severity": alert.severity,
                "type": alert.alert_type,
                "message": alert.message,
                "timestamp": alert.timestamp.isoformat(),
                "test_id": alert.test_id,
                "hypothesis_id": alert.hypothesis_id,
                "metadata": alert.metadata
            }
            for alert in recent_alerts
        ]
    
    async def _monitor_resources(self):
        """Monitor resource usage of active tests."""
        while True:
            try:
                # Get current process info
                process = psutil.Process()
                cpu_percent = process.cpu_percent(interval=1)
                memory_mb = process.memory_info().rss / 1024 / 1024
                
                # Update active test metrics
                for test_id, metrics in self.active_tests.items():
                    metrics.cpu_usage_percent = cpu_percent
                    metrics.memory_usage_mb = memory_mb
                
                # Check resource thresholds
                if cpu_percent > 80:
                    await self._create_alert(
                        severity="warning",
                        alert_type="high_cpu",
                        message=f"High CPU usage: {cpu_percent:.1f}%"
                    )
                
                if memory_mb > 1024:  # 1GB
                    await self._create_alert(
                        severity="warning",
                        alert_type="high_memory",
                        message=f"High memory usage: {memory_mb:.1f}MB"
                    )
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring resources: {e}")
                await asyncio.sleep(10)
    
    async def _check_alerts(self):
        """Check for alert conditions."""
        while True:
            try:
                now = datetime.utcnow()
                
                # Check for long-running tests
                for test_id, metrics in self.active_tests.items():
                    elapsed = (now - metrics.start_time).total_seconds()
                    
                    if elapsed > self.alert_threshold_seconds:
                        # Check if we already alerted for this test
                        already_alerted = any(
                            alert.test_id == test_id and 
                            alert.alert_type == "long_running_test"
                            for alert in self.alerts[-10:]  # Check last 10 alerts
                        )
                        
                        if not already_alerted:
                            await self._create_alert(
                                severity="warning",
                                alert_type="long_running_test",
                                message=f"Test running for {elapsed:.0f}s (threshold: {self.alert_threshold_seconds}s)",
                                test_id=test_id,
                                hypothesis_id=metrics.hypothesis_id
                            )
                
                # Clean up old alerts (keep last 100)
                if len(self.alerts) > 100:
                    self.alerts = self.alerts[-100:]
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error checking alerts: {e}")
                await asyncio.sleep(60)
    
    async def _create_alert(
        self,
        severity: str,
        alert_type: str,
        message: str,
        test_id: Optional[str] = None,
        hypothesis_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Create a monitoring alert."""
        alert = MonitoringAlert(
            alert_id=f"{alert_type}_{int(time.time() * 1000)}",
            severity=severity,
            alert_type=alert_type,
            message=message,
            timestamp=datetime.utcnow(),
            test_id=test_id,
            hypothesis_id=hypothesis_id,
            metadata=metadata or {}
        )
        
        self.alerts.append(alert)
        
        # Log based on severity
        if severity == "critical":
            logger.error(f"CRITICAL ALERT: {message}")
        elif severity == "error":
            logger.error(f"ERROR ALERT: {message}")
        elif severity == "warning":
            logger.warning(f"WARNING ALERT: {message}")
        else:
            logger.info(f"INFO ALERT: {message}")
        
        # Track alert metrics
        if self.metrics_collector:
            self.metrics_collector.counter(
                "monitoring_alerts",
                1,
                {"severity": severity, "type": alert_type}
            )
    
    def export_metrics(self) -> Dict[str, Any]:
        """Export current metrics snapshot."""
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "active_tests": len(self.active_tests),
            "completed_tests": len(self.completed_tests),
            "total_alerts": len(self.alerts),
            "performance_summary": self.get_performance_summary(),
            "recent_alerts": self.get_recent_alerts(5),
            "active_test_details": [
                {
                    "test_id": test_id,
                    "hypothesis_id": metrics.hypothesis_id,
                    "elapsed_seconds": (datetime.utcnow() - metrics.start_time).total_seconds(),
                    "cpu_usage": metrics.cpu_usage_percent,
                    "memory_usage_mb": metrics.memory_usage_mb
                }
                for test_id, metrics in self.active_tests.items()
            ]
        }