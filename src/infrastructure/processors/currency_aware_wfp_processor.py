"""Currency Zone-Aware WFP Processor - Handles multi-exchange rate price data."""

from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Union
import logging
import pandas as pd

from .wfp_processor import WFPProcessor
from .currency_zone_classifier import CurrencyZoneClassifier
from ...core.domain.market.currency_zones import (
    CurrencyZone, ZoneExchangeRate, CurrencyFragmentationIndex,
    CurrencyZoneService
)
from ...core.domain.market.entities import Market, PriceObservation
from ...core.domain.market.value_objects import (
    MarketId, Currency, ExchangeRate, Price
)

# Import the new data quality framework
from ..data_quality.integration import EnhancedCurrencyAwareProcessor


logger = logging.getLogger(__name__)


class CurrencyAwareWFPProcessor(WFPProcessor):
    """Enhanced WFP processor that applies currency zone-specific exchange rates.
    
    This processor handles the complexity of multiple exchange rate regimes
    by applying appropriate conversion rates based on territorial control zones
    before any price comparisons.
    """
    
    def __init__(
        self,
        zone_classifier: Optional[CurrencyZoneClassifier] = None,
        enable_zone_conversion: bool = True,
        enable_advanced_quality_control: bool = True,
        **kwargs
    ):
        """Initialize currency-aware processor.
        
        Args:
            zone_classifier: Classifier for mapping markets to currency zones
            enable_zone_conversion: Whether to apply zone-specific conversions
            enable_advanced_quality_control: Whether to use the new data quality framework
            **kwargs: Arguments passed to parent WFPProcessor
        """
        super().__init__(**kwargs)
        self.zone_classifier = zone_classifier or CurrencyZoneClassifier()
        self.zone_service = CurrencyZoneService()
        self.enable_zone_conversion = enable_zone_conversion
        self.enable_advanced_quality_control = enable_advanced_quality_control
        self._zone_rates_cache: Dict[Tuple[datetime, CurrencyZone], Decimal] = {}
        
        # Initialize enhanced processor if advanced quality control is enabled
        if self.enable_advanced_quality_control:
            self.enhanced_processor = EnhancedCurrencyAwareProcessor()
            logger.info("Advanced data quality control enabled")
        else:
            self.enhanced_processor = None
            logger.info("Using legacy processing (advanced quality control disabled)")
        
    async def process(
        self,
        data_source: Union[str, pd.DataFrame],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        commodities: Optional[List[str]] = None,
        zone_aware: bool = True
    ) -> Tuple[List[Market], List[PriceObservation], List[ExchangeRate], Dict]:
        """Process WFP data with currency zone awareness.
        
        Returns:
            Tuple of (markets, price_observations, exchange_rates, zone_metrics)
            where zone_metrics contains fragmentation analysis
        """
        # First, process using parent method
        markets, price_observations, exchange_rates = await super().process(
            data_source, start_date, end_date, commodities
        )
        
        if not zone_aware or not self.enable_zone_conversion:
            return markets, price_observations, exchange_rates, {}
        
        # Use advanced quality control if enabled
        if self.enable_advanced_quality_control and self.enhanced_processor:
            logger.info("Applying advanced data quality framework...")
            
            try:
                # Process with comprehensive data quality pipeline
                enhanced_observations, quality_report = await self.enhanced_processor.process_with_quality_control(
                    observations=price_observations,
                    markets=markets,
                    target_currency=Currency.USD,
                    enable_advanced_validation=True
                )
                
                # Extract zone metrics from quality report
                zone_metrics = {
                    'quality_score': quality_report['quality_score'],
                    'data_coverage': quality_report['data_coverage'],
                    'conversion_success_rate': quality_report['conversion_success_rate'],
                    'processing_time_seconds': quality_report['processing_time_seconds'],
                    'recommendations': quality_report['recommendations'],
                    'advanced_quality_control': True,
                    'warnings': quality_report.get('warnings', [])
                }
                
                logger.info(
                    f"Advanced processing complete. Quality score: {quality_report['quality_score']:.1f}, "
                    f"Coverage: {quality_report['data_coverage']:.1f}%"
                )
                
                return markets, enhanced_observations, exchange_rates, zone_metrics
                
            except Exception as e:
                logger.error(f"Advanced processing failed: {e}. Falling back to legacy processing.")
                # Fall back to legacy processing
                return await self._legacy_process(markets, price_observations, exchange_rates)
        
        else:
            # Use legacy processing
            logger.info("Using legacy currency zone processing...")
            return await self._legacy_process(markets, price_observations, exchange_rates)
    
    async def _legacy_process(
        self,
        markets: List[Market],
        price_observations: List[PriceObservation],
        exchange_rates: List[ExchangeRate]
    ) -> Tuple[List[Market], List[PriceObservation], List[ExchangeRate], Dict]:
        """Legacy processing method (original implementation)."""
        
        # Classify markets into zones
        market_zones = self._classify_market_zones(markets, price_observations)
        
        # Create zone-specific exchange rates
        zone_exchange_rates = self._create_zone_exchange_rates(
            exchange_rates, market_zones, price_observations
        )
        
        # Adjust price observations with zone-specific rates
        adjusted_observations = self._adjust_prices_by_zone(
            price_observations, market_zones, zone_exchange_rates
        )
        
        # Calculate fragmentation metrics
        zone_metrics = self._calculate_zone_metrics(
            price_observations, adjusted_observations, market_zones
        )
        
        # Add legacy processing flag
        zone_metrics['advanced_quality_control'] = False
        zone_metrics['legacy_processing'] = True
        
        logger.info(
            f"Legacy currency zone analysis complete. "
            f"Fragmentation ratio: {zone_metrics.get('avg_fragmentation_ratio', 0):.2f}x"
        )
        
        return markets, adjusted_observations, zone_exchange_rates, zone_metrics
    
    def _classify_market_zones(
        self,
        markets: List[Market],
        observations: List[PriceObservation]
    ) -> Dict[MarketId, CurrencyZone]:
        """Classify each market into currency zones."""
        market_zones = {}
        market_lookup = {m.market_id: m for m in markets}
        
        # Get unique dates from observations
        dates = list(set(obs.observed_date for obs in observations))
        
        for market_id in market_lookup:
            market = market_lookup[market_id]
            
            # Use most recent observation date for classification
            market_obs_dates = [
                obs.observed_date for obs in observations 
                if obs.market_id == market_id
            ]
            
            if market_obs_dates:
                latest_date = max(market_obs_dates)
                zone, confidence = self.zone_classifier.classify_market(
                    market, latest_date
                )
                market_zones[market_id] = zone
                
                if confidence < 0.7:
                    logger.warning(
                        f"Low confidence ({confidence:.2f}) zone classification "
                        f"for market {market.name}: {zone.value}"
                    )
            else:
                market_zones[market_id] = CurrencyZone.UNKNOWN
                
        # Log zone distribution
        zone_counts = {}
        for zone in market_zones.values():
            zone_counts[zone] = zone_counts.get(zone, 0) + 1
            
        logger.info(f"Market zone distribution: {zone_counts}")
        
        return market_zones
    
    def _create_zone_exchange_rates(
        self,
        base_rates: List[ExchangeRate],
        market_zones: Dict[MarketId, CurrencyZone],
        observations: List[PriceObservation]
    ) -> List[ZoneExchangeRate]:
        """Create zone-specific exchange rates based on market data."""
        zone_rates = []
        
        # Get date range
        dates = sorted(set(obs.observed_date for obs in observations))
        
        # Dynamic zone rates based on actual exchange rate data
        # TODO: CRITICAL - Replace hard-coded multipliers with dynamic calculation
        # These rates must be updated based on actual market data and date
        
        # Get dynamic multipliers based on actual exchange rate data
        dynamic_zone_multipliers = self._calculate_dynamic_zone_multipliers(dates)
        
        # Fallback to estimated rates only if no data available (with warning)
        if not dynamic_zone_multipliers:
            logger.warning("No dynamic exchange rate data available - using estimated fallback rates")
            default_zone_multipliers = {
                CurrencyZone.HOUTHI: Decimal("1.0"),      # Reference baseline 
                CurrencyZone.GOVERNMENT: Decimal("3.74"),   # Estimated ~2000/535 
                CurrencyZone.CONTESTED: Decimal("2.24"),    # Estimated ~1200/535
                CurrencyZone.UNKNOWN: Decimal("1.87")       # Estimated ~1000/535
            }
            dynamic_zone_multipliers = default_zone_multipliers
        
        for date in dates:
            # Find base rate for this date
            base_rate = None
            for rate in base_rates:
                if hasattr(rate, 'date') and rate.date == date:
                    if rate.from_currency == Currency.USD and rate.to_currency == Currency.YER:
                        base_rate = rate.rate
                        break
            
            if not base_rate:
                # Use default if no rate found
                base_rate = Decimal("535")  # Historical Houthi baseline
            
            # Create zone-specific rates using dynamic multipliers
            for zone, multiplier in dynamic_zone_multipliers.items():
                zone_rate = ZoneExchangeRate(
                    zone=zone,
                    from_currency=Currency.USD,
                    to_currency=Currency.YER,
                    rate=base_rate * multiplier,
                    date=date,
                    rate_type="official_cby_sanaa" if zone == CurrencyZone.HOUTHI else "official_cby_aden",
                    source="zone_estimation",
                    confidence=0.8
                )
                zone_rates.append(zone_rate)
                
                # Cache for quick lookup
                self._zone_rates_cache[(date, zone)] = zone_rate.rate
        
        return zone_rates
    
    def _adjust_prices_by_zone(
        self,
        observations: List[PriceObservation],
        market_zones: Dict[MarketId, CurrencyZone],
        zone_rates: List[ZoneExchangeRate]
    ) -> List[PriceObservation]:
        """Adjust price observations using zone-specific exchange rates.
        
        This is where the Yemen Paradox is revealed - prices that appear
        lower in YER in conflict zones are actually higher in real terms.
        """
        adjusted_observations = []
        
        for obs in observations:
            zone = market_zones.get(obs.market_id, CurrencyZone.UNKNOWN)
            
            # If price is already in USD or no zone data, keep original
            if obs.price.currency == Currency.USD or zone == CurrencyZone.UNKNOWN:
                adjusted_observations.append(obs)
                continue
            
            # Get zone-specific rate
            zone_rate = self._zone_rates_cache.get((obs.observed_date, zone))
            if not zone_rate:
                logger.warning(
                    f"No zone rate found for {obs.observed_date} in {zone.value}"
                )
                adjusted_observations.append(obs)
                continue
            
            # Create adjusted observation with USD price
            usd_price = obs.price.convert_to(Currency.USD, zone_rate)
            
            # Create new observation with both YER and USD prices
            # Store original YER price in metadata
            adjusted_obs = PriceObservation(
                market_id=obs.market_id,
                commodity=obs.commodity,
                price=usd_price,  # Use USD price for analysis
                observed_date=obs.observed_date,
                source=f"{obs.source}_zone_adjusted",
                quality=obs.quality,
                observations_count=obs.observations_count
            )
            
            adjusted_observations.append(adjusted_obs)
        
        return adjusted_observations
    
    def _calculate_zone_metrics(
        self,
        original_observations: List[PriceObservation],
        adjusted_observations: List[PriceObservation],
        market_zones: Dict[MarketId, CurrencyZone]
    ) -> Dict:
        """Calculate metrics that demonstrate the Yemen Paradox solution."""
        metrics = {
            'total_markets': len(market_zones),
            'zone_distribution': {},
            'fragmentation_indices': [],
            'zone_price_differential_examples': []
        }
        
        # Zone distribution
        for zone in CurrencyZone:
            count = sum(1 for z in market_zones.values() if z == zone)
            metrics['zone_distribution'][zone.value] = count
        
        # Calculate fragmentation over time
        dates = sorted(set(obs.observed_date for obs in original_observations))
        
        for date in dates[-30:]:  # Last 30 dates
            houthi_rate = self._zone_rates_cache.get((date, CurrencyZone.HOUTHI))
            gov_rate = self._zone_rates_cache.get((date, CurrencyZone.GOVERNMENT))
            
            if houthi_rate and gov_rate:
                frag_index = CurrencyFragmentationIndex(
                    date=date,
                    houthi_rate=houthi_rate,
                    government_rate=gov_rate
                )
                
                metrics['fragmentation_indices'].append({
                    'date': date,
                    'fragmentation_ratio': frag_index.fragmentation_ratio,
                    'fragmentation_pct': frag_index.fragmentation_percentage,
                    'severity': frag_index.get_severity_level()
                })
        
        # Calculate average fragmentation
        if metrics['fragmentation_indices']:
            avg_ratio = sum(
                f['fragmentation_ratio'] for f in metrics['fragmentation_indices']
            ) / len(metrics['fragmentation_indices'])
            metrics['avg_fragmentation_ratio'] = avg_ratio
            
            # Estimate aid effectiveness gain
            # Simplified: assume 60% aid to Houthi areas, 40% to Government
            current_allocation = {
                CurrencyZone.HOUTHI: 0.6,
                CurrencyZone.GOVERNMENT: 0.4
            }
            
            if metrics['fragmentation_indices']:
                latest_frag = CurrencyFragmentationIndex(
                    date=metrics['fragmentation_indices'][-1]['date'],
                    houthi_rate=self._zone_rates_cache.get(
                        (metrics['fragmentation_indices'][-1]['date'], CurrencyZone.HOUTHI)
                    ),
                    government_rate=self._zone_rates_cache.get(
                        (metrics['fragmentation_indices'][-1]['date'], CurrencyZone.GOVERNMENT)
                    )
                )
                
                effectiveness_gain = self.zone_service.estimate_aid_effectiveness_gain(
                    latest_frag, current_allocation
                )
                metrics['estimated_aid_effectiveness_gain_pct'] = effectiveness_gain
        
        # Find currency zone differential examples
        self._find_zone_differential_examples(
            original_observations, adjusted_observations, 
            market_zones, metrics
        )
        
        return metrics
    
    def _find_zone_differential_examples(
        self,
        original_obs: List[PriceObservation],
        adjusted_obs: List[PriceObservation],
        market_zones: Dict[MarketId, CurrencyZone],
        metrics: Dict
    ) -> None:
        """Find examples of price differentials between currency zones after conversion."""
        # Group observations by commodity and date
        from collections import defaultdict
        
        commodity_date_prices = defaultdict(lambda: defaultdict(list))
        
        for obs in adjusted_obs:
            zone = market_zones.get(obs.market_id, CurrencyZone.UNKNOWN)
            if zone in [CurrencyZone.HOUTHI, CurrencyZone.GOVERNMENT]:
                key = (obs.commodity.name, obs.observed_date)
                commodity_date_prices[key][zone].append(obs.price.amount)
        
        # Find cases where zone prices differ after USD conversion
        differential_examples = []
        
        for (commodity, date), zone_prices in commodity_date_prices.items():
            if CurrencyZone.HOUTHI in zone_prices and CurrencyZone.GOVERNMENT in zone_prices:
                avg_houthi = sum(zone_prices[CurrencyZone.HOUTHI]) / len(zone_prices[CurrencyZone.HOUTHI])
                avg_gov = sum(zone_prices[CurrencyZone.GOVERNMENT]) / len(zone_prices[CurrencyZone.GOVERNMENT])
                
                if avg_houthi > avg_gov:  # Northern zone more expensive after conversion
                    differential_examples.append({
                        'commodity': commodity,
                        'date': date,
                        'houthi_usd_price': float(avg_houthi),
                        'government_usd_price': float(avg_gov),
                        'premium_pct': (float(avg_houthi) / float(avg_gov) - 1) * 100
                    })
        
        # Keep top 5 examples with highest differential
        differential_examples.sort(key=lambda x: x['premium_pct'], reverse=True)
        metrics['zone_price_differential_examples'] = differential_examples[:5]
    
    def _calculate_dynamic_zone_multipliers(self, dates: List[Any]) -> Dict[CurrencyZone, Decimal]:
        """Calculate dynamic zone multipliers based on actual exchange rate data.
        
        CRITICAL FIX: Replaces hard-coded multipliers with data-driven calculation.
        
        Args:
            dates: List of dates for which to calculate multipliers
            
        Returns:
            Dictionary mapping currency zones to dynamic multipliers
        """
        from datetime import datetime, timedelta
        
        if not dates:
            return {}
        
        multipliers = {}
        
        # Get latest available exchange rate data for each zone
        latest_rates = {}
        
        for zone in [CurrencyZone.HOUTHI, CurrencyZone.GOVERNMENT, 
                     CurrencyZone.CONTESTED, CurrencyZone.UNKNOWN]:
            
            # Look for cached exchange rates in recent dates
            zone_rate = None
            for date in reversed(sorted(dates)):
                cache_key = (date, zone)
                if cache_key in self._zone_rates_cache:
                    zone_rate = self._zone_rates_cache[cache_key]
                    break
            
            if zone_rate:
                latest_rates[zone] = Decimal(str(zone_rate))
        
        # Calculate multipliers relative to Houthi zone (baseline)
        if CurrencyZone.HOUTHI in latest_rates:
            baseline_rate = latest_rates[CurrencyZone.HOUTHI]
            
            for zone, rate in latest_rates.items():
                if rate > 0:  # Avoid division by zero
                    multipliers[zone] = rate / baseline_rate
                else:
                    # Fallback for invalid rates
                    multipliers[zone] = Decimal("1.0") if zone == CurrencyZone.HOUTHI else Decimal("3.0")
        
        # Validation and bounds checking
        for zone, multiplier in multipliers.items():
            # Sanity check: multipliers should be between 0.5 and 10.0
            if multiplier < Decimal("0.5") or multiplier > Decimal("10.0"):
                logger.warning(f"Suspicious multiplier for {zone}: {multiplier}. Using fallback.")
                # Use conservative fallback
                if zone == CurrencyZone.HOUTHI:
                    multipliers[zone] = Decimal("1.0")
                elif zone == CurrencyZone.GOVERNMENT:
                    multipliers[zone] = Decimal("3.74")  # Conservative estimate
                else:
                    multipliers[zone] = Decimal("2.0")   # Middle estimate
        
        # Log multiplier calculation for transparency
        logger.info(f"Calculated dynamic zone multipliers: {multipliers}")
        
        return multipliers