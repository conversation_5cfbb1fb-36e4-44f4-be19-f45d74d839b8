"""Currency Zone Classifier - Maps markets to currency zones using ACAPS control data."""

from datetime import datetime
from typing import Dict, List, Optional, Tuple
import pandas as pd
from pathlib import Path

from src.core.domain.market.currency_zones import (
    CurrencyZone, CurrencyZoneMapping, CurrencyZoneService
)
from src.core.domain.market.value_objects import MarketId, ControlStatus
from src.core.domain.market.entities import Market


class CurrencyZoneClassifier:
    """Classifies markets into currency zones based on territorial control data.
    
    This classifier is critical for implementing the Yemen Paradox solution -
    it determines which exchange rate regime applies to each market.
    """
    
    def __init__(self, acaps_data_path: Optional[Path] = None):
        """Initialize with ACAPS control area data."""
        self.acaps_data_path = acaps_data_path
        self.zone_service = CurrencyZoneService()
        self._zone_mappings: Dict[str, List[CurrencyZoneMapping]] = {}
        self._control_data: Optional[pd.DataFrame] = None
        
        if acaps_data_path and acaps_data_path.exists():
            self._load_control_data()
    
    def _load_control_data(self) -> None:
        """Load ACAPS territorial control data."""
        # ACAPS provides shapefiles with control areas
        # For now, we'll use a simplified mapping based on governorate
        # In production, this would use actual GIS data
        pass
    
    def classify_market(
        self,
        market: Market,
        date: datetime
    ) -> Tuple[CurrencyZone, float]:
        """Classify a market into a currency zone at a specific date.
        
        Returns:
            Tuple of (CurrencyZone, confidence_score)
        """
        # First check if we have explicit mapping
        market_key = str(market.market_id.value)
        if market_key in self._zone_mappings:
            for mapping in self._zone_mappings[market_key]:
                if mapping.is_active_at(date):
                    return mapping.zone, mapping.confidence
        
        # Fall back to control status
        if market.control_status:
            zone = self.zone_service.get_zone_from_control_status(market.control_status)
            # Lower confidence for derived zones
            confidence = 0.7 if zone != CurrencyZone.UNKNOWN else 0.3
            return zone, confidence
        
        # Use governorate-based heuristics as last resort
        zone = self._classify_by_governorate(market.governorate)
        confidence = 0.5  # Low confidence for heuristic classification
        
        return zone, confidence
    
    def _classify_by_governorate(self, governorate: str) -> CurrencyZone:
        """Classify based on governorate using known control patterns.
        
        This is a simplified heuristic based on typical control patterns.
        In production, use actual ACAPS GIS data.
        """
        governorate_lower = governorate.lower().strip()
        
        # Houthi-controlled governorates (as of 2024)
        houthi_governorates = {
            "sana'a", "sanaa", "sa'ada", "saada", "amran", 
            "hajjah", "al mahwit", "dhamar", "raymah", "ibb"
        }
        
        # Government-controlled governorates
        government_governorates = {
            "aden", "lahj", "abyan", "al dhale", "shabwah",
            "hadramaut", "al mahrah", "socotra"
        }
        
        # Contested/mixed control
        contested_governorates = {
            "taiz", "al bayda", "marib", "al jawf", "al hudaydah"
        }
        
        if governorate_lower in houthi_governorates:
            return CurrencyZone.HOUTHI
        elif governorate_lower in government_governorates:
            return CurrencyZone.GOVERNMENT
        elif governorate_lower in contested_governorates:
            return CurrencyZone.CONTESTED
        else:
            return CurrencyZone.UNKNOWN
    
    def add_manual_mapping(
        self,
        market_id: str,
        zone: CurrencyZone,
        start_date: datetime,
        end_date: Optional[datetime] = None,
        confidence: float = 0.9,
        source: str = "manual"
    ) -> None:
        """Add manual zone mapping for specific markets."""
        mapping = CurrencyZoneMapping(
            market_id=MarketId(market_id),
            zone=zone,
            start_date=start_date,
            end_date=end_date,
            confidence=confidence,
            source=source
        )
        
        if market_id not in self._zone_mappings:
            self._zone_mappings[market_id] = []
        
        self._zone_mappings[market_id].append(mapping)
        
        # Sort by start date for efficient lookup
        self._zone_mappings[market_id].sort(key=lambda m: m.start_date)
    
    def load_historical_mappings(self, mappings_file: Path) -> None:
        """Load historical zone mappings from file.
        
        Expected format: CSV with columns:
        market_id, zone, start_date, end_date, confidence, source
        """
        if not mappings_file.exists():
            return
        
        df = pd.read_csv(mappings_file)
        for _, row in df.iterrows():
            self.add_manual_mapping(
                market_id=row['market_id'],
                zone=CurrencyZone(row['zone']),
                start_date=pd.to_datetime(row['start_date']),
                end_date=pd.to_datetime(row['end_date']) if pd.notna(row['end_date']) else None,
                confidence=row.get('confidence', 0.9),
                source=row.get('source', 'historical_data')
            )
    
    def create_zone_summary(
        self,
        markets: List[Market],
        date: datetime
    ) -> pd.DataFrame:
        """Create summary of zone classifications for all markets."""
        data = []
        
        for market in markets:
            zone, confidence = self.classify_market(market, date)
            
            data.append({
                'market_id': market.market_id.value,
                'market_name': market.name,
                'governorate': market.governorate,
                'district': market.district,
                'currency_zone': zone.value,
                'confidence': confidence,
                'control_status': market.control_status.value if market.control_status else None,
                'classification_date': date
            })
        
        return pd.DataFrame(data)
    
    def validate_classifications(
        self,
        markets: List[Market],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, float]:
        """Validate zone classifications over time period.
        
        Returns metrics on classification stability and confidence.
        """
        total_classifications = 0
        high_confidence = 0
        zone_changes = 0
        
        for market in markets:
            prev_zone = None
            
            # Sample weekly to check stability
            current_date = start_date
            while current_date <= end_date:
                zone, confidence = self.classify_market(market, current_date)
                
                total_classifications += 1
                if confidence >= 0.8:
                    high_confidence += 1
                
                if prev_zone and prev_zone != zone:
                    zone_changes += 1
                
                prev_zone = zone
                current_date += pd.Timedelta(days=7)
        
        return {
            'total_classifications': total_classifications,
            'high_confidence_pct': high_confidence / total_classifications * 100,
            'zone_change_rate': zone_changes / len(markets),
            'avg_stability': 1 - (zone_changes / total_classifications)
        }