"""
Multi-Source Exchange Rate Collection System

Collects exchange rates from multiple sources to ensure robust and accurate
rate tracking across different currency zones in Yemen.

Sources:
1. Central Bank of Yemen - Aden (CBY Aden)
2. Central Bank of Yemen - Sana'a (CBY Sana'a)
3. Money changer networks (parallel market)
4. NGO/humanitarian organization reports
5. Social media monitoring (Twitter/Telegram)
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import pandas as pd
import numpy as np
from decimal import Decimal
import re
from bs4 import BeautifulSoup

from ...core.domain.market.currency_zones import CurrencyZone, ZoneExchangeRate
from ...core.domain.market.value_objects import Currency
from src.core.utils.logging import get_logger


logger = get_logger(__name__)


@dataclass
class ExchangeRateObservation:
    """Single exchange rate observation from a source."""
    source: str
    zone: CurrencyZone
    from_currency: Currency
    to_currency: Currency
    rate: Decimal
    date: datetime
    rate_type: str  # 'official', 'parallel', 'bank'
    confidence: float  # 0-1 confidence score
    metadata: Dict[str, any] = None


@dataclass
class ValidationResult:
    """Result of rate validation."""
    is_valid: bool
    issues: List[str]
    adjusted_rate: Optional[Decimal] = None
    confidence_adjustment: float = 0.0


class ExchangeRateSource(ABC):
    """Abstract base class for exchange rate sources."""

    @abstractmethod
    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch exchange rates for a given date."""
        pass

    @abstractmethod
    def get_source_name(self) -> str:
        """Get the name of this source."""
        pass

    @abstractmethod
    def get_reliability_score(self) -> float:
        """Get reliability score for this source (0-1)."""
        pass


class CBYAdenScraper(ExchangeRateSource):
    """Scraper for Central Bank of Yemen - Aden."""

    def __init__(self):
        self.base_url = "https://cby-aden.gov.ye"  # Hypothetical URL
        self.reliability = 0.9

    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch rates from CBY Aden website."""
        logger.info(f"Fetching rates from CBY Aden for {date}")

        # In production, this would actually scrape the website
        # For now, return realistic mock data
        rates = []

        # Official CBY Aden rate (government zone)
        base_rate = Decimal("2000") + Decimal(str(np.random.normal(0, 20)))

        rates.append(ExchangeRateObservation(
            source=self.get_source_name(),
            zone=CurrencyZone.GOVERNMENT,
            from_currency=Currency.USD,
            to_currency=Currency.YER,
            rate=base_rate,
            date=date,
            rate_type="official_cby_aden",
            confidence=0.95,
            metadata={"url": f"{self.base_url}/rates/{date.strftime('%Y-%m-%d')}"}
        ))

        return rates

    def get_source_name(self) -> str:
        return "CBY_Aden_Official"

    def get_reliability_score(self) -> float:
        return self.reliability


class CBYSanaaScraper(ExchangeRateSource):
    """Scraper for Central Bank of Yemen - Sana'a."""

    def __init__(self):
        self.base_url = "https://cby-sanaa.gov.ye"  # Hypothetical URL
        self.reliability = 0.85  # Slightly lower due to less frequent updates

    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch rates from CBY Sana'a website."""
        logger.info(f"Fetching rates from CBY Sana'a for {date}")

        rates = []

        # Official CBY Sana'a rate (Houthi zone) - much more stable
        base_rate = Decimal("535") + Decimal(str(np.random.normal(0, 2)))

        rates.append(ExchangeRateObservation(
            source=self.get_source_name(),
            zone=CurrencyZone.HOUTHI,
            from_currency=Currency.USD,
            to_currency=Currency.YER,
            rate=base_rate,
            date=date,
            rate_type="official_cby_sanaa",
            confidence=0.90,
            metadata={"url": f"{self.base_url}/exchange-rates"}
        ))

        return rates

    def get_source_name(self) -> str:
        return "CBY_Sanaa_Official"

    def get_reliability_score(self) -> float:
        return self.reliability


class MoneyChangerAPI(ExchangeRateSource):
    """API for money changer network (parallel market rates)."""

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.base_url = "https://api.yemenexchange.com"  # Hypothetical API
        self.reliability = 0.75

    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Fetch parallel market rates from money changers."""
        logger.info(f"Fetching money changer rates for {date}")

        rates = []

        # Parallel rates typically show premiums
        # Government areas - higher premium
        gov_rate = Decimal("2100") + Decimal(str(np.random.normal(0, 30)))
        rates.append(ExchangeRateObservation(
            source=self.get_source_name(),
            zone=CurrencyZone.GOVERNMENT,
            from_currency=Currency.USD,
            to_currency=Currency.YER,
            rate=gov_rate,
            date=date,
            rate_type="parallel",
            confidence=0.80,
            metadata={"market": "aden_gold_market"}
        ))

        # Houthi areas - smaller premium
        houthi_rate = Decimal("545") + Decimal(str(np.random.normal(0, 5)))
        rates.append(ExchangeRateObservation(
            source=self.get_source_name(),
            zone=CurrencyZone.HOUTHI,
            from_currency=Currency.USD,
            to_currency=Currency.YER,
            rate=houthi_rate,
            date=date,
            rate_type="parallel",
            confidence=0.80,
            metadata={"market": "sanaa_money_changers"}
        ))

        # Contested areas - high volatility
        contested_rate = Decimal("1200") + Decimal(str(np.random.normal(0, 50)))
        rates.append(ExchangeRateObservation(
            source=self.get_source_name(),
            zone=CurrencyZone.CONTESTED,
            from_currency=Currency.USD,
            to_currency=Currency.YER,
            rate=contested_rate,
            date=date,
            rate_type="parallel",
            confidence=0.65,
            metadata={"market": "taiz_exchange"}
        ))

        return rates

    def get_source_name(self) -> str:
        return "MoneyChanger_Network"

    def get_reliability_score(self) -> float:
        return self.reliability


class NGOReportParser(ExchangeRateSource):
    """Parser for humanitarian organization reports containing exchange rates."""

    def __init__(self):
        self.reliability = 0.85
        self.report_sources = [
            "WFP_Yemen",
            "OCHA_Yemen",
            "REACH_Initiative",
            "Cash_Consortium_Yemen"
        ]

    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Parse exchange rates from NGO reports."""
        logger.info(f"Parsing NGO reports for rates on {date}")

        rates = []

        # NGOs typically report monthly averages
        # Check if we're near month end for fresh data
        if date.day >= 25:
            confidence = 0.90
        else:
            confidence = 0.75

        # NGO reported rates (usually conservative)
        rates.extend([
            ExchangeRateObservation(
                source="WFP_Monthly_Report",
                zone=CurrencyZone.GOVERNMENT,
                from_currency=Currency.USD,
                to_currency=Currency.YER,
                rate=Decimal("1950"),
                date=date,
                rate_type="ngo_reported",
                confidence=confidence,
                metadata={"report": "WFP_Yemen_Market_Monitor"}
            ),
            ExchangeRateObservation(
                source="OCHA_Humanitarian_Update",
                zone=CurrencyZone.HOUTHI,
                from_currency=Currency.USD,
                to_currency=Currency.YER,
                rate=Decimal("530"),
                date=date,
                rate_type="ngo_reported",
                confidence=confidence,
                metadata={"report": "OCHA_Humanitarian_Update_Yemen"}
            )
        ])

        return rates

    def get_source_name(self) -> str:
        return "NGO_Reports"

    def get_reliability_score(self) -> float:
        return self.reliability


class SocialMediaMonitor(ExchangeRateSource):
    """Monitor social media for exchange rate mentions."""

    def __init__(self):
        self.reliability = 0.60  # Lower reliability, but real-time
        self.platforms = ["twitter", "telegram"]

    async def fetch_rates(self, date: datetime) -> List[ExchangeRateObservation]:
        """Extract rates from social media posts."""
        logger.info(f"Monitoring social media for rates on {date}")

        # In production, would use Twitter API and Telegram bot
        # This provides early warning of rate changes

        rates = []

        # Social media often reports black market extremes
        if np.random.random() > 0.7:  # 30% chance of capturing spike
            spike_rate = Decimal("2200")
            rates.append(ExchangeRateObservation(
                source="Twitter_YemenTraders",
                zone=CurrencyZone.GOVERNMENT,
                from_currency=Currency.USD,
                to_currency=Currency.YER,
                rate=spike_rate,
                date=date,
                rate_type="social_media",
                confidence=0.50,
                metadata={"platform": "twitter", "sentiment": "panic"}
            ))

        return rates

    def get_source_name(self) -> str:
        return "Social_Media_Monitor"

    def get_reliability_score(self) -> float:
        return self.reliability


class ExchangeRateCollector:
    """Main collector that aggregates from all sources."""

    def __init__(self, sources: Optional[List[ExchangeRateSource]] = None):
        """Initialize with rate sources."""
        if sources is None:
            sources = [
                CBYAdenScraper(),
                CBYSanaaScraper(),
                MoneyChangerAPI(),
                NGOReportParser(),
                SocialMediaMonitor()
            ]
        self.sources = sources
        self.validation_engine = RateValidationEngine()
        self.imputation_engine = RateImputationEngine()

    async def collect_daily_rates(
        self,
        date: Optional[datetime] = None
    ) -> List[ZoneExchangeRate]:
        """Collect rates from all sources for a given date."""
        if date is None:
            date = datetime.now()

        logger.info(f"Collecting exchange rates for {date}")

        # Fetch from all sources concurrently
        tasks = [source.fetch_rates(date) for source in self.sources]
        all_observations = await asyncio.gather(*tasks)

        # Flatten observations
        observations = [obs for source_obs in all_observations for obs in source_obs]

        # Validate observations
        validated_rates = []
        for obs in observations:
            validation = self.validation_engine.validate_rate(obs)

            if validation.is_valid:
                # Convert to ZoneExchangeRate
                zone_rate = ZoneExchangeRate(
                    zone=obs.zone,
                    from_currency=obs.from_currency,
                    to_currency=obs.to_currency,
                    rate=validation.adjusted_rate or obs.rate,
                    date=obs.date,
                    rate_type=obs.rate_type,
                    source=obs.source,
                    confidence=obs.confidence + validation.confidence_adjustment
                )
                validated_rates.append(zone_rate)
            else:
                logger.warning(f"Invalid rate from {obs.source}: {validation.issues}")

        # Aggregate rates by zone
        final_rates = self._aggregate_by_zone(validated_rates)

        logger.info(f"Collected {len(final_rates)} valid exchange rates")

        return final_rates

    def _aggregate_by_zone(
        self,
        rates: List[ZoneExchangeRate]
    ) -> List[ZoneExchangeRate]:
        """Aggregate multiple rates per zone into best estimate."""
        # Group by zone and date
        zone_groups = {}

        for rate in rates:
            key = (rate.zone, rate.date)
            if key not in zone_groups:
                zone_groups[key] = []
            zone_groups[key].append(rate)

        aggregated = []

        for (zone, date), group in zone_groups.items():
            # Weight by confidence and source reliability
            weights = []
            values = []

            for rate in group:
                # Get source reliability
                source_obj = next(
                    (s for s in self.sources if s.get_source_name() in rate.source),
                    None
                )
                reliability = source_obj.get_reliability_score() if source_obj else 0.5

                weight = rate.confidence * reliability
                weights.append(weight)
                values.append(float(rate.rate))

            # Weighted average
            weights = np.array(weights)
            values = np.array(values)
            weights = weights / weights.sum()

            weighted_rate = np.sum(weights * values)
            combined_confidence = np.sum(weights * [r.confidence for r in group])

            # Determine rate type (most common)
            rate_types = [r.rate_type for r in group]
            main_rate_type = max(set(rate_types), key=rate_types.count)

            aggregated.append(ZoneExchangeRate(
                zone=zone,
                from_currency=Currency.USD,
                to_currency=Currency.YER,
                rate=Decimal(str(weighted_rate)),
                date=date,
                rate_type=main_rate_type,
                source="aggregated",
                confidence=min(combined_confidence, 0.95)
            ))

        return aggregated

    async def collect_historical_rates(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> pd.DataFrame:
        """Collect historical rates for a date range."""
        logger.info(f"Collecting historical rates from {start_date} to {end_date}")

        all_rates = []
        current_date = start_date

        while current_date <= end_date:
            daily_rates = await self.collect_daily_rates(current_date)

            for rate in daily_rates:
                all_rates.append({
                    'date': rate.date,
                    'zone': rate.zone.value,
                    'rate': float(rate.rate),
                    'rate_type': rate.rate_type,
                    'confidence': rate.confidence
                })

            current_date += timedelta(days=1)

        return pd.DataFrame(all_rates)

    def impute_missing_rates(
        self,
        existing_rates: pd.DataFrame,
        date_range: Tuple[datetime, datetime]
    ) -> pd.DataFrame:
        """Impute missing rates using various strategies."""
        return self.imputation_engine.impute_missing(existing_rates, date_range)


class RateValidationEngine:
    """Validates exchange rates against rules and historical patterns."""

    def __init__(self):
        # Zone-specific bounds based on historical patterns
        self.bounds = {
            CurrencyZone.HOUTHI: (500, 600),      # Stable range
            CurrencyZone.GOVERNMENT: (1500, 2500), # High volatility
            CurrencyZone.CONTESTED: (800, 1800),   # Middle ground
            CurrencyZone.UNKNOWN: (500, 2500)      # Wide range
        }

        # Maximum daily change percentages
        self.max_daily_change = {
            CurrencyZone.HOUTHI: 0.02,      # 2% max daily change
            CurrencyZone.GOVERNMENT: 0.10,   # 10% max daily change
            CurrencyZone.CONTESTED: 0.15,    # 15% max daily change
            CurrencyZone.UNKNOWN: 0.20       # 20% max daily change
        }

    def validate_rate(self, observation: ExchangeRateObservation) -> ValidationResult:
        """Validate a single rate observation."""
        issues = []
        confidence_adjustment = 0.0

        # Check bounds
        min_rate, max_rate = self.bounds.get(observation.zone, (0, float('inf')))
        rate_value = float(observation.rate)

        if rate_value < min_rate:
            issues.append(f"Rate {rate_value} below minimum {min_rate} for {observation.zone.value}")
            confidence_adjustment -= 0.2
        elif rate_value > max_rate:
            issues.append(f"Rate {rate_value} above maximum {max_rate} for {observation.zone.value}")
            confidence_adjustment -= 0.2

        # Check rate type consistency
        if observation.zone == CurrencyZone.HOUTHI and "aden" in observation.rate_type:
            issues.append("Houthi zone using Aden rate type")
            confidence_adjustment -= 0.3
        elif observation.zone == CurrencyZone.GOVERNMENT and "sanaa" in observation.rate_type:
            issues.append("Government zone using Sanaa rate type")
            confidence_adjustment -= 0.3

        # Adjust rate if needed
        adjusted_rate = None
        if issues and rate_value < min_rate:
            adjusted_rate = Decimal(str(min_rate))
        elif issues and rate_value > max_rate:
            adjusted_rate = Decimal(str(max_rate))

        is_valid = len(issues) == 0 or confidence_adjustment > -0.5

        return ValidationResult(
            is_valid=is_valid,
            issues=issues,
            adjusted_rate=adjusted_rate,
            confidence_adjustment=confidence_adjustment
        )

    def check_temporal_consistency(
        self,
        current_rate: Decimal,
        previous_rate: Decimal,
        zone: CurrencyZone
    ) -> bool:
        """Check if rate change is within acceptable bounds."""
        if previous_rate == 0:
            return True

        change_pct = abs(float(current_rate - previous_rate)) / float(previous_rate)
        max_change = self.max_daily_change.get(zone, 0.20)

        return change_pct <= max_change


class RateImputationEngine:
    """Handles missing rate imputation with conflict awareness."""

    def impute_missing(
        self,
        rates_df: pd.DataFrame,
        date_range: Tuple[datetime, datetime]
    ) -> pd.DataFrame:
        """Impute missing rates using various strategies."""
        # Create complete date range
        date_index = pd.date_range(start=date_range[0], end=date_range[1], freq='D')

        # Impute by zone
        imputed_dfs = []

        for zone in CurrencyZone:
            zone_df = rates_df[rates_df['zone'] == zone.value].copy()

            if zone_df.empty:
                # No data for this zone - use defaults
                default_rates = self._get_default_rates(zone)
                zone_df = pd.DataFrame({
                    'date': date_index,
                    'zone': zone.value,
                    'rate': default_rates[zone],
                    'confidence': 0.5,
                    'imputed': True
                })
            else:
                # Reindex to complete date range
                zone_df = zone_df.set_index('date').reindex(date_index)
                zone_df['zone'] = zone.value

                # Forward fill for stable zones (Houthi)
                if zone == CurrencyZone.HOUTHI:
                    zone_df['rate'] = zone_df['rate'].fillna(method='ffill', limit=7)

                # Interpolate for volatile zones
                elif zone == CurrencyZone.GOVERNMENT:
                    zone_df['rate'] = zone_df['rate'].interpolate(method='linear', limit=3)

                # Use rolling mean for contested zones
                elif zone == CurrencyZone.CONTESTED:
                    zone_df['rate'] = zone_df['rate'].fillna(
                        zone_df['rate'].rolling(window=7, min_periods=1).mean()
                    )

                # Mark imputed values
                zone_df['imputed'] = zone_df['rate'].isna()
                zone_df['confidence'] = zone_df['confidence'].fillna(0.3)

                # Final forward fill for remaining gaps
                zone_df['rate'] = zone_df['rate'].fillna(method='ffill')
                zone_df['rate'] = zone_df['rate'].fillna(method='bfill')

                zone_df = zone_df.reset_index()

            imputed_dfs.append(zone_df)

        return pd.concat(imputed_dfs, ignore_index=True)

    def _get_default_rates(self, zone: CurrencyZone) -> Dict[CurrencyZone, float]:
        """Get default rates by zone based on historical averages."""
        return {
            CurrencyZone.HOUTHI: 535,
            CurrencyZone.GOVERNMENT: 2000,
            CurrencyZone.CONTESTED: 1200,
            CurrencyZone.UNKNOWN: 1000
        }
