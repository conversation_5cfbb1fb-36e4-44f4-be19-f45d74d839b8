"""
Conflict-Aware Missing Data Imputation for Yemen Market Data.

Implements sophisticated imputation methods that account for the fact that
missing data in conflict settings is NOT missing at random (MNAR) but is
systematically related to conflict intensity and market disruption.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.impute import KNNImputer

from ...core.domain.market.currency_zones import CurrencyZone
from ...core.domain.market.value_objects import Currency, Price
from ...core.domain.market.entities import Market, PriceObservation, Commodity
from ...core.utils.logging import get_logger

logger = get_logger(__name__)


class MissingDataPattern(Enum):
    """Types of missing data patterns in conflict settings."""
    CONFLICT_RELATED = "conflict_related"  # Missing due to active conflict
    SIEGE_RELATED = "siege_related"       # Missing due to siege/blockade
    INFRASTRUCTURE_RELATED = "infrastructure_related"  # Missing due to infrastructure damage
    TRADER_EXODUS = "trader_exodus"       # Missing due to trader displacement
    SEASONAL_CLOSURE = "seasonal_closure"  # Missing due to seasonal factors
    SYSTEMATIC_GAPS = "systematic_gaps"   # Regular pattern of missing data
    RANDOM_MISSING = "random_missing"     # Truly random missing (rare)


class ImputationMethod(Enum):
    """Available imputation methods for conflict-aware missing data."""
    CONFLICT_WEIGHTED_MEAN = "conflict_weighted_mean"
    SPATIAL_INTERPOLATION = "spatial_interpolation"
    TEMPORAL_REGRESSION = "temporal_regression"
    ZONE_SPECIFIC_KNN = "zone_specific_knn"
    CONFLICT_INTENSITY_MODEL = "conflict_intensity_model"
    CROSS_COMMODITY_IMPUTATION = "cross_commodity_imputation"
    SURVIVOR_BIAS_CORRECTION = "survivor_bias_correction"


@dataclass
class ConflictContext:
    """Conflict context information for a specific market/time."""
    date: datetime
    market_id: str
    zone: CurrencyZone
    conflict_intensity: float  # 0-1 scale
    siege_status: bool
    infrastructure_damage: float  # 0-1 scale
    trader_presence: float  # 0-1 scale (proxy for market activity)
    accessibility: float  # 0-1 scale
    nearby_conflict_events: int
    days_since_last_report: int


@dataclass
class ImputationRequest:
    """Request for missing data imputation."""
    market_data: pd.DataFrame  # Panel data with missing values
    conflict_data: List[ConflictContext]  # Conflict context for each market/time
    commodity: str
    zone: CurrencyZone
    imputation_method: ImputationMethod
    confidence_threshold: float = 0.7
    max_interpolation_days: int = 30
    
    # Results
    imputed_data: Optional[pd.DataFrame] = None
    imputation_confidence: Dict[Tuple[str, datetime], float] = field(default_factory=dict)
    imputation_flags: Dict[Tuple[str, datetime], List[str]] = field(default_factory=dict)


@dataclass
class ImputationResult:
    """Result of missing data imputation."""
    success: bool
    imputed_observations: List[PriceObservation]
    confidence_scores: Dict[Tuple[str, datetime], float]
    imputation_flags: Dict[Tuple[str, datetime], List[str]]
    quality_metrics: Dict[str, Any]
    method_used: ImputationMethod
    warnings: List[str]
    metadata: Dict[str, Any]


class ConflictAwareImputation:
    """
    Implements missing data imputation that accounts for conflict-related patterns.
    
    This system recognizes that in conflict settings, missing data is not random
    but systematically related to conflict intensity, market disruption, and
    trader behavior. It implements specialized imputation methods that account
    for these patterns.
    """
    
    def __init__(
        self,
        max_imputation_gap_days: int = 30,
        min_confidence_threshold: float = 0.5,
        enable_survivor_bias_correction: bool = True
    ):
        """
        Initialize conflict-aware imputation system.
        
        Args:
            max_imputation_gap_days: Maximum gap to impute (longer gaps flagged as unreliable)
            min_confidence_threshold: Minimum confidence to accept imputation
            enable_survivor_bias_correction: Whether to apply survivor bias corrections
        """
        self.max_imputation_gap_days = max_imputation_gap_days
        self.min_confidence_threshold = min_confidence_threshold
        self.enable_survivor_bias_correction = enable_survivor_bias_correction
        
        # Imputation models and parameters
        self._imputation_models: Dict[ImputationMethod, Any] = {}
        self._conflict_intensity_cache: Dict[Tuple[str, datetime], float] = {}
        
        # Initialize models
        self._initialize_imputation_models()
        
        # Validation metrics
        self._validation_results: Dict[str, Any] = {}
        
    def _initialize_imputation_models(self) -> None:
        """Initialize machine learning models for imputation."""
        
        # Random Forest for complex pattern recognition
        self._imputation_models[ImputationMethod.CONFLICT_INTENSITY_MODEL] = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        
        # KNN imputer for spatial similarity
        self._imputation_models[ImputationMethod.ZONE_SPECIFIC_KNN] = KNNImputer(
            n_neighbors=5,
            weights='distance'
        )
        
        # Scaler for preprocessing
        self._scaler = StandardScaler()
        
    async def impute_missing_data(
        self,
        market_data: pd.DataFrame,
        conflict_contexts: List[ConflictContext],
        commodity: str,
        zone: CurrencyZone,
        method: ImputationMethod = ImputationMethod.CONFLICT_INTENSITY_MODEL
    ) -> ImputationResult:
        """
        Impute missing price data using conflict-aware methods.
        
        Args:
            market_data: Panel data with missing values (markets x time)
            conflict_contexts: Conflict context for each market/time combination
            commodity: Commodity being analyzed
            zone: Currency zone for the markets
            method: Imputation method to use
            
        Returns:
            Imputation result with filled data and confidence scores
        """
        logger.info(f"Starting conflict-aware imputation for {commodity} in {zone.value} using {method.value}")
        
        # Analyze missing data patterns
        missing_patterns = self._analyze_missing_patterns(market_data, conflict_contexts)
        
        # Create imputation request
        request = ImputationRequest(
            market_data=market_data.copy(),
            conflict_data=conflict_contexts,
            commodity=commodity,
            zone=zone,
            imputation_method=method
        )
        
        # Apply appropriate imputation method
        try:
            if method == ImputationMethod.CONFLICT_WEIGHTED_MEAN:
                await self._conflict_weighted_mean_imputation(request)
            elif method == ImputationMethod.SPATIAL_INTERPOLATION:
                await self._spatial_interpolation_imputation(request)
            elif method == ImputationMethod.TEMPORAL_REGRESSION:
                await self._temporal_regression_imputation(request)
            elif method == ImputationMethod.ZONE_SPECIFIC_KNN:
                await self._zone_specific_knn_imputation(request)
            elif method == ImputationMethod.CONFLICT_INTENSITY_MODEL:
                await self._conflict_intensity_model_imputation(request)
            elif method == ImputationMethod.CROSS_COMMODITY_IMPUTATION:
                await self._cross_commodity_imputation(request)
            elif method == ImputationMethod.SURVIVOR_BIAS_CORRECTION:
                await self._survivor_bias_correction_imputation(request)
            else:
                raise ValueError(f"Unsupported imputation method: {method}")
            
            # Apply survivor bias correction if enabled
            if self.enable_survivor_bias_correction and method != ImputationMethod.SURVIVOR_BIAS_CORRECTION:
                await self._apply_survivor_bias_correction(request)
            
            # Validate imputation results
            validation_metrics = self._validate_imputation_results(request)
            
            # Convert to price observations
            imputed_observations = self._convert_to_price_observations(request)
            
            success = len(imputed_observations) > 0
            
            return ImputationResult(
                success=success,
                imputed_observations=imputed_observations,
                confidence_scores=request.imputation_confidence,
                imputation_flags=request.imputation_flags,
                quality_metrics=validation_metrics,
                method_used=method,
                warnings=[],
                metadata={
                    'missing_patterns': missing_patterns,
                    'total_imputed': len(imputed_observations),
                    'avg_confidence': np.mean(list(request.imputation_confidence.values())) if request.imputation_confidence else 0
                }
            )
            
        except Exception as e:
            logger.error(f"Error in imputation: {e}")
            return ImputationResult(
                success=False,
                imputed_observations=[],
                confidence_scores={},
                imputation_flags={},
                quality_metrics={},
                method_used=method,
                warnings=[str(e)],
                metadata={}
            )
    
    def _analyze_missing_patterns(
        self,
        data: pd.DataFrame,
        contexts: List[ConflictContext]
    ) -> Dict[str, Any]:
        """Analyze patterns in missing data to understand causes."""
        
        # Create conflict intensity map
        conflict_map = {}
        for context in contexts:
            key = (context.market_id, context.date)
            conflict_map[key] = context.conflict_intensity
        
        missing_analysis = {
            'total_cells': data.size,
            'missing_cells': data.isnull().sum().sum(),
            'missing_percentage': (data.isnull().sum().sum() / data.size) * 100,
            'missing_by_market': data.isnull().sum(axis=1).to_dict(),
            'missing_by_date': data.isnull().sum(axis=0).to_dict(),
            'pattern_classification': {}
        }
        
        # Analyze correlation between missing data and conflict
        if conflict_map:
            missing_positions = []
            conflict_intensities = []
            
            for market in data.index:
                for date in data.columns:
                    key = (str(market), pd.to_datetime(date))
                    if key in conflict_map:
                        missing_positions.append(1 if pd.isna(data.loc[market, date]) else 0)
                        conflict_intensities.append(conflict_map[key])
            
            if missing_positions and conflict_intensities:
                correlation = np.corrcoef(missing_positions, conflict_intensities)[0, 1]
                missing_analysis['conflict_correlation'] = correlation
                
                # Classify pattern based on correlation
                if correlation > 0.3:
                    missing_analysis['primary_pattern'] = MissingDataPattern.CONFLICT_RELATED
                elif correlation > 0.1:
                    missing_analysis['primary_pattern'] = MissingDataPattern.INFRASTRUCTURE_RELATED
                else:
                    missing_analysis['primary_pattern'] = MissingDataPattern.RANDOM_MISSING
        
        return missing_analysis
    
    async def _conflict_weighted_mean_imputation(self, request: ImputationRequest) -> None:
        """Impute using conflict-weighted means from similar contexts."""
        
        data = request.market_data
        contexts = {(ctx.market_id, ctx.date): ctx for ctx in request.conflict_data}
        
        # For each missing value, find similar contexts and compute weighted mean
        for market in data.index:
            for date in data.columns:
                if pd.isna(data.loc[market, date]):
                    
                    key = (str(market), pd.to_datetime(date))
                    current_context = contexts.get(key)
                    
                    if not current_context:
                        continue
                    
                    # Find similar contexts (same zone, similar conflict intensity)
                    similar_values = []
                    weights = []
                    
                    for other_market in data.index:
                        for other_date in data.columns:
                            other_key = (str(other_market), pd.to_datetime(other_date))
                            other_context = contexts.get(other_key)
                            
                            if (other_context and 
                                not pd.isna(data.loc[other_market, other_date]) and
                                other_context.zone == current_context.zone):
                                
                                # Calculate similarity weight
                                conflict_similarity = 1 - abs(
                                    current_context.conflict_intensity - other_context.conflict_intensity
                                )
                                
                                # Time proximity weight
                                time_diff = abs((current_context.date - other_context.date).days)
                                time_weight = np.exp(-time_diff / 30)  # 30-day decay
                                
                                # Combined weight
                                weight = conflict_similarity * time_weight
                                
                                if weight > 0.1:  # Minimum threshold
                                    similar_values.append(data.loc[other_market, other_date])
                                    weights.append(weight)
                    
                    # Compute weighted mean if we have similar values
                    if similar_values and weights:
                        weighted_mean = np.average(similar_values, weights=weights)
                        data.loc[market, date] = weighted_mean
                        
                        # Calculate confidence based on number of similar values and weights
                        confidence = min(0.9, (len(similar_values) / 10) * np.mean(weights))
                        request.imputation_confidence[(str(market), pd.to_datetime(date))] = confidence
                        
                        flags = ['conflict_weighted_mean']
                        if len(similar_values) < 3:
                            flags.append('low_sample_size')
                        if np.mean(weights) < 0.3:
                            flags.append('low_similarity')
                        
                        request.imputation_flags[(str(market), pd.to_datetime(date))] = flags
        
        request.imputed_data = data
    
    async def _spatial_interpolation_imputation(self, request: ImputationRequest) -> None:
        """Impute using spatial interpolation from nearby markets."""
        
        data = request.market_data
        contexts = {(ctx.market_id, ctx.date): ctx for ctx in request.conflict_data}
        
        # Group contexts by date to enable spatial interpolation
        for date in data.columns:
            date_contexts = []
            date_values = []
            missing_markets = []
            
            for market in data.index:
                key = (str(market), pd.to_datetime(date))
                context = contexts.get(key)
                
                if context:
                    if pd.isna(data.loc[market, date]):
                        missing_markets.append((market, context))
                    else:
                        date_contexts.append(context)
                        date_values.append(data.loc[market, date])
            
            # Interpolate for missing markets
            for market, missing_context in missing_markets:
                if len(date_values) >= 2:  # Need at least 2 points for interpolation
                    
                    # Calculate distances (simplified - would use actual geographic distance)
                    distances = []
                    values = []
                    
                    for i, context in enumerate(date_contexts):
                        # Use conflict intensity difference as proxy for "distance"
                        conflict_distance = abs(
                            missing_context.conflict_intensity - context.conflict_intensity
                        )
                        # Zone compatibility
                        zone_distance = 0 if missing_context.zone == context.zone else 0.5
                        
                        total_distance = conflict_distance + zone_distance
                        
                        if total_distance < 1.0:  # Within reasonable range
                            distances.append(total_distance)
                            values.append(date_values[i])
                    
                    if values and distances:
                        # Inverse distance weighting
                        weights = [1 / (d + 0.1) for d in distances]  # Add small constant to avoid division by zero
                        interpolated_value = np.average(values, weights=weights)
                        
                        data.loc[market, date] = interpolated_value
                        
                        # Confidence based on number of neighbors and distance
                        confidence = min(0.8, len(values) / 5) * (1 - np.mean(distances))
                        request.imputation_confidence[(str(market), pd.to_datetime(date))] = max(0.1, confidence)
                        
                        flags = ['spatial_interpolation']
                        if len(values) < 3:
                            flags.append('few_neighbors')
                        if np.mean(distances) > 0.5:
                            flags.append('distant_neighbors')
                        
                        request.imputation_flags[(str(market), pd.to_datetime(date))] = flags
        
        request.imputed_data = data
    
    async def _temporal_regression_imputation(self, request: ImputationRequest) -> None:
        """Impute using temporal regression models for each market."""
        
        data = request.market_data
        contexts = {(ctx.market_id, ctx.date): ctx for ctx in request.conflict_data}
        
        # For each market, build temporal regression model
        for market in data.index:
            market_series = data.loc[market, :]
            
            if market_series.isnull().all():
                continue  # Skip if no data for this market
            
            # Prepare features and targets
            dates = []
            values = []
            conflict_features = []
            
            for date in data.columns:
                key = (str(market), pd.to_datetime(date))
                context = contexts.get(key)
                
                if context and not pd.isna(market_series[date]):
                    dates.append(pd.to_datetime(date))
                    values.append(market_series[date])
                    conflict_features.append([
                        context.conflict_intensity,
                        context.infrastructure_damage,
                        context.trader_presence,
                        context.accessibility
                    ])
            
            if len(values) < 5:  # Need minimum data for regression
                continue
            
            # Build regression model
            try:
                # Convert dates to numeric (days since start)
                date_numeric = [(d - min(dates)).days for d in dates]
                
                # Combine temporal and conflict features
                X = np.column_stack([date_numeric, conflict_features])
                y = np.array(values)
                
                # Simple linear regression (could use more sophisticated models)
                from sklearn.linear_model import LinearRegression
                model = LinearRegression()
                model.fit(X, y)
                
                # Predict missing values
                for date in data.columns:
                    if pd.isna(market_series[date]):
                        key = (str(market), pd.to_datetime(date))
                        context = contexts.get(key)
                        
                        if context:
                            date_num = (pd.to_datetime(date) - min(dates)).days
                            features = np.array([[
                                date_num,
                                context.conflict_intensity,
                                context.infrastructure_damage,
                                context.trader_presence,
                                context.accessibility
                            ]])
                            
                            predicted_value = model.predict(features)[0]
                            
                            # Apply bounds check
                            if predicted_value > 0:  # Prices must be positive
                                data.loc[market, date] = predicted_value
                                
                                # Confidence based on model R²
                                r2 = model.score(X, y)
                                confidence = max(0.1, min(0.8, r2))
                                
                                request.imputation_confidence[(str(market), pd.to_datetime(date))] = confidence
                                
                                flags = ['temporal_regression']
                                if r2 < 0.5:
                                    flags.append('low_model_fit')
                                if len(values) < 10:
                                    flags.append('limited_training_data')
                                
                                request.imputation_flags[(str(market), pd.to_datetime(date))] = flags
                
            except Exception as e:
                logger.warning(f"Regression failed for market {market}: {e}")
                continue
        
        request.imputed_data = data
    
    async def _zone_specific_knn_imputation(self, request: ImputationRequest) -> None:
        """Impute using KNN within the same currency zone."""
        
        data = request.market_data
        contexts = {(ctx.market_id, ctx.date): ctx for ctx in request.conflict_data}
        
        # Group markets by zone
        zone_markets = {}
        for market in data.index:
            # Find zone for this market (use most common zone across time)
            market_zones = []
            for date in data.columns:
                key = (str(market), pd.to_datetime(date))
                context = contexts.get(key)
                if context:
                    market_zones.append(context.zone)
            
            if market_zones:
                # Use most common zone
                market_zone = max(set(market_zones), key=market_zones.count)
                if market_zone not in zone_markets:
                    zone_markets[market_zone] = []
                zone_markets[market_zone].append(market)
        
        # Apply KNN within each zone
        for zone, zone_market_list in zone_markets.items():
            if len(zone_market_list) < 3:  # Need minimum markets for KNN
                continue
            
            zone_data = data.loc[zone_market_list, :]
            
            if zone_data.isnull().all().all():  # Skip if no data
                continue
            
            # Apply KNN imputation
            try:
                knn_imputer = self._imputation_models[ImputationMethod.ZONE_SPECIFIC_KNN]
                imputed_zone_data = pd.DataFrame(
                    knn_imputer.fit_transform(zone_data),
                    index=zone_data.index,
                    columns=zone_data.columns
                )
                
                # Update original data with imputed values
                for market in zone_market_list:
                    for date in data.columns:
                        if pd.isna(data.loc[market, date]) and not pd.isna(imputed_zone_data.loc[market, date]):
                            data.loc[market, date] = imputed_zone_data.loc[market, date]
                            
                            # Confidence based on number of neighbors and zone consistency
                            confidence = min(0.7, len(zone_market_list) / 10)
                            request.imputation_confidence[(str(market), pd.to_datetime(date))] = confidence
                            
                            flags = ['zone_specific_knn', f'zone_{zone.value}']
                            if len(zone_market_list) < 5:
                                flags.append('small_zone_sample')
                            
                            request.imputation_flags[(str(market), pd.to_datetime(date))] = flags
                
            except Exception as e:
                logger.warning(f"KNN imputation failed for zone {zone}: {e}")
                continue
        
        request.imputed_data = data
    
    async def _conflict_intensity_model_imputation(self, request: ImputationRequest) -> None:
        """Impute using random forest model trained on conflict features."""
        
        data = request.market_data
        contexts = {(ctx.market_id, ctx.date): ctx for ctx in request.conflict_data}
        
        # Prepare training data
        training_features = []
        training_targets = []
        missing_keys = []
        missing_features = []
        
        for market in data.index:
            for date in data.columns:
                key = (str(market), pd.to_datetime(date))
                context = contexts.get(key)
                
                if context:
                    features = [
                        context.conflict_intensity,
                        context.infrastructure_damage,
                        context.trader_presence,
                        context.accessibility,
                        float(context.siege_status),
                        context.nearby_conflict_events,
                        context.days_since_last_report,
                        (pd.to_datetime(date) - pd.to_datetime(data.columns[0])).days,  # Time trend
                        hash(context.zone.value) % 1000 / 1000.0,  # Zone encoding
                        pd.to_datetime(date).month,  # Seasonal effects
                        pd.to_datetime(date).dayofweek  # Day of week
                    ]
                    
                    if not pd.isna(data.loc[market, date]):
                        training_features.append(features)
                        training_targets.append(data.loc[market, date])
                    else:
                        missing_keys.append((str(market), pd.to_datetime(date)))
                        missing_features.append(features)
        
        if len(training_features) < 20:  # Need minimum training data
            logger.warning("Insufficient training data for conflict intensity model")
            request.imputed_data = data
            return
        
        # Train random forest model
        try:
            X_train = np.array(training_features)
            y_train = np.array(training_targets)
            
            # Scale features
            X_train_scaled = self._scaler.fit_transform(X_train)
            
            # Train model
            rf_model = self._imputation_models[ImputationMethod.CONFLICT_INTENSITY_MODEL]
            rf_model.fit(X_train_scaled, y_train)
            
            # Predict missing values
            if missing_features:
                X_missing = np.array(missing_features)
                X_missing_scaled = self._scaler.transform(X_missing)
                
                predictions = rf_model.predict(X_missing_scaled)
                
                # Get prediction intervals for confidence
                tree_predictions = np.array([
                    tree.predict(X_missing_scaled) for tree in rf_model.estimators_
                ])
                prediction_std = np.std(tree_predictions, axis=0)
                
                for i, (market_str, date) in enumerate(missing_keys):
                    predicted_value = predictions[i]
                    prediction_uncertainty = prediction_std[i]
                    
                    if predicted_value > 0:  # Prices must be positive
                        # Find market index
                        market_idx = data.index[data.index.astype(str) == market_str][0]
                        data.loc[market_idx, date] = predicted_value
                        
                        # Confidence based on model performance and prediction uncertainty
                        base_confidence = rf_model.score(X_train_scaled, y_train)
                        uncertainty_penalty = min(0.5, prediction_uncertainty / np.mean(y_train))
                        confidence = max(0.1, base_confidence - uncertainty_penalty)
                        
                        request.imputation_confidence[(market_str, date)] = confidence
                        
                        flags = ['conflict_intensity_model']
                        if prediction_uncertainty > np.std(y_train) * 0.5:
                            flags.append('high_uncertainty')
                        if base_confidence < 0.6:
                            flags.append('low_model_performance')
                        
                        request.imputation_flags[(market_str, date)] = flags
            
        except Exception as e:
            logger.error(f"Conflict intensity model failed: {e}")
            # Fall back to simpler method
            await self._conflict_weighted_mean_imputation(request)
            return
        
        request.imputed_data = data
    
    async def _cross_commodity_imputation(self, request: ImputationRequest) -> None:
        """Impute using price relationships with other commodities."""
        # This would require additional commodity data
        # For now, fall back to conflict-weighted mean
        await self._conflict_weighted_mean_imputation(request)
    
    async def _survivor_bias_correction_imputation(self, request: ImputationRequest) -> None:
        """Impute with correction for trader survivor bias."""
        # Apply base imputation first
        await self._conflict_intensity_model_imputation(request)
        
        # Then apply survivor bias correction
        await self._apply_survivor_bias_correction(request)
    
    async def _apply_survivor_bias_correction(self, request: ImputationRequest) -> None:
        """Apply survivor bias correction to imputed values."""
        
        data = request.imputed_data or request.market_data
        contexts = {(ctx.market_id, ctx.date): ctx for ctx in request.conflict_data}
        
        # Identify high-conflict periods where survivor bias is likely
        for market in data.index:
            for date in data.columns:
                key = (str(market), pd.to_datetime(date))
                context = contexts.get(key)
                
                if context and context.conflict_intensity > 0.7:  # High conflict
                    current_value = data.loc[market, date]
                    
                    if not pd.isna(current_value):
                        # Apply upward adjustment for survivor bias
                        # Traders who remain in high-conflict areas may have higher costs
                        bias_multiplier = 1 + (context.conflict_intensity - 0.7) * 0.2  # Up to 6% increase
                        
                        corrected_value = current_value * bias_multiplier
                        data.loc[market, date] = corrected_value
                        
                        # Update or add flags
                        key_tuple = (str(market), pd.to_datetime(date))
                        existing_flags = request.imputation_flags.get(key_tuple, [])
                        existing_flags.append('survivor_bias_corrected')
                        request.imputation_flags[key_tuple] = existing_flags
                        
                        # Reduce confidence slightly due to adjustment
                        existing_confidence = request.imputation_confidence.get(key_tuple, 0.5)
                        request.imputation_confidence[key_tuple] = existing_confidence * 0.9
        
        request.imputed_data = data
    
    def _validate_imputation_results(self, request: ImputationRequest) -> Dict[str, Any]:
        """Validate the quality of imputation results."""
        
        original_data = request.market_data
        imputed_data = request.imputed_data
        
        if imputed_data is None:
            return {'validation_failed': True}
        
        # Calculate basic metrics
        original_missing = original_data.isnull().sum().sum()
        imputed_missing = imputed_data.isnull().sum().sum()
        imputed_count = original_missing - imputed_missing
        
        validation_metrics = {
            'original_missing_count': original_missing,
            'remaining_missing_count': imputed_missing,
            'imputed_count': imputed_count,
            'imputation_rate': (imputed_count / original_missing * 100) if original_missing > 0 else 0,
            'avg_confidence': np.mean(list(request.imputation_confidence.values())) if request.imputation_confidence else 0,
            'low_confidence_count': sum(1 for conf in request.imputation_confidence.values() if conf < self.min_confidence_threshold)
        }
        
        # Validate imputed values are reasonable
        if imputed_count > 0:
            # Check for negative prices
            negative_prices = (imputed_data < 0).sum().sum()
            validation_metrics['negative_prices'] = negative_prices
            
            # Check for extreme values
            if not original_data.dropna().empty:
                original_mean = original_data.mean().mean()
                original_std = original_data.std().mean()
                
                extreme_count = 0
                for market in imputed_data.index:
                    for date in imputed_data.columns:
                        if (pd.isna(original_data.loc[market, date]) and 
                            not pd.isna(imputed_data.loc[market, date])):
                            
                            value = imputed_data.loc[market, date]
                            z_score = abs(value - original_mean) / original_std if original_std > 0 else 0
                            
                            if z_score > 3:  # More than 3 standard deviations
                                extreme_count += 1
                
                validation_metrics['extreme_values'] = extreme_count
                validation_metrics['extreme_value_rate'] = (extreme_count / imputed_count * 100) if imputed_count > 0 else 0
        
        return validation_metrics
    
    def _convert_to_price_observations(self, request: ImputationRequest) -> List[PriceObservation]:
        """Convert imputed data back to PriceObservation objects."""
        
        observations = []
        imputed_data = request.imputed_data
        
        if imputed_data is None:
            return observations
        
        # Convert imputed values to observations
        for market in imputed_data.index:
            for date in imputed_data.columns:
                # Check if this was an imputed value
                key = (str(market), pd.to_datetime(date))
                if key in request.imputation_confidence:
                    
                    price_value = imputed_data.loc[market, date]
                    if not pd.isna(price_value) and price_value > 0:
                        
                        # Create price observation
                        from ...core.domain.market.value_objects import MarketId
                        
                        obs = PriceObservation(
                            market_id=MarketId(str(market)),
                            commodity=Commodity(name=request.commodity),
                            price=Price(
                                amount=Decimal(str(price_value)),
                                currency=Currency.USD  # Assuming USD after conversion
                            ),
                            observed_date=pd.to_datetime(date),
                            source="conflict_aware_imputation",
                            quality="imputed",
                            observations_count=1
                        )
                        
                        observations.append(obs)
        
        return observations
    
    def get_imputation_summary(
        self,
        results: List[ImputationResult]
    ) -> Dict[str, Any]:
        """Generate summary statistics for multiple imputation results."""
        
        if not results:
            return {'no_results': True}
        
        total_imputed = sum(len(result.imputed_observations) for result in results)
        successful_results = [r for r in results if r.success]
        
        summary = {
            'total_imputation_requests': len(results),
            'successful_requests': len(successful_results),
            'success_rate': len(successful_results) / len(results) * 100,
            'total_observations_imputed': total_imputed,
            'methods_used': list(set(result.method_used.value for result in results)),
            'avg_confidence': np.mean([
                np.mean(list(result.confidence_scores.values())) 
                for result in successful_results 
                if result.confidence_scores
            ]) if successful_results else 0
        }
        
        # Flag distribution
        all_flags = []
        for result in successful_results:
            for flags_list in result.imputation_flags.values():
                all_flags.extend(flags_list)
        
        if all_flags:
            flag_counts = {}
            for flag in all_flags:
                flag_counts[flag] = flag_counts.get(flag, 0) + 1
            
            summary['common_flags'] = sorted(flag_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return summary