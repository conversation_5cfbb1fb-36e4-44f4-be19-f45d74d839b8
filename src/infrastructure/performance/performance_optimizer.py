"""
Performance testing and optimization framework.

Provides tools for benchmarking, profiling, and optimizing
the econometric models and data processing pipelines.
"""

import time
import psutil
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Callable, Any
from dataclasses import dataclass
from datetime import datetime
import warnings
from functools import wraps
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import cProfile
import pstats
import io
from memory_profiler import profile as memory_profile

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """Container for performance measurement results."""
    operation_name: str
    execution_time: float  # seconds
    memory_usage: float  # MB
    cpu_usage: float  # percentage
    throughput: Optional[float] = None  # operations per second
    latency_p50: Optional[float] = None
    latency_p95: Optional[float] = None
    latency_p99: Optional[float] = None


@dataclass
class OptimizationResult:
    """Result of an optimization attempt."""
    optimization_name: str
    original_time: float
    optimized_time: float
    speedup_factor: float
    memory_reduction: float
    implementation_notes: str
    code_changes: Optional[Dict[str, str]] = None


@dataclass
class BenchmarkReport:
    """Comprehensive benchmark report."""
    timestamp: datetime
    system_info: Dict[str, Any]
    performance_metrics: List[PerformanceMetrics]
    optimization_results: List[OptimizationResult]
    bottlenecks: List[str]
    recommendations: List[str]


class PerformanceOptimizer:
    """
    Framework for performance testing and optimization.
    
    Provides tools to:
    - Benchmark model operations
    - Profile memory and CPU usage
    - Identify bottlenecks
    - Test optimization strategies
    - Generate performance reports
    """
    
    def __init__(self):
        """Initialize performance optimizer."""
        self.metrics_history = []
        self.optimization_history = []
        self.profiler = cProfile.Profile()
        
    def measure_performance(self, func: Callable, *args, 
                          operation_name: str = "unnamed",
                          n_iterations: int = 1,
                          **kwargs) -> PerformanceMetrics:
        """
        Measure performance of a function.
        
        Args:
            func: Function to benchmark
            *args: Arguments for the function
            operation_name: Name for the operation
            n_iterations: Number of iterations for averaging
            **kwargs: Keyword arguments for the function
            
        Returns:
            Performance metrics
        """
        logger.info(f"Measuring performance for: {operation_name}")
        
        # Measure execution time
        execution_times = []
        memory_usages = []
        cpu_usages = []
        
        for i in range(n_iterations):
            # Memory before
            process = psutil.Process()
            mem_before = process.memory_info().rss / 1024 / 1024  # MB
            
            # CPU before
            cpu_before = process.cpu_percent(interval=0.1)
            
            # Time execution
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            # Memory after
            mem_after = process.memory_info().rss / 1024 / 1024  # MB
            
            # CPU after
            cpu_after = process.cpu_percent(interval=0.1)
            
            execution_times.append(end_time - start_time)
            memory_usages.append(mem_after - mem_before)
            cpu_usages.append((cpu_before + cpu_after) / 2)
        
        # Calculate statistics
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            execution_time=np.mean(execution_times),
            memory_usage=np.mean(memory_usages),
            cpu_usage=np.mean(cpu_usages),
            throughput=1.0 / np.mean(execution_times) if execution_times else None,
            latency_p50=np.percentile(execution_times, 50),
            latency_p95=np.percentile(execution_times, 95),
            latency_p99=np.percentile(execution_times, 99)
        )
        
        self.metrics_history.append(metrics)
        logger.info(f"Performance: {metrics.execution_time:.3f}s, "
                   f"Memory: {metrics.memory_usage:.1f}MB")
        
        return metrics
    
    def profile_function(self, func: Callable, *args, **kwargs) -> str:
        """
        Profile a function to identify bottlenecks.
        
        Args:
            func: Function to profile
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Profile report as string
        """
        # Enable profiler
        self.profiler.enable()
        
        # Run function
        func(*args, **kwargs)
        
        # Disable profiler
        self.profiler.disable()
        
        # Generate report
        s = io.StringIO()
        ps = pstats.Stats(self.profiler, stream=s).sort_stats('cumulative')
        ps.print_stats(20)  # Top 20 functions
        
        return s.getvalue()
    
    def optimize_pandas_operations(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Test pandas optimization strategies.
        
        Args:
            df: DataFrame to optimize operations on
            
        Returns:
            Optimization results
        """
        results = {}
        
        # Test 1: Vectorization vs loops
        logger.info("Testing vectorization vs loops...")
        
        # Loop version
        def loop_calculation(df):
            results = []
            for idx, row in df.iterrows():
                results.append(row['A'] * row['B'] + row['C'])
            return results
        
        # Vectorized version
        def vectorized_calculation(df):
            return df['A'] * df['B'] + df['C']
        
        # Create test data
        test_df = pd.DataFrame({
            'A': np.random.randn(10000),
            'B': np.random.randn(10000),
            'C': np.random.randn(10000)
        })
        
        loop_metrics = self.measure_performance(
            loop_calculation, test_df,
            operation_name="Loop calculation"
        )
        
        vector_metrics = self.measure_performance(
            vectorized_calculation, test_df,
            operation_name="Vectorized calculation"
        )
        
        results['vectorization'] = OptimizationResult(
            optimization_name="Pandas Vectorization",
            original_time=loop_metrics.execution_time,
            optimized_time=vector_metrics.execution_time,
            speedup_factor=loop_metrics.execution_time / vector_metrics.execution_time,
            memory_reduction=loop_metrics.memory_usage - vector_metrics.memory_usage,
            implementation_notes="Use pandas vectorized operations instead of loops"
        )
        
        # Test 2: Query optimization
        logger.info("Testing query optimization...")
        
        # Chained filtering
        def chained_filter(df):
            return df[df['A'] > 0][df['B'] < 0][df['C'] > 0]
        
        # Single query
        def single_query(df):
            return df.query('A > 0 & B < 0 & C > 0')
        
        chained_metrics = self.measure_performance(
            chained_filter, test_df,
            operation_name="Chained filtering"
        )
        
        query_metrics = self.measure_performance(
            single_query, test_df,
            operation_name="Query filtering"
        )
        
        results['query_optimization'] = OptimizationResult(
            optimization_name="Query Optimization",
            original_time=chained_metrics.execution_time,
            optimized_time=query_metrics.execution_time,
            speedup_factor=chained_metrics.execution_time / query_metrics.execution_time,
            memory_reduction=chained_metrics.memory_usage - query_metrics.memory_usage,
            implementation_notes="Use df.query() for complex filtering"
        )
        
        return results
    
    def optimize_numpy_operations(self, size: int = 10000) -> Dict[str, Any]:
        """
        Test numpy optimization strategies.
        
        Args:
            size: Size of arrays to test
            
        Returns:
            Optimization results
        """
        results = {}
        
        # Test 1: Broadcasting vs loops
        logger.info("Testing numpy broadcasting...")
        
        A = np.random.randn(size, 100)
        B = np.random.randn(100)
        
        # Loop version
        def loop_multiply(A, B):
            result = np.zeros_like(A)
            for i in range(A.shape[0]):
                for j in range(A.shape[1]):
                    result[i, j] = A[i, j] * B[j]
            return result
        
        # Broadcasting version
        def broadcast_multiply(A, B):
            return A * B
        
        loop_metrics = self.measure_performance(
            loop_multiply, A, B,
            operation_name="Loop multiplication"
        )
        
        broadcast_metrics = self.measure_performance(
            broadcast_multiply, A, B,
            operation_name="Broadcast multiplication"
        )
        
        results['broadcasting'] = OptimizationResult(
            optimization_name="Numpy Broadcasting",
            original_time=loop_metrics.execution_time,
            optimized_time=broadcast_metrics.execution_time,
            speedup_factor=loop_metrics.execution_time / broadcast_metrics.execution_time,
            memory_reduction=0,
            implementation_notes="Use numpy broadcasting instead of explicit loops"
        )
        
        # Test 2: Einsum optimization
        logger.info("Testing einsum optimization...")
        
        C = np.random.randn(50, 100)
        D = np.random.randn(100, 50)
        
        # Traditional matrix multiplication
        def traditional_matmul(C, D):
            return np.dot(C, D)
        
        # Einsum version
        def einsum_matmul(C, D):
            return np.einsum('ij,jk->ik', C, D)
        
        trad_metrics = self.measure_performance(
            traditional_matmul, C, D,
            operation_name="Traditional matmul"
        )
        
        einsum_metrics = self.measure_performance(
            einsum_matmul, C, D,
            operation_name="Einsum matmul"
        )
        
        results['einsum'] = OptimizationResult(
            optimization_name="Einsum Optimization",
            original_time=trad_metrics.execution_time,
            optimized_time=einsum_metrics.execution_time,
            speedup_factor=trad_metrics.execution_time / einsum_metrics.execution_time,
            memory_reduction=0,
            implementation_notes="Consider einsum for complex tensor operations"
        )
        
        return results
    
    def test_parallelization(self, func: Callable, data_list: List[Any],
                           max_workers: int = 4) -> Dict[str, Any]:
        """
        Test parallelization strategies.
        
        Args:
            func: Function to parallelize
            data_list: List of data to process
            max_workers: Maximum number of workers
            
        Returns:
            Parallelization results
        """
        results = {}
        
        # Sequential processing
        logger.info("Testing sequential processing...")
        seq_start = time.time()
        seq_results = [func(data) for data in data_list]
        seq_time = time.time() - seq_start
        
        # Thread pool
        logger.info("Testing thread pool...")
        thread_start = time.time()
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            thread_results = list(executor.map(func, data_list))
        thread_time = time.time() - thread_start
        
        results['threading'] = OptimizationResult(
            optimization_name="Thread Pool Parallelization",
            original_time=seq_time,
            optimized_time=thread_time,
            speedup_factor=seq_time / thread_time,
            memory_reduction=0,
            implementation_notes=f"ThreadPoolExecutor with {max_workers} workers"
        )
        
        # Process pool (for CPU-bound tasks)
        logger.info("Testing process pool...")
        process_start = time.time()
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            process_results = list(executor.map(func, data_list))
        process_time = time.time() - process_start
        
        results['multiprocessing'] = OptimizationResult(
            optimization_name="Process Pool Parallelization",
            original_time=seq_time,
            optimized_time=process_time,
            speedup_factor=seq_time / process_time,
            memory_reduction=0,
            implementation_notes=f"ProcessPoolExecutor with {max_workers} workers"
        )
        
        return results
    
    def optimize_model_inference(self, model: Any, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Optimize model inference operations.
        
        Args:
            model: Model to optimize
            data: Data for inference
            
        Returns:
            Optimization results
        """
        results = {}
        
        # Test batch size optimization
        logger.info("Testing batch size optimization...")
        
        batch_sizes = [1, 10, 50, 100, 500]
        batch_times = []
        
        for batch_size in batch_sizes:
            def batch_predict():
                predictions = []
                for i in range(0, len(data), batch_size):
                    batch = data.iloc[i:i+batch_size]
                    pred = model.predict(batch)
                    predictions.extend(pred)
                return predictions
            
            metrics = self.measure_performance(
                batch_predict,
                operation_name=f"Batch size {batch_size}"
            )
            batch_times.append(metrics.execution_time)
        
        optimal_batch_idx = np.argmin(batch_times)
        optimal_batch_size = batch_sizes[optimal_batch_idx]
        
        results['batch_optimization'] = OptimizationResult(
            optimization_name="Batch Size Optimization",
            original_time=batch_times[0],  # Single sample
            optimized_time=batch_times[optimal_batch_idx],
            speedup_factor=batch_times[0] / batch_times[optimal_batch_idx],
            memory_reduction=0,
            implementation_notes=f"Optimal batch size: {optimal_batch_size}"
        )
        
        return results
    
    def identify_bottlenecks(self, profile_data: str) -> List[str]:
        """
        Identify performance bottlenecks from profile data.
        
        Args:
            profile_data: Profile output string
            
        Returns:
            List of identified bottlenecks
        """
        bottlenecks = []
        
        # Parse profile data (simplified)
        lines = profile_data.split('\n')
        for line in lines:
            if 'cumtime' in line or '%' in line:
                # Look for functions taking >10% of time
                if any(x in line for x in ['pandas', 'numpy', 'sklearn']):
                    bottlenecks.append(f"Heavy computation in: {line.strip()}")
        
        # Add specific bottleneck patterns
        if any('iterrows' in line for line in lines):
            bottlenecks.append("DataFrame iteration detected - consider vectorization")
        
        if any('append' in line for line in lines):
            bottlenecks.append("List append in loop - consider pre-allocation")
        
        return bottlenecks
    
    def generate_recommendations(self, metrics: List[PerformanceMetrics],
                               optimizations: List[OptimizationResult]) -> List[str]:
        """
        Generate optimization recommendations.
        
        Args:
            metrics: Performance metrics
            optimizations: Optimization results
            
        Returns:
            List of recommendations
        """
        recommendations = []
        
        # Analyze metrics
        slow_operations = [m for m in metrics if m.execution_time > 1.0]
        if slow_operations:
            recommendations.append(
                f"Consider optimizing these slow operations: "
                f"{', '.join(op.operation_name for op in slow_operations)}"
            )
        
        memory_heavy = [m for m in metrics if m.memory_usage > 100]
        if memory_heavy:
            recommendations.append(
                "High memory usage detected - consider chunking or streaming processing"
            )
        
        # Analyze optimizations
        high_speedup = [o for o in optimizations if o.speedup_factor > 5]
        if high_speedup:
            for opt in high_speedup:
                recommendations.append(
                    f"Apply {opt.optimization_name}: {opt.speedup_factor:.1f}x speedup"
                )
        
        # General recommendations
        recommendations.extend([
            "Use numpy/pandas vectorized operations instead of loops",
            "Consider caching frequently computed results",
            "Profile regularly to catch performance regressions",
            "Use appropriate data types (e.g., categorical for repeated strings)"
        ])
        
        return recommendations
    
    def generate_benchmark_report(self) -> BenchmarkReport:
        """Generate comprehensive benchmark report."""
        # Get system info
        system_info = {
            'cpu_count': psutil.cpu_count(),
            'cpu_freq': psutil.cpu_freq().current if psutil.cpu_freq() else 'N/A',
            'memory_total': psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
            'python_version': import sys; sys.version,
            'platform': import platform; platform.platform()
        }
        
        # Identify bottlenecks
        bottlenecks = []
        if hasattr(self, 'last_profile'):
            bottlenecks = self.identify_bottlenecks(self.last_profile)
        
        # Generate recommendations
        recommendations = self.generate_recommendations(
            self.metrics_history,
            self.optimization_history
        )
        
        report = BenchmarkReport(
            timestamp=datetime.now(),
            system_info=system_info,
            performance_metrics=self.metrics_history,
            optimization_results=self.optimization_history,
            bottlenecks=bottlenecks,
            recommendations=recommendations
        )
        
        return report
    
    def export_report(self, report: BenchmarkReport, filepath: str):
        """Export benchmark report to file."""
        with open(filepath, 'w') as f:
            f.write(f"PERFORMANCE BENCHMARK REPORT\n")
            f.write(f"Generated: {report.timestamp}\n")
            f.write(f"\nSYSTEM INFO:\n")
            for key, value in report.system_info.items():
                f.write(f"  {key}: {value}\n")
            
            f.write(f"\nPERFORMANCE METRICS:\n")
            for metric in report.performance_metrics:
                f.write(f"\n  {metric.operation_name}:\n")
                f.write(f"    Execution Time: {metric.execution_time:.3f}s\n")
                f.write(f"    Memory Usage: {metric.memory_usage:.1f}MB\n")
                f.write(f"    CPU Usage: {metric.cpu_usage:.1f}%\n")
            
            f.write(f"\nOPTIMIZATION RESULTS:\n")
            for opt in report.optimization_results:
                f.write(f"\n  {opt.optimization_name}:\n")
                f.write(f"    Speedup: {opt.speedup_factor:.1f}x\n")
                f.write(f"    Notes: {opt.implementation_notes}\n")
            
            f.write(f"\nBOTTLENECKS:\n")
            for bottleneck in report.bottlenecks:
                f.write(f"  - {bottleneck}\n")
            
            f.write(f"\nRECOMMENDATIONS:\n")
            for rec in report.recommendations:
                f.write(f"  - {rec}\n")


def performance_monitor(threshold_ms: float = 1000):
    """
    Decorator to monitor function performance.
    
    Args:
        threshold_ms: Log warning if execution exceeds this threshold
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = (time.time() - start_time) * 1000  # ms
            
            if execution_time > threshold_ms:
                logger.warning(
                    f"{func.__name__} took {execution_time:.1f}ms "
                    f"(threshold: {threshold_ms}ms)"
                )
            
            return result
        return wrapper
    return decorator