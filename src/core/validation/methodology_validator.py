"""
Methodology Validator

Automated checks to ensure scientific integrity across all research outputs.
Prevents predetermined language and enforces template standards.
"""

import re
import os
from typing import List, Dict, Set, Optional
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

class ViolationType(Enum):
    """Types of methodology violations."""
    PREDETERMINED_LANGUAGE = "predetermined_language"
    MISSING_CURRENCY_CHECK = "missing_currency_check"
    PREDETERMINED_RESULTS = "predetermined_results"
    MISSING_UNCERTAINTY = "missing_uncertainty"
    CAUSAL_CLAIMS = "causal_claims"


@dataclass
class Violation:
    """Represents a methodology violation."""
    file_path: str
    line_number: int
    violation_type: ViolationType
    description: str
    suggested_fix: str
    severity: str  # "error", "warning", "info"


class MethodologyValidator:
    """Validates research outputs for scientific integrity."""
    
    # Forbidden phrases that suggest predetermined conclusions
    PREDETERMINED_PHRASES = {
        "revolutionary discovery",
        "game-changing", 
        "paradigm shift",
        "groundbreaking",
        "breakthrough",
        "validates our theory",
        "confirms our hypothesis",
        "proves that",
        "establishes that",
        "demonstrates conclusively",
        "as expected",
        "obviously",
        "clearly shows",
        "undoubtedly",
        "without question",
        "yemen paradox solved",
        "key discovery"
    }
    
    # Phrases requiring careful scrutiny for causal claims
    CAUSAL_PHRASES = {
        "causes",
        "leads to",
        "results in",
        "drives",
        "determines",
        "influences",
        "affects",
        "impacts"
    }
    
    # Required currency verification patterns
    CURRENCY_PATTERNS = [
        r"currency.{0,20}conversion",
        r"exchange.{0,20}rate.{0,20}check",
        r"USD.{0,20}YER",
        r"price.{0,20}currency"
    ]
    
    # Uncertainty indicators (should be present)
    UNCERTAINTY_PATTERNS = [
        r"confidence interval",
        r"p.{0,5}value",
        r"standard error",
        r"may.{0,10}be",
        r"suggests",
        r"indicates",
        r"appears",
        r"might",
        r"could"
    ]

    def __init__(self, exclude_directories: Optional[List[str]] = None):
        """Initialize validator.
        
        Args:
            exclude_directories: Directories to skip (e.g., archives)
        """
        self.exclude_directories = exclude_directories or [
            "archive", "examples", "test", "__pycache__"
        ]
        self.violations: List[Violation] = []
    
    def validate_directory(self, directory_path: str) -> List[Violation]:
        """Validate all markdown files in directory.
        
        Args:
            directory_path: Path to directory to validate
            
        Returns:
            List of violations found
        """
        self.violations = []
        
        for root, dirs, files in os.walk(directory_path):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in self.exclude_directories]
            
            for file in files:
                if file.endswith('.md'):
                    file_path = os.path.join(root, file)
                    self._validate_file(file_path)
        
        return self.violations
    
    def validate_file(self, file_path: str) -> List[Violation]:
        """Validate single file.
        
        Args:
            file_path: Path to file to validate
            
        Returns:
            List of violations in this file
        """
        self.violations = []
        self._validate_file(file_path)
        return self.violations
    
    def _validate_file(self, file_path: str):
        """Internal file validation."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                self._check_predetermined_language(file_path, line_num, line)
                self._check_causal_claims(file_path, line_num, line)
                
            # Check whole file patterns
            content = ''.join(lines)
            self._check_currency_verification(file_path, content)
            self._check_uncertainty_presence(file_path, content)
            
        except Exception as e:
            self.violations.append(Violation(
                file_path=file_path,
                line_number=0,
                violation_type=ViolationType.PREDETERMINED_LANGUAGE,
                description=f"Error reading file: {e}",
                suggested_fix="Check file encoding and permissions",
                severity="error"
            ))
    
    def _check_predetermined_language(self, file_path: str, line_num: int, line: str):
        """Check for predetermined language."""
        line_lower = line.lower()
        
        for phrase in self.PREDETERMINED_PHRASES:
            if phrase in line_lower:
                self.violations.append(Violation(
                    file_path=file_path,
                    line_number=line_num,
                    violation_type=ViolationType.PREDETERMINED_LANGUAGE,
                    description=f"Predetermined language detected: '{phrase}'",
                    suggested_fix=f"Replace with objective language or placeholder like '[TO BE DETERMINED]'",
                    severity="error"
                ))
    
    def _check_causal_claims(self, file_path: str, line_num: int, line: str):
        """Check for unsubstantiated causal claims."""
        line_lower = line.lower()
        
        # Skip if line contains uncertainty language
        has_uncertainty = any(pattern in line_lower for pattern in [
            "may", "might", "could", "appears", "suggests", "indicates"
        ])
        
        if has_uncertainty:
            return
        
        for phrase in self.CAUSAL_PHRASES:
            if phrase in line_lower:
                # Check if preceded by evidence indicators
                evidence_indicators = ["evidence suggests", "analysis shows", "results indicate"]
                has_evidence = any(indicator in line_lower for indicator in evidence_indicators)
                
                if not has_evidence:
                    self.violations.append(Violation(
                        file_path=file_path,
                        line_number=line_num,
                        violation_type=ViolationType.CAUSAL_CLAIMS,
                        description=f"Potential unsupported causal claim: '{phrase}'",
                        suggested_fix="Add uncertainty language ('suggests', 'may', etc.) or provide evidence",
                        severity="warning"
                    ))
    
    def _check_currency_verification(self, file_path: str, content: str):
        """Check for currency conversion verification in price analysis files."""
        content_lower = content.lower()
        
        # Check if file deals with prices
        has_price_analysis = any(term in content_lower for term in [
            "price", "cost", "exchange rate", "yer", "usd"
        ])
        
        if not has_price_analysis:
            return
        
        # Check for currency verification
        has_currency_check = any(
            re.search(pattern, content_lower) 
            for pattern in self.CURRENCY_PATTERNS
        )
        
        if not has_currency_check:
            self.violations.append(Violation(
                file_path=file_path,
                line_number=0,
                violation_type=ViolationType.MISSING_CURRENCY_CHECK,
                description="Price analysis file missing currency conversion verification",
                suggested_fix="Add currency conversion checklist from template",
                severity="error"
            ))
    
    def _check_uncertainty_presence(self, file_path: str, content: str):
        """Check for appropriate uncertainty language in results files."""
        content_lower = content.lower()
        
        # Check if file contains results
        has_results = any(term in content_lower for term in [
            "result", "finding", "conclusion", "significant", "effect"
        ])
        
        if not has_results:
            return
        
        # Check for uncertainty indicators
        has_uncertainty = any(
            re.search(pattern, content_lower)
            for pattern in self.UNCERTAINTY_PATTERNS
        )
        
        if not has_uncertainty:
            self.violations.append(Violation(
                file_path=file_path,
                line_number=0,
                violation_type=ViolationType.MISSING_UNCERTAINTY,
                description="Results file missing uncertainty quantification",
                suggested_fix="Add confidence intervals, p-values, or uncertainty language",
                severity="warning"
            ))
    
    def generate_report(self, output_path: Optional[str] = None) -> str:
        """Generate validation report.
        
        Args:
            output_path: Optional path to save report
            
        Returns:
            Report as string
        """
        if not self.violations:
            report = "✅ No methodology violations found!\n"
            return report
        
        # Group violations by type
        by_type = {}
        for violation in self.violations:
            vtype = violation.violation_type.value
            if vtype not in by_type:
                by_type[vtype] = []
            by_type[vtype].append(violation)
        
        report = f"# Methodology Validation Report\n\n"
        report += f"**Total Violations**: {len(self.violations)}\n\n"
        
        for vtype, violations in by_type.items():
            report += f"## {vtype.replace('_', ' ').title()} ({len(violations)} violations)\n\n"
            
            for violation in violations:
                report += f"**File**: {violation.file_path}\n"
                if violation.line_number > 0:
                    report += f"**Line**: {violation.line_number}\n"
                report += f"**Issue**: {violation.description}\n"
                report += f"**Fix**: {violation.suggested_fix}\n"
                report += f"**Severity**: {violation.severity}\n\n"
        
        if output_path:
            with open(output_path, 'w') as f:
                f.write(report)
        
        return report
    
    def has_errors(self) -> bool:
        """Check if any error-level violations found."""
        return any(v.severity == "error" for v in self.violations)
    
    def has_warnings(self) -> bool:
        """Check if any warning-level violations found."""
        return any(v.severity == "warning" for v in self.violations)


class TemplateEnforcer:
    """Enforces template usage standards."""
    
    REQUIRED_TEMPLATE_ELEMENTS = {
        "currency_checklist": [
            "currency conversion",
            "exchange rate", 
            "USD",
            "YER"
        ],
        "placeholders": [
            "[TO BE DETERMINED]",
            "[TO BE CALCULATED]", 
            "[TO BE TESTED]",
            "[SPECIFY"
        ],
        "decision_rules": [
            "IF",
            "THEN", 
            "ELSE",
            "decision"
        ]
    }
    
    def validate_template_compliance(self, file_path: str) -> List[Violation]:
        """Check if file follows template standards."""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
            
            # Check for required elements in template files
            if "template" in file_path.lower():
                for element_type, keywords in self.REQUIRED_TEMPLATE_ELEMENTS.items():
                    has_element = any(keyword in content for keyword in keywords)
                    
                    if not has_element:
                        violations.append(Violation(
                            file_path=file_path,
                            line_number=0,
                            violation_type=ViolationType.PREDETERMINED_RESULTS,
                            description=f"Template missing required element: {element_type}",
                            suggested_fix=f"Add {element_type} following template standards",
                            severity="warning"
                        ))
        
        except Exception as e:
            violations.append(Violation(
                file_path=file_path,
                line_number=0,
                violation_type=ViolationType.PREDETERMINED_RESULTS,
                description=f"Error validating template: {e}",
                suggested_fix="Check file accessibility",
                severity="error"
            ))
        
        return violations


def validate_methodology_package(package_path: str) -> Dict[str, any]:
    """Validate entire methodology package.
    
    Args:
        package_path: Path to methodology package
        
    Returns:
        Validation results summary
    """
    validator = MethodologyValidator()
    template_enforcer = TemplateEnforcer()
    
    # Run main validation
    violations = validator.validate_directory(package_path)
    
    # Run template enforcement
    templates_path = os.path.join(package_path, "07-results-templates")
    if os.path.exists(templates_path):
        for root, dirs, files in os.walk(templates_path):
            for file in files:
                if file.endswith('.md'):
                    file_path = os.path.join(root, file)
                    template_violations = template_enforcer.validate_template_compliance(file_path)
                    violations.extend(template_violations)
    
    # Generate summary
    summary = {
        'total_violations': len(violations),
        'errors': len([v for v in violations if v.severity == "error"]),
        'warnings': len([v for v in violations if v.severity == "warning"]),
        'violations_by_type': {},
        'detailed_report': validator.generate_report()
    }
    
    # Group by type
    for violation in violations:
        vtype = violation.violation_type.value
        if vtype not in summary['violations_by_type']:
            summary['violations_by_type'][vtype] = 0
        summary['violations_by_type'][vtype] += 1
    
    return summary


# CLI interface
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate methodology package for scientific integrity")
    parser.add_argument("path", help="Path to validate")
    parser.add_argument("--output", help="Output report path")
    parser.add_argument("--strict", action="store_true", help="Fail on warnings")
    
    args = parser.parse_args()
    
    if os.path.isfile(args.path):
        validator = MethodologyValidator()
        violations = validator.validate_file(args.path)
    else:
        results = validate_methodology_package(args.path)
        violations = results['total_violations']
        print(results['detailed_report'])
    
    if args.output:
        validator.generate_report(args.output)
    
    # Exit codes
    if validator.has_errors():
        print(f"❌ {len([v for v in validator.violations if v.severity == 'error'])} errors found")
        exit(1)
    elif validator.has_warnings() and args.strict:
        print(f"⚠️ {len([v for v in validator.violations if v.severity == 'warning'])} warnings found (strict mode)")
        exit(1)
    else:
        print("✅ Validation passed")
        exit(0)