"""
Bayesian Panel Regression with Informative Priors

Implements Bayesian panel data models with priors informed by conflict economics
literature. Provides robust uncertainty quantification for policy decisions.

Key features:
- Informative priors from cross-country conflict studies
- Hierarchical structure for market heterogeneity
- Policy-friendly uncertainty communication
- MCMC sampling with convergence diagnostics
"""

import numpy as np
import pandas as pd
import pymc as pm
import arviz as az
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
import json

from src.core.utils.logging import get_logger


logger = get_logger(__name__)


@dataclass
class PriorSpecification:
    """Specification for a prior distribution."""
    distribution: str  # 'normal', 'student_t', 'uniform', etc.
    params: Dict[str, float]  # Parameters for the distribution
    source: str  # Literature source for the prior
    rationale: str  # Why this prior was chosen


@dataclass
class BayesianPanelResults:
    """Results from Bayesian panel estimation."""
    posterior_samples: az.InferenceData
    posterior_means: pd.Series
    credible_intervals: pd.DataFrame  # 90% and 95% intervals
    convergence_diagnostics: Dict[str, float]
    model_comparison: Dict[str, float]  # WAIC, LOO
    effect_probabilities: Dict[str, float]  # P(effect > 0)
    policy_summary: Dict[str, any]


class BayesianPanelModel:
    """
    Bayesian panel regression with conflict-informed priors.
    
    Model:
    y_it = α_i + β'X_it + γ'Conflict_it + θ'Aid_it + ε_it
    
    With hierarchical structure:
    - α_i ~ N(μ_α, σ²_α)  # Random intercepts
    - β ~ N(μ_β, Σ_β)     # Informative priors on coefficients
    """
    
    def __init__(self, 
                 random_effects: bool = True,
                 hierarchical: bool = False,
                 nuts_samples: int = 2000,
                 nuts_tune: int = 1000,
                 target_accept: float = 0.95):
        """
        Initialize Bayesian panel model.
        
        Args:
            random_effects: Include random effects for entities
            hierarchical: Use hierarchical structure for coefficients
            nuts_samples: Number of MCMC samples to draw
            nuts_tune: Number of tuning samples
            target_accept: Target acceptance rate for NUTS
        """
        self.random_effects = random_effects
        self.hierarchical = hierarchical
        self.nuts_samples = nuts_samples
        self.nuts_tune = nuts_tune
        self.target_accept = target_accept
        
        self.model = None
        self.trace = None
        self.prior_specs = {}
        
    def set_yemen_conflict_priors(self) -> Dict[str, PriorSpecification]:
        """
        Set informative priors based on conflict economics literature.
        
        Returns dictionary of prior specifications informed by:
        - Cross-country meta-analyses
        - Previous Yemen studies
        - Theoretical bounds
        """
        priors = {
            'conflict_effect': PriorSpecification(
                distribution='normal',
                params={'mu': 0.15, 'sigma': 0.08},
                source='Blattman & Miguel (2010), Amodio & Di Maio (2018)',
                rationale='Meta-analysis shows 10-20% price increase from conflict'
            ),
            
            'exchange_rate_passthrough': PriorSpecification(
                distribution='normal',
                params={'mu': 0.8, 'sigma': 0.2},
                source='Obstfeld & Rogoff (1996), Goldberg & Knetter (1997)',
                rationale='High pass-through expected in developing countries'
            ),
            
            'aid_cash_effect': PriorSpecification(
                distribution='normal',
                params={'mu': -0.08, 'sigma': 0.10},
                source='Cunha et al. (2019), Aker (2017)',
                rationale='Cash aid typically reduces prices by 5-10%'
            ),
            
            'aid_inkind_effect': PriorSpecification(
                distribution='normal',
                params={'mu': -0.15, 'sigma': 0.12},
                source='Lentz et al. (2013), Barrett & Maxwell (2005)',
                rationale='In-kind aid has larger price effects than cash'
            ),
            
            'displacement_effect': PriorSpecification(
                distribution='student_t',
                params={'nu': 3, 'mu': -0.20, 'sigma': 0.15},
                source='Ruiz & Vargas-Silva (2013), Maystadt & Verwimp (2014)',
                rationale='Heavy-tailed due to extreme displacement events'
            ),
            
            'currency_zone_differential': PriorSpecification(
                distribution='normal',
                params={'mu': 2.5, 'sigma': 0.5},
                source='This study - Yemen specific',
                rationale='Government/Houthi exchange rate ratio ~3.7x'
            ),
            
            'market_integration_baseline': PriorSpecification(
                distribution='beta',
                params={'alpha': 2, 'beta': 2},
                source='Aker et al. (2010), Fackler & Goodwin (2001)',
                rationale='Moderate integration expected in conflict settings'
            ),
            
            'seasonal_ramadan_effect': PriorSpecification(
                distribution='normal',
                params={'mu': 0.12, 'sigma': 0.05},
                source='Arezki & Bruckner (2014), local knowledge',
                rationale='10-15% price increase during Ramadan'
            ),
            
            'variance_conflict_zone': PriorSpecification(
                distribution='inverse_gamma',
                params={'alpha': 3, 'beta': 0.5},
                source='Brück et al. (2016)',
                rationale='Higher price volatility in conflict zones'
            )
        }
        
        self.prior_specs = priors
        return priors
    
    def build_model(self,
                    panel_data: pd.DataFrame,
                    dependent_var: str,
                    fixed_effects_vars: List[str],
                    entity_var: str = 'market_id',
                    time_var: str = 'date') -> pm.Model:
        """
        Build PyMC model with specified priors.
        
        Args:
            panel_data: Panel dataframe with MultiIndex (entity, time)
            dependent_var: Name of dependent variable
            fixed_effects_vars: List of covariate names
            entity_var: Entity identifier column
            time_var: Time identifier column
            
        Returns:
            PyMC model object
        """
        logger.info("Building Bayesian panel model")
        
        # Ensure priors are set
        if not self.prior_specs:
            self.set_yemen_conflict_priors()
        
        # Extract data
        y = panel_data[dependent_var].values
        X = panel_data[fixed_effects_vars].values
        
        # Get dimensions
        n_obs = len(y)
        n_vars = X.shape[1]
        
        # Entity mapping for random effects
        if self.random_effects:
            entity_idx = pd.Categorical(panel_data.index.get_level_values(entity_var)).codes
            n_entities = len(np.unique(entity_idx))
        
        with pm.Model() as model:
            # Data containers
            X_data = pm.Data('X', X)
            y_data = pm.Data('y', y)
            
            # Global intercept
            alpha_global = pm.Normal('alpha_global', mu=0, sigma=10)
            
            # Random intercepts if specified
            if self.random_effects:
                # Hyperpriors for random effects
                mu_alpha = pm.Normal('mu_alpha', mu=0, sigma=5)
                sigma_alpha = pm.HalfNormal('sigma_alpha', sigma=2)
                
                # Random intercepts
                alpha_i = pm.Normal('alpha_i', 
                                   mu=mu_alpha, 
                                   sigma=sigma_alpha,
                                   shape=n_entities)
                
                # Map to observations
                alpha = alpha_global + alpha_i[entity_idx]
            else:
                alpha = alpha_global
            
            # Regression coefficients with informative priors
            betas = []
            for i, var_name in enumerate(fixed_effects_vars):
                # Check if we have a specific prior
                if var_name in self.prior_specs:
                    prior = self.prior_specs[var_name]
                    if prior.distribution == 'normal':
                        beta = pm.Normal(f'beta_{var_name}',
                                       mu=prior.params['mu'],
                                       sigma=prior.params['sigma'])
                    elif prior.distribution == 'student_t':
                        beta = pm.StudentT(f'beta_{var_name}',
                                         nu=prior.params['nu'],
                                         mu=prior.params['mu'],
                                         sigma=prior.params['sigma'])
                else:
                    # Default weakly informative prior
                    beta = pm.Normal(f'beta_{var_name}', mu=0, sigma=1)
                
                betas.append(beta)
            
            # Stack coefficients
            beta_vector = pm.math.stack(betas)
            
            # Linear predictor
            mu = alpha + pm.math.dot(X_data, beta_vector)
            
            # Error variance with potential heteroskedasticity
            if 'conflict_intensity' in fixed_effects_vars:
                # Allow variance to depend on conflict
                conflict_idx = fixed_effects_vars.index('conflict_intensity')
                base_sigma = pm.HalfNormal('base_sigma', sigma=1)
                conflict_sigma_effect = pm.Normal('conflict_sigma_effect', mu=0.5, sigma=0.2)
                sigma = base_sigma * pm.math.exp(conflict_sigma_effect * X[:, conflict_idx])
            else:
                # Homoskedastic errors
                sigma = pm.HalfNormal('sigma', sigma=1)
            
            # Likelihood
            likelihood = pm.Normal('y_obs', mu=mu, sigma=sigma, observed=y_data)
            
            # Add potential for Student-t errors in conflict zones
            if self.hierarchical and 'is_conflict_zone' in panel_data.columns:
                # Degrees of freedom parameter
                nu = pm.Gamma('nu', alpha=2, beta=0.1)
                # Switch to Student-t for conflict observations
                conflict_mask = panel_data['is_conflict_zone'].values
                likelihood_robust = pm.StudentT('y_obs_robust',
                                              nu=nu,
                                              mu=mu[conflict_mask],
                                              sigma=sigma,
                                              observed=y_data[conflict_mask])
        
        self.model = model
        return model
    
    def fit(self,
            panel_data: pd.DataFrame,
            dependent_var: str,
            fixed_effects_vars: List[str],
            **kwargs) -> BayesianPanelResults:
        """
        Fit the Bayesian panel model using MCMC.
        
        Args:
            panel_data: Panel data
            dependent_var: Dependent variable name
            fixed_effects_vars: Covariate names
            **kwargs: Additional arguments for model building
            
        Returns:
            BayesianPanelResults object
        """
        # Build model
        model = self.build_model(panel_data, dependent_var, fixed_effects_vars, **kwargs)
        
        logger.info(f"Sampling {self.nuts_samples} draws with {self.nuts_tune} tuning steps")
        
        with model:
            # Sample from posterior
            trace = pm.sample(
                draws=self.nuts_samples,
                tune=self.nuts_tune,
                target_accept=self.target_accept,
                return_inferencedata=True,
                progressbar=True
            )
            
            # Add prior and posterior predictive checks
            trace.extend(pm.sample_prior_predictive())
            trace.extend(pm.sample_posterior_predictive(trace))
        
        self.trace = trace
        
        # Extract results
        results = self._extract_results(trace, fixed_effects_vars)
        
        return results
    
    def _extract_results(self,
                        trace: az.InferenceData,
                        var_names: List[str]) -> BayesianPanelResults:
        """Extract and format results from MCMC trace."""
        # Posterior means
        posterior_means = {}
        for var in var_names:
            var_samples = trace.posterior[f'beta_{var}'].values.flatten()
            posterior_means[var] = np.mean(var_samples)
        
        posterior_means_series = pd.Series(posterior_means)
        
        # Credible intervals
        credible_intervals = pd.DataFrame(index=var_names)
        for var in var_names:
            var_samples = trace.posterior[f'beta_{var}'].values.flatten()
            credible_intervals.loc[var, 'CI_90_lower'] = np.percentile(var_samples, 5)
            credible_intervals.loc[var, 'CI_90_upper'] = np.percentile(var_samples, 95)
            credible_intervals.loc[var, 'CI_95_lower'] = np.percentile(var_samples, 2.5)
            credible_intervals.loc[var, 'CI_95_upper'] = np.percentile(var_samples, 97.5)
        
        # Convergence diagnostics
        diagnostics = {
            'rhat_max': float(az.rhat(trace).max().values),
            'ess_bulk_min': float(az.ess(trace, method='bulk').min().values),
            'ess_tail_min': float(az.ess(trace, method='tail').min().values),
            'energy': float(trace.sample_stats.energy.mean())
        }
        
        # Model comparison metrics
        model_comparison = {
            'waic': az.waic(trace).waic,
            'loo': az.loo(trace).loo
        }
        
        # Effect probabilities (probability that effect > 0)
        effect_probs = {}
        for var in var_names:
            var_samples = trace.posterior[f'beta_{var}'].values.flatten()
            effect_probs[var] = float(np.mean(var_samples > 0))
        
        # Policy summary
        policy_summary = self._create_policy_summary(
            posterior_means_series,
            credible_intervals,
            effect_probs
        )
        
        return BayesianPanelResults(
            posterior_samples=trace,
            posterior_means=posterior_means_series,
            credible_intervals=credible_intervals,
            convergence_diagnostics=diagnostics,
            model_comparison=model_comparison,
            effect_probabilities=effect_probs,
            policy_summary=policy_summary
        )
    
    def _create_policy_summary(self,
                              means: pd.Series,
                              intervals: pd.DataFrame,
                              effect_probs: Dict[str, float]) -> Dict[str, any]:
        """Create policy-friendly summary of results."""
        summary = {
            'key_findings': [],
            'confidence_statements': [],
            'policy_implications': []
        }
        
        # Interpret key effects
        for var, mean in means.items():
            ci_90_lower = intervals.loc[var, 'CI_90_lower']
            ci_90_upper = intervals.loc[var, 'CI_90_upper']
            prob_positive = effect_probs[var]
            
            # Create confidence statement
            if prob_positive > 0.95:
                confidence = "very strong evidence"
                direction = "increases" if mean > 0 else "decreases"
            elif prob_positive > 0.90 or prob_positive < 0.10:
                confidence = "strong evidence"
                direction = "increases" if mean > 0 else "decreases"
            elif prob_positive > 0.75 or prob_positive < 0.25:
                confidence = "moderate evidence"
                direction = "increases" if mean > 0 else "decreases"
            else:
                confidence = "weak evidence"
                direction = "affects"
            
            # Format finding
            if 'conflict' in var.lower():
                summary['key_findings'].append(
                    f"Conflict {direction} prices by {abs(mean)*100:.1f}% "
                    f"(90% CI: {ci_90_lower*100:.1f}% to {ci_90_upper*100:.1f}%)"
                )
                summary['confidence_statements'].append(
                    f"There is {confidence} that conflict {direction} prices"
                )
                
            elif 'exchange' in var.lower():
                summary['key_findings'].append(
                    f"A 10% increase in exchange rate leads to {mean*10*100:.1f}% price increase"
                )
                
            elif 'aid' in var.lower():
                aid_type = 'cash' if 'cash' in var.lower() else 'in-kind'
                summary['key_findings'].append(
                    f"{aid_type.title()} aid {direction} prices by {abs(mean)*100:.1f}%"
                )
        
        # Policy implications
        if 'conflict_effect' in means.index and means['conflict_effect'] > 0:
            summary['policy_implications'].append(
                "Conflict zones require additional aid to compensate for higher prices"
            )
        
        if 'exchange_rate_passthrough' in means.index and means['exchange_rate_passthrough'] > 0.7:
            summary['policy_implications'].append(
                "High exchange rate pass-through suggests currency stabilization is critical"
            )
        
        return summary
    
    def plot_posterior_distributions(self,
                                    var_names: Optional[List[str]] = None) -> None:
        """Plot posterior distributions with prior overlay."""
        if self.trace is None:
            raise ValueError("Model must be fitted first")
        
        import matplotlib.pyplot as plt
        
        if var_names is None:
            var_names = [v for v in self.trace.posterior.data_vars if v.startswith('beta_')]
        
        fig, axes = plt.subplots(len(var_names), 2, figsize=(12, 4*len(var_names)))
        if len(var_names) == 1:
            axes = axes.reshape(1, -1)
        
        for i, var in enumerate(var_names):
            # Posterior distribution
            az.plot_posterior(self.trace, var_names=[var], ax=axes[i, 0])
            
            # Trace plot
            az.plot_trace(self.trace, var_names=[var], axes=axes[i, 1:])
            
            # Add prior if available
            clean_var = var.replace('beta_', '')
            if clean_var in self.prior_specs:
                prior = self.prior_specs[clean_var]
                axes[i, 0].axvline(prior.params.get('mu', 0), 
                                  color='red', 
                                  linestyle='--', 
                                  label='Prior mean')
                axes[i, 0].legend()
        
        plt.tight_layout()
        plt.show()
    
    def posterior_predictive_check(self) -> Dict[str, float]:
        """Perform posterior predictive checks."""
        if self.trace is None:
            raise ValueError("Model must be fitted first")
        
        # Extract observed and predicted
        y_obs = self.trace.observed_data.y.values
        y_pred = self.trace.posterior_predictive.y_obs.values.flatten()
        
        # Calculate fit statistics
        residuals = y_obs - np.mean(y_pred)
        
        checks = {
            'mean_absolute_error': np.mean(np.abs(residuals)),
            'rmse': np.sqrt(np.mean(residuals**2)),
            'coverage_90': np.mean(
                (y_obs >= np.percentile(y_pred, 5)) & 
                (y_obs <= np.percentile(y_pred, 95))
            ),
            'bayesian_r2': 1 - np.var(residuals) / np.var(y_obs)
        }
        
        return checks
    
    def compare_models(self,
                      other_model: 'BayesianPanelModel') -> pd.DataFrame:
        """Compare this model with another using information criteria."""
        if self.trace is None or other_model.trace is None:
            raise ValueError("Both models must be fitted")
        
        comparison = az.compare({
            'model1': self.trace,
            'model2': other_model.trace
        })
        
        return comparison


class BayesianModelAveraging:
    """
    Bayesian Model Averaging for model uncertainty.
    
    Addresses uncertainty about model specification by averaging
    over multiple plausible models weighted by posterior probabilities.
    """
    
    def __init__(self, models: List[BayesianPanelModel]):
        """Initialize with candidate models."""
        self.models = models
        self.weights = None
        self.averaged_results = None
        
    def compute_model_weights(self) -> np.ndarray:
        """Compute model weights using WAIC."""
        waics = []
        
        for model in self.models:
            if model.trace is None:
                raise ValueError("All models must be fitted")
            waics.append(az.waic(model.trace).waic)
        
        # Convert WAIC to weights (lower is better)
        waics = np.array(waics)
        # Use Akaike weights formula
        delta_waic = waics - np.min(waics)
        weights = np.exp(-0.5 * delta_waic)
        weights = weights / np.sum(weights)
        
        self.weights = weights
        return weights
    
    def average_predictions(self,
                          X_new: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Get model-averaged predictions.
        
        Returns:
            Tuple of (mean predictions, standard deviations)
        """
        if self.weights is None:
            self.compute_model_weights()
        
        predictions = []
        
        for model, weight in zip(self.models, self.weights):
            # Get predictions from each model
            pred = model.predict(X_new)
            predictions.append(pred * weight)
        
        # Weighted average
        mean_pred = np.sum(predictions, axis=0)
        
        # Uncertainty includes both within and between model uncertainty
        var_within = np.sum([
            weight * model.predict_variance(X_new) 
            for model, weight in zip(self.models, self.weights)
        ], axis=0)
        
        var_between = np.sum([
            weight * (pred - mean_pred)**2
            for pred, weight in zip(predictions, self.weights)
        ], axis=0)
        
        total_var = var_within + var_between
        std_pred = np.sqrt(total_var)
        
        return mean_pred, std_pred