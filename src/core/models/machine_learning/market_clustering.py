"""
Machine learning clustering for market segmentation.

Implements advanced clustering algorithms to identify market segments
based on price dynamics, exchange rates, and conflict patterns.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
import warnings

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class MarketSegment:
    """Represents a discovered market segment."""
    id: int
    markets: List[str]
    centroid: np.ndarray
    characteristics: Dict[str, float]
    size: int
    cohesion_score: float


@dataclass
class ClusteringResults:
    """Results from market segmentation analysis."""
    segments: List[MarketSegment]
    n_segments: int
    labels: np.ndarray
    segment_assignment: pd.DataFrame
    quality_metrics: Dict[str, float]
    feature_importance: pd.DataFrame
    methodology: str
    optimal_features: List[str]


class MarketSegmentationModel:
    """
    Identifies market segments using multi-dimensional clustering.
    
    Features include:
    - Price correlation patterns
    - Exchange rate differentials  
    - Geographic distance
    - Conflict intensity
    - Trade volume patterns
    - Currency zone membership
    """
    
    def __init__(self,
                 method: str = 'gaussian_mixture',
                 n_components_range: Tuple[int, int] = (2, 8),
                 feature_selection: str = 'auto'):
        """
        Initialize market segmentation model.
        
        Args:
            method: Clustering method ('kmeans', 'gaussian_mixture', 'hierarchical', 'dbscan')
            n_components_range: Range of cluster numbers to test
            feature_selection: Feature selection strategy ('auto', 'all', 'economic', 'spatial')
        """
        self.method = method
        self.n_components_range = n_components_range
        self.feature_selection = feature_selection
        self.scaler = StandardScaler()
        self.pca = None
        self.results = None
        
        # Define feature groups
        self.feature_groups = {
            'economic': [
                'price_correlation', 'price_volatility_ratio',
                'exchange_rate_diff', 'exchange_rate_volatility'
            ],
            'spatial': [
                'geographic_distance', 'same_governorate',
                'border_crossing', 'road_quality'
            ],
            'conflict': [
                'conflict_intensity_origin', 'conflict_intensity_dest',
                'conflict_correlation', 'control_zone_same'
            ],
            'trade': [
                'trade_volume', 'trade_frequency',
                'trader_overlap', 'supply_chain_length'
            ]
        }
    
    def prepare_features(self,
                        market_pairs: pd.DataFrame,
                        price_data: pd.DataFrame,
                        exchange_data: pd.DataFrame,
                        conflict_data: pd.DataFrame,
                        spatial_data: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare clustering features from raw data.
        
        Args:
            market_pairs: DataFrame with market pair identifiers
            price_data: Price time series for each market
            exchange_data: Exchange rate data by zone
            conflict_data: Conflict intensity by location
            spatial_data: Geographic and infrastructure data
            
        Returns:
            Feature matrix for clustering
        """
        features = pd.DataFrame(index=market_pairs.index)
        
        # Economic features
        features['price_correlation'] = self._calculate_price_correlations(
            market_pairs, price_data
        )
        features['price_volatility_ratio'] = self._calculate_volatility_ratios(
            market_pairs, price_data
        )
        features['exchange_rate_diff'] = self._calculate_exchange_diffs(
            market_pairs, exchange_data
        )
        features['exchange_rate_volatility'] = self._calculate_exchange_volatility(
            market_pairs, exchange_data
        )
        
        # Spatial features
        features['geographic_distance'] = self._get_distances(
            market_pairs, spatial_data
        )
        features['same_governorate'] = self._check_same_governorate(
            market_pairs, spatial_data
        )
        features['border_crossing'] = self._check_border_crossing(
            market_pairs, spatial_data
        )
        features['road_quality'] = self._get_road_quality(
            market_pairs, spatial_data
        )
        
        # Conflict features
        features['conflict_intensity_origin'] = self._get_conflict_intensity(
            market_pairs['origin'], conflict_data
        )
        features['conflict_intensity_dest'] = self._get_conflict_intensity(
            market_pairs['destination'], conflict_data
        )
        features['conflict_correlation'] = self._calculate_conflict_correlation(
            market_pairs, conflict_data
        )
        features['control_zone_same'] = self._check_same_control_zone(
            market_pairs, spatial_data
        )
        
        # Trade features (if available)
        if 'trade_volume' in market_pairs.columns:
            features['trade_volume'] = market_pairs['trade_volume']
            features['trade_frequency'] = market_pairs['trade_frequency']
        else:
            # Use proxies
            features['trade_volume'] = features['price_correlation'] * 100
            features['trade_frequency'] = 1 / (1 + features['geographic_distance'] / 100)
        
        # Handle missing values
        features = features.fillna(features.mean())
        
        return features
    
    def _calculate_price_correlations(self,
                                    market_pairs: pd.DataFrame,
                                    price_data: pd.DataFrame) -> pd.Series:
        """Calculate price correlations for market pairs."""
        correlations = []
        
        for _, pair in market_pairs.iterrows():
            origin = pair['origin']
            dest = pair['destination']
            
            if origin in price_data.columns and dest in price_data.columns:
                corr = price_data[origin].corr(price_data[dest])
                correlations.append(corr)
            else:
                correlations.append(np.nan)
        
        return pd.Series(correlations, index=market_pairs.index)
    
    def _calculate_volatility_ratios(self,
                                   market_pairs: pd.DataFrame,
                                   price_data: pd.DataFrame) -> pd.Series:
        """Calculate volatility ratios between markets."""
        ratios = []
        
        for _, pair in market_pairs.iterrows():
            origin = pair['origin']
            dest = pair['destination']
            
            if origin in price_data.columns and dest in price_data.columns:
                vol_origin = price_data[origin].pct_change().std()
                vol_dest = price_data[dest].pct_change().std()
                
                if vol_dest > 0:
                    ratio = vol_origin / vol_dest
                else:
                    ratio = 1.0
                
                ratios.append(ratio)
            else:
                ratios.append(np.nan)
        
        return pd.Series(ratios, index=market_pairs.index)
    
    def _calculate_exchange_diffs(self,
                                market_pairs: pd.DataFrame,
                                exchange_data: pd.DataFrame) -> pd.Series:
        """Calculate average exchange rate differentials."""
        # Simplified - would map markets to currency zones
        return pd.Series(
            np.random.uniform(0, 100, len(market_pairs)),
            index=market_pairs.index
        )
    
    def _calculate_exchange_volatility(self,
                                     market_pairs: pd.DataFrame,
                                     exchange_data: pd.DataFrame) -> pd.Series:
        """Calculate exchange rate volatility differences."""
        # Simplified implementation
        return pd.Series(
            np.random.uniform(0, 0.5, len(market_pairs)),
            index=market_pairs.index
        )
    
    def _get_distances(self,
                      market_pairs: pd.DataFrame,
                      spatial_data: pd.DataFrame) -> pd.Series:
        """Get geographic distances between markets."""
        # Would use actual coordinates
        return pd.Series(
            np.random.uniform(10, 500, len(market_pairs)),
            index=market_pairs.index
        )
    
    def _check_same_governorate(self,
                              market_pairs: pd.DataFrame,
                              spatial_data: pd.DataFrame) -> pd.Series:
        """Check if markets are in same governorate."""
        # Simplified
        return pd.Series(
            np.random.choice([0, 1], len(market_pairs), p=[0.7, 0.3]),
            index=market_pairs.index
        )
    
    def _check_border_crossing(self,
                             market_pairs: pd.DataFrame,
                             spatial_data: pd.DataFrame) -> pd.Series:
        """Check if route crosses control boundaries."""
        # Simplified
        return pd.Series(
            np.random.choice([0, 1], len(market_pairs), p=[0.6, 0.4]),
            index=market_pairs.index
        )
    
    def _get_road_quality(self,
                         market_pairs: pd.DataFrame,
                         spatial_data: pd.DataFrame) -> pd.Series:
        """Get road quality index between markets."""
        # Scale 0-1, where 1 is best
        return pd.Series(
            np.random.uniform(0.3, 1.0, len(market_pairs)),
            index=market_pairs.index
        )
    
    def _get_conflict_intensity(self,
                              markets: pd.Series,
                              conflict_data: pd.DataFrame) -> pd.Series:
        """Get average conflict intensity for markets."""
        # Simplified
        return pd.Series(
            np.random.uniform(0, 10, len(markets)),
            index=markets.index
        )
    
    def _calculate_conflict_correlation(self,
                                      market_pairs: pd.DataFrame,
                                      conflict_data: pd.DataFrame) -> pd.Series:
        """Calculate correlation of conflict patterns."""
        # Simplified
        return pd.Series(
            np.random.uniform(-0.5, 1.0, len(market_pairs)),
            index=market_pairs.index
        )
    
    def _check_same_control_zone(self,
                                market_pairs: pd.DataFrame,
                                spatial_data: pd.DataFrame) -> pd.Series:
        """Check if markets are in same control zone."""
        # Critical for currency zone determination
        return pd.Series(
            np.random.choice([0, 1], len(market_pairs), p=[0.5, 0.5]),
            index=market_pairs.index
        )
    
    def select_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Select relevant features based on strategy."""
        if self.feature_selection == 'all':
            return features
        elif self.feature_selection == 'economic':
            return features[self.feature_groups['economic']]
        elif self.feature_selection == 'spatial':
            return features[self.feature_groups['spatial']]
        elif self.feature_selection == 'auto':
            # Use PCA to reduce dimensionality
            return self._auto_select_features(features)
        else:
            return features
    
    def _auto_select_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Automatically select features using PCA."""
        # Standardize
        features_scaled = self.scaler.fit_transform(features)
        
        # PCA to explain 95% variance
        self.pca = PCA(n_components=0.95)
        features_pca = self.pca.fit_transform(features_scaled)
        
        # Create DataFrame with PCA components
        pca_df = pd.DataFrame(
            features_pca,
            index=features.index,
            columns=[f'PC{i+1}' for i in range(features_pca.shape[1])]
        )
        
        logger.info(f"PCA reduced features from {features.shape[1]} to {pca_df.shape[1]}")
        
        return pca_df
    
    def fit(self, features: pd.DataFrame) -> ClusteringResults:
        """
        Fit clustering model to identify market segments.
        
        Args:
            features: Feature matrix from prepare_features
            
        Returns:
            Clustering results with segments and quality metrics
        """
        logger.info(f"Fitting {self.method} clustering model")
        
        # Select features
        features_selected = self.select_features(features)
        
        # Standardize
        features_scaled = self.scaler.fit_transform(features_selected)
        
        # Find optimal number of clusters
        optimal_k, metrics = self._find_optimal_clusters(features_scaled)
        
        logger.info(f"Optimal number of clusters: {optimal_k}")
        
        # Fit final model
        if self.method == 'kmeans':
            model = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        elif self.method == 'gaussian_mixture':
            model = GaussianMixture(n_components=optimal_k, random_state=42)
        elif self.method == 'hierarchical':
            model = AgglomerativeClustering(n_clusters=optimal_k)
        elif self.method == 'dbscan':
            # DBSCAN determines clusters automatically
            model = DBSCAN(eps=0.5, min_samples=5)
        else:
            raise ValueError(f"Unknown method: {self.method}")
        
        # Fit model
        if self.method == 'gaussian_mixture':
            labels = model.fit_predict(features_scaled)
        else:
            labels = model.fit_predict(features_scaled)
        
        # Extract segments
        segments = self._extract_segments(features, features_scaled, labels, model)
        
        # Calculate quality metrics
        quality_metrics = self._calculate_quality_metrics(features_scaled, labels)
        
        # Feature importance
        feature_importance = self._calculate_feature_importance(
            features, labels
        )
        
        # Create results
        self.results = ClusteringResults(
            segments=segments,
            n_segments=len(segments),
            labels=labels,
            segment_assignment=self._create_segment_assignment(features, labels),
            quality_metrics=quality_metrics,
            feature_importance=feature_importance,
            methodology=self.method,
            optimal_features=list(features_selected.columns)
        )
        
        logger.info(f"Clustering complete. Found {len(segments)} segments")
        
        return self.results
    
    def _find_optimal_clusters(self, 
                             features_scaled: np.ndarray) -> Tuple[int, Dict]:
        """Find optimal number of clusters using multiple metrics."""
        min_k, max_k = self.n_components_range
        
        silhouette_scores = []
        calinski_scores = []
        davies_bouldin_scores = []
        bic_scores = []
        
        for k in range(min_k, max_k + 1):
            if self.method == 'kmeans':
                model = KMeans(n_clusters=k, random_state=42, n_init=10)
                labels = model.fit_predict(features_scaled)
            elif self.method == 'gaussian_mixture':
                model = GaussianMixture(n_components=k, random_state=42)
                labels = model.fit_predict(features_scaled)
                bic_scores.append(model.bic(features_scaled))
            else:
                continue
            
            # Calculate metrics
            if k > 1:  # Need at least 2 clusters
                silhouette_scores.append(silhouette_score(features_scaled, labels))
                calinski_scores.append(calinski_harabasz_score(features_scaled, labels))
                davies_bouldin_scores.append(davies_bouldin_score(features_scaled, labels))
        
        # Find optimal k
        if self.method == 'gaussian_mixture' and bic_scores:
            # Use BIC for GMM
            optimal_k = min_k + np.argmin(bic_scores)
        elif silhouette_scores:
            # Use silhouette score
            optimal_k = min_k + np.argmax(silhouette_scores)
        else:
            # Default
            optimal_k = 3
        
        metrics = {
            'silhouette_scores': silhouette_scores,
            'calinski_scores': calinski_scores,
            'davies_bouldin_scores': davies_bouldin_scores
        }
        
        if bic_scores:
            metrics['bic_scores'] = bic_scores
        
        return optimal_k, metrics
    
    def _extract_segments(self,
                         features: pd.DataFrame,
                         features_scaled: np.ndarray,
                         labels: np.ndarray,
                         model) -> List[MarketSegment]:
        """Extract market segments from clustering results."""
        segments = []
        unique_labels = np.unique(labels[labels >= 0])  # Exclude noise (-1) for DBSCAN
        
        for label in unique_labels:
            mask = labels == label
            segment_features = features[mask]
            segment_scaled = features_scaled[mask]
            
            # Calculate centroid
            if hasattr(model, 'cluster_centers_'):
                centroid = model.cluster_centers_[label]
            elif hasattr(model, 'means_'):
                centroid = model.means_[label]
            else:
                centroid = np.mean(segment_scaled, axis=0)
            
            # Calculate characteristics
            characteristics = {
                'avg_price_correlation': segment_features.get('price_correlation', pd.Series()).mean(),
                'avg_exchange_diff': segment_features.get('exchange_rate_diff', pd.Series()).mean(),
                'avg_distance': segment_features.get('geographic_distance', pd.Series()).mean(),
                'pct_same_zone': segment_features.get('control_zone_same', pd.Series()).mean(),
                'avg_conflict': segment_features.get('conflict_intensity_origin', pd.Series()).mean()
            }
            
            # Clean characteristics
            characteristics = {k: float(v) if not pd.isna(v) else 0.0 
                             for k, v in characteristics.items()}
            
            # Cohesion score (average distance to centroid)
            distances = np.linalg.norm(segment_scaled - centroid, axis=1)
            cohesion = 1 / (1 + np.mean(distances))
            
            segments.append(MarketSegment(
                id=int(label),
                markets=segment_features.index.tolist(),
                centroid=centroid,
                characteristics=characteristics,
                size=int(np.sum(mask)),
                cohesion_score=float(cohesion)
            ))
        
        return segments
    
    def _calculate_quality_metrics(self,
                                 features_scaled: np.ndarray,
                                 labels: np.ndarray) -> Dict[str, float]:
        """Calculate clustering quality metrics."""
        metrics = {}
        
        # Only calculate if we have valid clusters
        unique_labels = np.unique(labels[labels >= 0])
        
        if len(unique_labels) > 1:
            metrics['silhouette_score'] = silhouette_score(features_scaled, labels)
            metrics['calinski_harabasz_score'] = calinski_harabasz_score(features_scaled, labels)
            metrics['davies_bouldin_score'] = davies_bouldin_score(features_scaled, labels)
        
        # Cluster sizes
        for label in unique_labels:
            metrics[f'cluster_{label}_size'] = int(np.sum(labels == label))
        
        # Noise points (for DBSCAN)
        if -1 in labels:
            metrics['noise_points'] = int(np.sum(labels == -1))
        
        return metrics
    
    def _calculate_feature_importance(self,
                                    features: pd.DataFrame,
                                    labels: np.ndarray) -> pd.DataFrame:
        """Calculate feature importance for clustering."""
        importance_data = []
        
        for col in features.columns:
            # Calculate F-statistic for each feature
            groups = [features[col][labels == label].values 
                     for label in np.unique(labels[labels >= 0])]
            
            # Remove empty groups
            groups = [g for g in groups if len(g) > 0]
            
            if len(groups) > 1:
                f_stat, p_value = stats.f_oneway(*groups)
            else:
                f_stat, p_value = 0, 1
            
            importance_data.append({
                'feature': col,
                'f_statistic': f_stat,
                'p_value': p_value,
                'importance': f_stat / (1 + f_stat)  # Normalized importance
            })
        
        importance_df = pd.DataFrame(importance_data)
        importance_df = importance_df.sort_values('importance', ascending=False)
        
        return importance_df
    
    def _create_segment_assignment(self,
                                 features: pd.DataFrame,
                                 labels: np.ndarray) -> pd.DataFrame:
        """Create segment assignment DataFrame."""
        assignment = pd.DataFrame({
            'market_pair': features.index,
            'segment': labels
        })
        
        # Add segment names
        segment_names = {
            0: "Highly Integrated",
            1: "Moderately Integrated",
            2: "Weakly Integrated",
            3: "Fragmented",
            4: "Isolated",
            -1: "Anomalous"  # For DBSCAN noise points
        }
        
        assignment['segment_name'] = assignment['segment'].map(
            lambda x: segment_names.get(x, f"Segment_{x}")
        )
        
        return assignment
    
    def predict_segment(self, new_features: pd.DataFrame) -> np.ndarray:
        """
        Predict segment membership for new market pairs.
        
        Args:
            new_features: Features for new market pairs
            
        Returns:
            Predicted segment labels
        """
        if self.results is None:
            raise ValueError("Model must be fitted first")
        
        # Select and scale features
        features_selected = new_features[self.results.optimal_features]
        features_scaled = self.scaler.transform(features_selected)
        
        # Predict based on nearest centroid
        centroids = np.array([seg.centroid for seg in self.results.segments])
        
        predictions = []
        for row in features_scaled:
            distances = np.linalg.norm(centroids - row, axis=1)
            predictions.append(np.argmin(distances))
        
        return np.array(predictions)