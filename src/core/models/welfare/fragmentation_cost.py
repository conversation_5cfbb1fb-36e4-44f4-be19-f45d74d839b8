"""
Fragmentation cost quantification system.

Quantifies the economic costs of currency fragmentation across
different dimensions: trade, welfare, efficiency, and humanitarian impact.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy import stats, optimize

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class FragmentationCosts:
    """Comprehensive fragmentation cost breakdown."""
    total_cost_usd: float
    total_cost_pct_gdp: float
    trade_cost: float
    welfare_cost: float
    efficiency_cost: float
    humanitarian_cost: float
    cost_by_zone: Dict[str, float]
    cost_by_commodity: Dict[str, float]
    deadweight_loss: float
    forgone_trade_volume: float


@dataclass
class CostComponent:
    """Individual cost component."""
    name: str
    value_usd: float
    pct_of_total: float
    affected_population: int
    per_capita_cost: float
    methodology: str


class FragmentationCostEstimator:
    """
    Estimates economic costs of currency fragmentation.
    
    Cost components:
    1. Trade costs: Transaction costs, arbitrage barriers
    2. Welfare costs: Consumer/producer surplus losses
    3. Efficiency costs: Resource misallocation
    4. Humanitarian costs: Increased aid requirements
    """
    
    def __init__(self,
                 gdp_yemen_usd: float = 21.6e9,  # World Bank 2021 estimate
                 population: int = 32.98e6,       # UN 2023 estimate
                 trade_elasticity: float = 1.5):  # From gravity model literature
        """
        Initialize cost estimator.
        
        Args:
            gdp_yemen_usd: Yemen GDP in USD
            population: Total population
            trade_elasticity: Trade elasticity parameter
        """
        self.gdp_yemen_usd = gdp_yemen_usd
        self.population = population
        self.trade_elasticity = trade_elasticity
        
        # Cost parameters from literature
        self.cost_params = {
            'transaction_cost_multiplier': 0.03,  # 3% of trade value
            'deadweight_loss_rate': 0.15,         # 15% efficiency loss
            'aid_inefficiency_rate': 0.25,        # 25% aid effectiveness loss
            'price_dispersion_cost': 0.08         # 8% from price misalignment
        }
    
    def estimate_fragmentation_costs(self,
                                   price_data: pd.DataFrame,
                                   trade_flows: pd.DataFrame,
                                   exchange_rates: pd.DataFrame,
                                   zone_populations: Dict[str, int],
                                   aid_flows: pd.DataFrame) -> FragmentationCosts:
        """
        Estimate total costs of currency fragmentation.
        
        Args:
            price_data: Prices by market and commodity
            trade_flows: Trade volume data between markets
            exchange_rates: Exchange rates by zone
            zone_populations: Population by currency zone
            aid_flows: Humanitarian aid distribution data
            
        Returns:
            Comprehensive cost breakdown
        """
        logger.info("Estimating currency fragmentation costs")
        
        # Calculate trade costs
        trade_cost = self._calculate_trade_costs(
            trade_flows, exchange_rates, price_data
        )
        
        # Calculate welfare costs
        welfare_cost = self._calculate_welfare_costs(
            price_data, zone_populations, exchange_rates
        )
        
        # Calculate efficiency costs
        efficiency_cost = self._calculate_efficiency_costs(
            price_data, trade_flows, exchange_rates
        )
        
        # Calculate humanitarian costs
        humanitarian_cost = self._calculate_humanitarian_costs(
            aid_flows, exchange_rates, zone_populations
        )
        
        # Calculate deadweight loss
        deadweight_loss = self._calculate_deadweight_loss(
            trade_cost, welfare_cost, efficiency_cost
        )
        
        # Calculate forgone trade
        forgone_trade = self._calculate_forgone_trade(
            trade_flows, exchange_rates
        )
        
        # Aggregate costs
        total_cost = (
            trade_cost + welfare_cost + 
            efficiency_cost + humanitarian_cost
        )
        
        total_cost_pct_gdp = (total_cost / self.gdp_yemen_usd) * 100
        
        # Cost by zone
        cost_by_zone = self._allocate_costs_by_zone(
            total_cost, zone_populations, exchange_rates
        )
        
        # Cost by commodity
        cost_by_commodity = self._allocate_costs_by_commodity(
            total_cost, price_data, trade_flows
        )
        
        logger.info(f"Total fragmentation cost: ${total_cost:,.0f} ({total_cost_pct_gdp:.1f}% of GDP)")
        
        return FragmentationCosts(
            total_cost_usd=total_cost,
            total_cost_pct_gdp=total_cost_pct_gdp,
            trade_cost=trade_cost,
            welfare_cost=welfare_cost,
            efficiency_cost=efficiency_cost,
            humanitarian_cost=humanitarian_cost,
            cost_by_zone=cost_by_zone,
            cost_by_commodity=cost_by_commodity,
            deadweight_loss=deadweight_loss,
            forgone_trade_volume=forgone_trade
        )
    
    def _calculate_trade_costs(self,
                             trade_flows: pd.DataFrame,
                             exchange_rates: pd.DataFrame,
                             price_data: pd.DataFrame) -> float:
        """Calculate costs from reduced trade and transaction costs."""
        # Base trade value
        if not trade_flows.empty:
            total_trade_value = trade_flows.sum().sum()
        else:
            # Estimate from price correlations
            avg_price = price_data.mean().mean()
            estimated_trade = self.population * 0.1 * avg_price * 365  # 10% trade
            total_trade_value = estimated_trade
        
        # Exchange rate spread cost
        rate_spread = exchange_rates.max().max() - exchange_rates.min().min()
        spread_pct = rate_spread / exchange_rates.mean().mean()
        
        # Transaction costs increase with fragmentation
        transaction_cost = total_trade_value * self.cost_params['transaction_cost_multiplier'] * (1 + spread_pct)
        
        # Trade reduction from fragmentation (gravity model)
        trade_reduction = total_trade_value * (1 - np.exp(-spread_pct * self.trade_elasticity))
        
        # Arbitrage barriers
        arbitrage_cost = self._calculate_arbitrage_costs(price_data, exchange_rates)
        
        total_trade_cost = transaction_cost + trade_reduction + arbitrage_cost
        
        return total_trade_cost
    
    def _calculate_arbitrage_costs(self,
                                 price_data: pd.DataFrame,
                                 exchange_rates: pd.DataFrame) -> float:
        """Calculate costs from prevented arbitrage."""
        # Price differentials that exceed transport costs
        price_matrix = price_data.values
        n_markets = price_matrix.shape[1]
        
        arbitrage_opportunities = 0
        
        for i in range(n_markets):
            for j in range(i+1, n_markets):
                # Price differential
                price_diff = abs(price_matrix[:, i] - price_matrix[:, j])
                avg_diff = np.nanmean(price_diff)
                
                # Expected transport cost (5% of value)
                transport_cost = np.nanmean(price_matrix[:, [i, j]]) * 0.05
                
                # Arbitrage opportunity if diff > transport
                if avg_diff > transport_cost:
                    arbitrage_opportunities += (avg_diff - transport_cost)
        
        # Scale by population and frequency
        daily_arbitrage_loss = arbitrage_opportunities * self.population / 1e6  # per million people
        annual_arbitrage_cost = daily_arbitrage_loss * 365
        
        return annual_arbitrage_cost
    
    def _calculate_welfare_costs(self,
                               price_data: pd.DataFrame,
                               zone_populations: Dict[str, int],
                               exchange_rates: pd.DataFrame) -> float:
        """Calculate consumer and producer surplus losses."""
        # Consumer surplus loss from higher prices
        consumer_loss = self._calculate_consumer_surplus_loss(
            price_data, zone_populations, exchange_rates
        )
        
        # Producer surplus loss from market segmentation
        producer_loss = self._calculate_producer_surplus_loss(
            price_data, zone_populations
        )
        
        # Allocative inefficiency
        allocative_loss = self._calculate_allocative_inefficiency(
            price_data, zone_populations
        )
        
        total_welfare_cost = consumer_loss + producer_loss + allocative_loss
        
        return total_welfare_cost
    
    def _calculate_consumer_surplus_loss(self,
                                       price_data: pd.DataFrame,
                                       zone_populations: Dict[str, int],
                                       exchange_rates: pd.DataFrame) -> float:
        """Calculate consumer surplus loss from fragmentation."""
        # Average price increase due to fragmentation
        unified_price = price_data.mean().mean()
        
        # Zone-specific prices (accounting for exchange rates)
        zone_losses = 0
        
        for zone, population in zone_populations.items():
            if zone in exchange_rates.columns:
                zone_rate = exchange_rates[zone].mean()
                
                # Higher prices in depreciated currency zones
                if zone == 'government':
                    price_premium = 0.25  # 25% higher effective prices
                else:
                    price_premium = 0.05  # 5% higher in stable zone
                
                # Consumer surplus loss (triangle approximation)
                # Loss = 0.5 * price_increase * quantity_reduction
                price_increase = unified_price * price_premium
                
                # Demand elasticity assumption
                elasticity = -0.6  # Food demand elasticity
                quantity_reduction = price_premium * elasticity
                
                # Per capita daily loss
                per_capita_loss = 0.5 * price_increase * abs(quantity_reduction)
                
                # Total zone loss
                zone_loss = per_capita_loss * population * 365
                zone_losses += zone_loss
        
        return zone_losses
    
    def _calculate_producer_surplus_loss(self,
                                       price_data: pd.DataFrame,
                                       zone_populations: Dict[str, int]) -> float:
        """Calculate producer surplus loss from market segmentation."""
        # Reduced market access reduces producer prices
        market_segmentation_factor = 0.7  # 30% market reduction
        
        # Average production value (simplified)
        production_value = self.gdp_yemen_usd * 0.15  # 15% agricultural GDP
        
        # Producer loss from reduced market access
        producer_loss = production_value * (1 - market_segmentation_factor)
        
        return producer_loss
    
    def _calculate_allocative_inefficiency(self,
                                         price_data: pd.DataFrame,
                                         zone_populations: Dict[str, int]) -> float:
        """Calculate losses from resource misallocation."""
        # Price dispersion indicates misallocation
        price_cv = price_data.std() / price_data.mean()
        avg_cv = price_cv.mean()
        
        # Welfare loss proportional to squared CV (Harberger approximation)
        allocative_loss_rate = 0.5 * avg_cv ** 2
        
        # Apply to total economic value
        affected_value = self.gdp_yemen_usd * 0.3  # 30% of economy affected
        allocative_loss = affected_value * allocative_loss_rate
        
        return allocative_loss
    
    def _calculate_efficiency_costs(self,
                                  price_data: pd.DataFrame,
                                  trade_flows: pd.DataFrame,
                                  exchange_rates: pd.DataFrame) -> float:
        """Calculate economic efficiency losses."""
        # Information asymmetry costs
        info_cost = self._calculate_information_costs(exchange_rates)
        
        # Resource duplication costs
        duplication_cost = self._calculate_duplication_costs(exchange_rates)
        
        # Search and matching costs
        search_cost = self._calculate_search_costs(price_data, trade_flows)
        
        # Financial system inefficiency
        financial_cost = self._calculate_financial_costs(exchange_rates)
        
        total_efficiency_cost = (
            info_cost + duplication_cost + 
            search_cost + financial_cost
        )
        
        return total_efficiency_cost
    
    def _calculate_information_costs(self, exchange_rates: pd.DataFrame) -> float:
        """Calculate costs from information asymmetry."""
        # Volatility creates information costs
        rate_volatility = exchange_rates.pct_change().std().mean()
        
        # Information cost as % of trade value
        info_cost_rate = rate_volatility * 0.02  # 2% per unit volatility
        
        # Apply to affected economic activity
        affected_value = self.gdp_yemen_usd * 0.4  # 40% of economy
        info_cost = affected_value * info_cost_rate
        
        return info_cost
    
    def _calculate_duplication_costs(self, exchange_rates: pd.DataFrame) -> float:
        """Calculate costs from duplicated systems and infrastructure."""
        # Number of currency zones
        n_zones = len(exchange_rates.columns)
        
        # Fixed cost per zone (banking, payment systems, etc.)
        fixed_cost_per_zone = 50e6  # $50 million
        
        # Duplication cost
        duplication_cost = fixed_cost_per_zone * (n_zones - 1)
        
        return duplication_cost
    
    def _calculate_search_costs(self,
                              price_data: pd.DataFrame,
                              trade_flows: pd.DataFrame) -> float:
        """Calculate increased search and matching costs."""
        # Price dispersion increases search costs
        price_dispersion = price_data.std() / price_data.mean()
        avg_dispersion = price_dispersion.mean()
        
        # Search cost proportional to dispersion
        search_cost_rate = avg_dispersion * 0.01  # 1% per unit dispersion
        
        # Apply to trade value
        if not trade_flows.empty:
            trade_value = trade_flows.sum().sum()
        else:
            trade_value = self.gdp_yemen_usd * 0.2  # 20% trade/GDP ratio
        
        search_cost = trade_value * search_cost_rate
        
        return search_cost
    
    def _calculate_financial_costs(self, exchange_rates: pd.DataFrame) -> float:
        """Calculate financial system inefficiency costs."""
        # Multiple exchange rates create financial friction
        rate_spread = (exchange_rates.max() - exchange_rates.min()) / exchange_rates.mean()
        avg_spread = rate_spread.mean()
        
        # Financial friction cost
        friction_rate = avg_spread * 0.03  # 3% per unit spread
        
        # Apply to financial transactions
        financial_volume = self.gdp_yemen_usd * 1.5  # 150% money velocity
        financial_cost = financial_volume * friction_rate
        
        return financial_cost
    
    def _calculate_humanitarian_costs(self,
                                    aid_flows: pd.DataFrame,
                                    exchange_rates: pd.DataFrame,
                                    zone_populations: Dict[str, int]) -> float:
        """Calculate increased humanitarian aid requirements."""
        # Base aid requirement
        if not aid_flows.empty:
            base_aid = aid_flows.sum().sum()
        else:
            # Estimate: $100 per capita humanitarian need
            base_aid = self.population * 100
        
        # Fragmentation increases aid needs
        fragmentation_multiplier = 1 + self.cost_params['aid_inefficiency_rate']
        
        # Zone-specific inefficiencies
        zone_inefficiency = 0
        
        for zone, population in zone_populations.items():
            zone_share = population / self.population
            
            if zone in exchange_rates.columns:
                # Exchange rate volatility increases aid costs
                zone_volatility = exchange_rates[zone].pct_change().std()
                zone_cost = base_aid * zone_share * zone_volatility * 0.5
                zone_inefficiency += zone_cost
        
        # Total humanitarian cost
        humanitarian_cost = (
            base_aid * (fragmentation_multiplier - 1) + zone_inefficiency
        )
        
        return humanitarian_cost
    
    def _calculate_deadweight_loss(self,
                                  trade_cost: float,
                                  welfare_cost: float,
                                  efficiency_cost: float) -> float:
        """Calculate total deadweight loss from fragmentation."""
        # Harberger triangles from various distortions
        deadweight_components = {
            'trade': trade_cost * self.cost_params['deadweight_loss_rate'],
            'welfare': welfare_cost * self.cost_params['deadweight_loss_rate'],
            'efficiency': efficiency_cost * 0.5  # Higher DWL for efficiency
        }
        
        total_deadweight = sum(deadweight_components.values())
        
        return total_deadweight
    
    def _calculate_forgone_trade(self,
                               trade_flows: pd.DataFrame,
                               exchange_rates: pd.DataFrame) -> float:
        """Calculate volume of trade prevented by fragmentation."""
        # Current trade volume
        if not trade_flows.empty:
            current_trade = trade_flows.sum().sum()
        else:
            current_trade = self.gdp_yemen_usd * 0.2  # Estimate
        
        # Exchange rate spread impact on trade (gravity model)
        rate_spread = (exchange_rates.max() - exchange_rates.min()) / exchange_rates.mean()
        avg_spread = rate_spread.mean()
        
        # Trade reduction formula
        trade_reduction_pct = 1 - np.exp(-avg_spread * self.trade_elasticity)
        
        # Forgone trade
        forgone_trade = current_trade * trade_reduction_pct / (1 - trade_reduction_pct)
        
        return forgone_trade
    
    def _allocate_costs_by_zone(self,
                              total_cost: float,
                              zone_populations: Dict[str, int],
                              exchange_rates: pd.DataFrame) -> Dict[str, float]:
        """Allocate total costs across currency zones."""
        cost_by_zone = {}
        total_pop = sum(zone_populations.values())
        
        for zone, population in zone_populations.items():
            # Base allocation by population
            pop_share = population / total_pop
            base_cost = total_cost * pop_share
            
            # Adjust for zone-specific factors
            if zone in exchange_rates.columns:
                # Higher costs in volatile zones
                zone_volatility = exchange_rates[zone].pct_change().std()
                volatility_multiplier = 1 + zone_volatility
                
                zone_cost = base_cost * volatility_multiplier
            else:
                zone_cost = base_cost
            
            cost_by_zone[zone] = zone_cost
        
        # Normalize to ensure sum equals total
        total_allocated = sum(cost_by_zone.values())
        if total_allocated > 0:
            for zone in cost_by_zone:
                cost_by_zone[zone] *= total_cost / total_allocated
        
        return cost_by_zone
    
    def _allocate_costs_by_commodity(self,
                                   total_cost: float,
                                   price_data: pd.DataFrame,
                                   trade_flows: pd.DataFrame) -> Dict[str, float]:
        """Allocate costs across commodities."""
        cost_by_commodity = {}
        
        # Get commodity list
        commodities = price_data.index.tolist()
        
        for commodity in commodities:
            # Price dispersion for this commodity
            if commodity in price_data.index:
                commodity_prices = price_data.loc[commodity]
                price_cv = commodity_prices.std() / commodity_prices.mean()
            else:
                price_cv = 0.2  # Default
            
            # Trade volume weight (if available)
            if not trade_flows.empty and commodity in trade_flows.index:
                trade_weight = trade_flows.loc[commodity].sum() / trade_flows.sum().sum()
            else:
                trade_weight = 1 / len(commodities)  # Equal weight
            
            # Allocate cost based on dispersion and trade volume
            commodity_cost = total_cost * trade_weight * (1 + price_cv)
            cost_by_commodity[commodity] = commodity_cost
        
        # Normalize
        total_allocated = sum(cost_by_commodity.values())
        if total_allocated > 0:
            for commodity in cost_by_commodity:
                cost_by_commodity[commodity] *= total_cost / total_allocated
        
        return cost_by_commodity
    
    def project_costs(self,
                     current_costs: FragmentationCosts,
                     scenarios: Dict[str, Dict],
                     time_horizon: int = 5) -> pd.DataFrame:
        """
        Project fragmentation costs under different scenarios.
        
        Args:
            current_costs: Current cost estimates
            scenarios: Dict of scenarios with parameters
            time_horizon: Years to project
            
        Returns:
            DataFrame with cost projections
        """
        projections = []
        
        for scenario_name, params in scenarios.items():
            # Extract scenario parameters
            exchange_convergence = params.get('exchange_convergence', 0)
            trade_growth = params.get('trade_growth', 0.02)
            aid_efficiency_gain = params.get('aid_efficiency_gain', 0)
            
            for year in range(time_horizon + 1):
                # Project costs with scenario assumptions
                trade_cost = current_costs.trade_cost * (1 + trade_growth) ** year
                
                # Exchange rate convergence reduces costs
                convergence_factor = 1 - (exchange_convergence * year / time_horizon)
                welfare_cost = current_costs.welfare_cost * convergence_factor
                efficiency_cost = current_costs.efficiency_cost * convergence_factor
                
                # Aid efficiency improvements
                humanitarian_cost = current_costs.humanitarian_cost * \
                                  (1 - aid_efficiency_gain * year / time_horizon)
                
                # Total projected cost
                total_cost = trade_cost + welfare_cost + efficiency_cost + humanitarian_cost
                
                projections.append({
                    'scenario': scenario_name,
                    'year': year,
                    'total_cost': total_cost,
                    'trade_cost': trade_cost,
                    'welfare_cost': welfare_cost,
                    'efficiency_cost': efficiency_cost,
                    'humanitarian_cost': humanitarian_cost,
                    'cost_reduction': (current_costs.total_cost_usd - total_cost) / \
                                    current_costs.total_cost_usd * 100
                })
        
        return pd.DataFrame(projections)