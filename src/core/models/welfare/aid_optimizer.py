"""
Aid optimization engine for currency-aware humanitarian programming.

Optimizes aid allocation across currency zones to maximize impact
while accounting for exchange rate differentials and market conditions.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
import cvxpy as cp
from scipy.optimize import linprog, minimize
import warnings

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class AidAllocation:
    """Optimal aid allocation result."""
    zone: str
    cash_amount_usd: float
    inkind_amount_usd: float
    total_amount_usd: float
    beneficiaries: int
    modality: str  # 'cash', 'in-kind', 'mixed'
    expected_impact: float
    cost_effectiveness: float


@dataclass
class OptimizationResults:
    """Results from aid optimization."""
    allocations: List[AidAllocation]
    total_budget_usd: float
    total_beneficiaries: int
    expected_welfare_gain: float
    cost_per_beneficiary: float
    efficiency_gain_pct: float
    sensitivity_analysis: pd.DataFrame
    implementation_plan: Dict


class AidOptimizationEngine:
    """
    Optimizes humanitarian aid distribution across currency zones.
    
    Key features:
    - Currency zone-aware allocation
    - Cash vs in-kind optimization
    - Market impact constraints
    - Dynamic adjustment to conditions
    """
    
    def __init__(self,
                 market_impact_threshold: float = 0.15,
                 min_beneficiary_transfer: float = 50,
                 overhead_rate: float = 0.08):
        """
        Initialize aid optimizer.
        
        Args:
            market_impact_threshold: Max acceptable price impact (15%)
            min_beneficiary_transfer: Minimum transfer per beneficiary (USD)
            overhead_rate: Administrative overhead rate (8%)
        """
        self.market_impact_threshold = market_impact_threshold
        self.min_beneficiary_transfer = min_beneficiary_transfer
        self.overhead_rate = overhead_rate
        
        # Effectiveness parameters from literature
        self.effectiveness_params = {
            'cash_stable_zone': 0.95,      # 95% pass-through in stable zones
            'cash_volatile_zone': 0.75,     # 75% in volatile zones
            'inkind_delivery': 0.85,        # 85% for in-kind
            'inkind_preference_penalty': 0.9 # 10% preference penalty
        }
    
    def optimize_aid_allocation(self,
                              budget_usd: float,
                              zone_data: Dict[str, Dict],
                              price_data: pd.DataFrame,
                              exchange_rates: pd.DataFrame,
                              need_assessment: pd.DataFrame,
                              constraints: Optional[Dict] = None) -> OptimizationResults:
        """
        Optimize aid allocation across zones and modalities.
        
        Args:
            budget_usd: Total available budget in USD
            zone_data: Data by zone (population, vulnerability, etc.)
            price_data: Current market prices
            exchange_rates: Exchange rates by zone
            need_assessment: Humanitarian needs by location
            constraints: Additional constraints (e.g., donor restrictions)
            
        Returns:
            Optimal allocation results
        """
        logger.info(f"Optimizing aid allocation for budget: ${budget_usd:,.0f}")
        
        # Prepare optimization problem
        zones = list(zone_data.keys())
        n_zones = len(zones)
        
        # Decision variables
        cash_alloc = cp.Variable(n_zones, nonneg=True)
        inkind_alloc = cp.Variable(n_zones, nonneg=True)
        
        # Objective: Maximize total welfare impact
        objective = self._create_objective(
            cash_alloc, inkind_alloc, zone_data, exchange_rates
        )
        
        # Constraints
        constraints_list = self._create_constraints(
            cash_alloc, inkind_alloc, budget_usd, zone_data, 
            price_data, exchange_rates, need_assessment, constraints
        )
        
        # Solve optimization
        problem = cp.Problem(cp.Maximize(objective), constraints_list)
        
        try:
            problem.solve(solver=cp.ECOS)
            
            if problem.status not in ["optimal", "optimal_inaccurate"]:
                logger.warning(f"Optimization status: {problem.status}")
                # Fall back to simple allocation
                return self._fallback_allocation(budget_usd, zone_data, need_assessment)
        except Exception as e:
            logger.error(f"Optimization failed: {e}")
            return self._fallback_allocation(budget_usd, zone_data, need_assessment)
        
        # Extract results
        allocations = self._extract_allocations(
            cash_alloc.value, inkind_alloc.value, zones, 
            zone_data, exchange_rates
        )
        
        # Calculate metrics
        total_beneficiaries = sum(a.beneficiaries for a in allocations)
        expected_welfare = objective.value
        
        # Sensitivity analysis
        sensitivity = self._sensitivity_analysis(
            problem, cash_alloc, inkind_alloc, zones
        )
        
        # Implementation plan
        implementation = self._create_implementation_plan(
            allocations, zone_data, exchange_rates
        )
        
        # Calculate efficiency gain vs naive allocation
        naive_welfare = self._calculate_naive_welfare(budget_usd, zone_data)
        efficiency_gain = (expected_welfare - naive_welfare) / naive_welfare * 100
        
        return OptimizationResults(
            allocations=allocations,
            total_budget_usd=budget_usd,
            total_beneficiaries=int(total_beneficiaries),
            expected_welfare_gain=float(expected_welfare),
            cost_per_beneficiary=budget_usd / total_beneficiaries if total_beneficiaries > 0 else 0,
            efficiency_gain_pct=float(efficiency_gain),
            sensitivity_analysis=sensitivity,
            implementation_plan=implementation
        )
    
    def _create_objective(self,
                        cash_alloc: cp.Variable,
                        inkind_alloc: cp.Variable,
                        zone_data: Dict[str, Dict],
                        exchange_rates: pd.DataFrame) -> cp.Expression:
        """Create optimization objective function."""
        zones = list(zone_data.keys())
        welfare_impact = 0
        
        for i, zone in enumerate(zones):
            zone_info = zone_data[zone]
            
            # Get effectiveness parameters
            if zone in exchange_rates.columns:
                exr_volatility = exchange_rates[zone].pct_change().std()
                
                if exr_volatility < 0.1:  # Stable zone
                    cash_effect = self.effectiveness_params['cash_stable_zone']
                else:  # Volatile zone
                    cash_effect = self.effectiveness_params['cash_volatile_zone']
            else:
                cash_effect = 0.85  # Default
            
            inkind_effect = self.effectiveness_params['inkind_delivery']
            
            # Welfare impact function (log utility)
            # Account for diminishing returns
            cash_impact = cash_effect * cp.log1p(cash_alloc[i] / zone_info['population'])
            inkind_impact = inkind_effect * cp.log1p(inkind_alloc[i] / zone_info['population'])
            
            # Weight by vulnerability
            vulnerability = zone_info.get('vulnerability_score', 50) / 100
            
            zone_welfare = vulnerability * (cash_impact + inkind_impact) * zone_info['population']
            welfare_impact += zone_welfare
        
        return welfare_impact
    
    def _create_constraints(self,
                          cash_alloc: cp.Variable,
                          inkind_alloc: cp.Variable,
                          budget: float,
                          zone_data: Dict[str, Dict],
                          price_data: pd.DataFrame,
                          exchange_rates: pd.DataFrame,
                          need_assessment: pd.DataFrame,
                          custom_constraints: Optional[Dict]) -> List:
        """Create optimization constraints."""
        zones = list(zone_data.keys())
        n_zones = len(zones)
        constraints = []
        
        # Budget constraint
        total_cost = cp.sum(cash_alloc) + cp.sum(inkind_alloc)
        constraints.append(total_cost <= budget * (1 - self.overhead_rate))
        
        # Minimum transfer constraint
        for i, zone in enumerate(zones):
            zone_pop = zone_data[zone]['population']
            min_total = self.min_beneficiary_transfer * zone_pop * 0.3  # 30% coverage
            
            constraints.append(cash_alloc[i] + inkind_alloc[i] >= min_total)
        
        # Market impact constraints
        for i, zone in enumerate(zones):
            # Cash injections should not destabilize markets
            if zone in exchange_rates.columns:
                market_size = zone_data[zone].get('market_size', 1e8)  # Default 100M
                max_cash = market_size * self.market_impact_threshold
                
                constraints.append(cash_alloc[i] <= max_cash)
        
        # Need-based allocation constraints
        if not need_assessment.empty:
            for i, zone in enumerate(zones):
                if zone in need_assessment.index:
                    zone_need = need_assessment.loc[zone, 'total_need_usd']
                    
                    # Don't exceed assessed need
                    constraints.append(cash_alloc[i] + inkind_alloc[i] <= zone_need * 1.2)
        
        # Supply constraints for in-kind
        for i, zone in enumerate(zones):
            # In-kind limited by logistics capacity
            logistics_capacity = zone_data[zone].get('logistics_capacity', 1e7)  # Default 10M
            constraints.append(inkind_alloc[i] <= logistics_capacity)
        
        # Currency zone specific constraints
        for i, zone in enumerate(zones):
            if zone == 'houthi':
                # Restrictions in Houthi areas - favor in-kind
                constraints.append(inkind_alloc[i] >= 0.6 * (cash_alloc[i] + inkind_alloc[i]))
            elif zone == 'government' and zone in exchange_rates.columns:
                # High volatility areas - limit cash
                volatility = exchange_rates[zone].pct_change().std()
                if volatility > 0.2:
                    constraints.append(cash_alloc[i] <= 0.4 * (cash_alloc[i] + inkind_alloc[i]))
        
        # Custom constraints
        if custom_constraints:
            for constraint_type, params in custom_constraints.items():
                if constraint_type == 'max_cash_pct':
                    max_cash_total = budget * params['value']
                    constraints.append(cp.sum(cash_alloc) <= max_cash_total)
                elif constraint_type == 'min_coverage':
                    for i, zone in enumerate(zones):
                        min_beneficiaries = zone_data[zone]['population'] * params['value']
                        transfer_per_person = self.min_beneficiary_transfer
                        constraints.append(
                            cash_alloc[i] + inkind_alloc[i] >= min_beneficiaries * transfer_per_person
                        )
        
        return constraints
    
    def _extract_allocations(self,
                           cash_values: np.ndarray,
                           inkind_values: np.ndarray,
                           zones: List[str],
                           zone_data: Dict[str, Dict],
                           exchange_rates: pd.DataFrame) -> List[AidAllocation]:
        """Extract allocation results from optimization solution."""
        allocations = []
        
        for i, zone in enumerate(zones):
            cash = float(cash_values[i]) if cash_values is not None else 0
            inkind = float(inkind_values[i]) if inkind_values is not None else 0
            total = cash + inkind
            
            if total > 0:
                # Determine modality
                if cash > 0.8 * total:
                    modality = 'cash'
                elif inkind > 0.8 * total:
                    modality = 'in-kind'
                else:
                    modality = 'mixed'
                
                # Calculate beneficiaries
                avg_transfer = max(self.min_beneficiary_transfer, total / zone_data[zone]['population'])
                beneficiaries = int(total / avg_transfer)
                
                # Expected impact
                if zone in exchange_rates.columns:
                    exr = exchange_rates[zone].iloc[-1]  # Latest rate
                    local_purchasing_power = total * exr
                else:
                    local_purchasing_power = total * 1000  # Default
                
                expected_impact = self._calculate_expected_impact(
                    cash, inkind, zone_data[zone], local_purchasing_power
                )
                
                # Cost effectiveness
                cost_effectiveness = expected_impact / total if total > 0 else 0
                
                allocations.append(AidAllocation(
                    zone=zone,
                    cash_amount_usd=cash,
                    inkind_amount_usd=inkind,
                    total_amount_usd=total,
                    beneficiaries=beneficiaries,
                    modality=modality,
                    expected_impact=expected_impact,
                    cost_effectiveness=cost_effectiveness
                ))
        
        return allocations
    
    def _calculate_expected_impact(self,
                                 cash: float,
                                 inkind: float,
                                 zone_info: Dict,
                                 local_purchasing_power: float) -> float:
        """Calculate expected welfare impact of allocation."""
        # Baseline welfare
        baseline_welfare = zone_info.get('welfare_index', 50)
        
        # Impact multipliers
        cash_multiplier = 1.5 if zone_info.get('market_functioning', True) else 1.2
        inkind_multiplier = 1.3
        
        # Calculate welfare improvement
        cash_impact = (cash / zone_info['population']) * cash_multiplier * 100
        inkind_impact = (inkind / zone_info['population']) * inkind_multiplier * 100
        
        # Diminishing returns
        total_impact = np.log1p(cash_impact + inkind_impact) * 10
        
        # Adjust for local conditions
        if zone_info.get('conflict_intensity', 0) > 5:
            total_impact *= 0.8  # Reduce effectiveness in high conflict
        
        return total_impact
    
    def _sensitivity_analysis(self,
                            problem: cp.Problem,
                            cash_var: cp.Variable,
                            inkind_var: cp.Variable,
                            zones: List[str]) -> pd.DataFrame:
        """Perform sensitivity analysis on optimization results."""
        sensitivity_results = []
        
        # Parameters to test
        param_ranges = {
            'budget': [0.8, 0.9, 1.0, 1.1, 1.2],
            'cash_effectiveness': [0.7, 0.85, 1.0, 1.15, 1.3],
            'market_impact_threshold': [0.1, 0.15, 0.2, 0.25, 0.3]
        }
        
        base_objective = problem.value
        
        # Simplified sensitivity - in practice would re-solve
        for param, values in param_ranges.items():
            for value in values:
                # Estimate impact (simplified)
                if param == 'budget':
                    impact = base_objective * value
                elif param == 'cash_effectiveness':
                    cash_share = np.sum(cash_var.value) / (np.sum(cash_var.value) + np.sum(inkind_var.value))
                    impact = base_objective * (1 + (value - 1) * cash_share)
                else:
                    impact = base_objective * (1 + (value - 0.15) * 0.5)
                
                sensitivity_results.append({
                    'parameter': param,
                    'value': value,
                    'objective': impact,
                    'pct_change': (impact - base_objective) / base_objective * 100
                })
        
        return pd.DataFrame(sensitivity_results)
    
    def _create_implementation_plan(self,
                                  allocations: List[AidAllocation],
                                  zone_data: Dict[str, Dict],
                                  exchange_rates: pd.DataFrame) -> Dict:
        """Create detailed implementation plan."""
        plan = {
            'phases': [],
            'logistics': {},
            'monitoring': {},
            'risk_mitigation': {}
        }
        
        # Phase planning
        total_budget = sum(a.total_amount_usd for a in allocations)
        
        # Phase 1: High vulnerability zones
        phase1_zones = [a for a in allocations 
                       if zone_data[a.zone].get('vulnerability_score', 50) > 70]
        
        phase1_budget = sum(a.total_amount_usd for a in phase1_zones)
        
        plan['phases'].append({
            'phase': 1,
            'duration': '2 months',
            'zones': [a.zone for a in phase1_zones],
            'budget': phase1_budget,
            'focus': 'Emergency response in high vulnerability areas'
        })
        
        # Phase 2: Remaining zones
        phase2_zones = [a for a in allocations if a not in phase1_zones]
        phase2_budget = sum(a.total_amount_usd for a in phase2_zones)
        
        plan['phases'].append({
            'phase': 2,
            'duration': '4 months',
            'zones': [a.zone for a in phase2_zones],
            'budget': phase2_budget,
            'focus': 'Sustained support and market stabilization'
        })
        
        # Logistics planning
        for allocation in allocations:
            zone = allocation.zone
            
            plan['logistics'][zone] = {
                'cash_delivery': self._plan_cash_delivery(allocation, zone_data[zone]),
                'inkind_procurement': self._plan_inkind_procurement(allocation, zone_data[zone]),
                'distribution_points': self._plan_distribution_points(allocation, zone_data[zone])
            }
        
        # Monitoring framework
        plan['monitoring'] = {
            'indicators': [
                'Market price levels',
                'Exchange rate movements',
                'Beneficiary satisfaction',
                'Local trader inventory',
                'Security incidents'
            ],
            'frequency': 'Weekly',
            'reporting': 'Monthly dashboard with early warning triggers'
        }
        
        # Risk mitigation
        plan['risk_mitigation'] = self._identify_risks_and_mitigation(
            allocations, zone_data, exchange_rates
        )
        
        return plan
    
    def _plan_cash_delivery(self, allocation: AidAllocation, zone_info: Dict) -> Dict:
        """Plan cash delivery mechanism."""
        if allocation.cash_amount_usd < 0.1 * allocation.total_amount_usd:
            return {'method': 'none', 'reason': 'Minimal cash component'}
        
        # Select delivery mechanism based on zone
        if zone_info.get('mobile_money_coverage', 0) > 0.6:
            return {
                'method': 'mobile_money',
                'provider': 'YemenMobile Cash',
                'fee_rate': 0.02,
                'coverage': zone_info['mobile_money_coverage']
            }
        elif zone_info.get('bank_access', 0) > 0.4:
            return {
                'method': 'bank_transfer',
                'provider': 'Local banks',
                'fee_rate': 0.03,
                'requirements': 'Valid ID and bank account'
            }
        else:
            return {
                'method': 'direct_distribution',
                'provider': 'Implementing partner',
                'fee_rate': 0.05,
                'security': 'Required armed escort'
            }
    
    def _plan_inkind_procurement(self, allocation: AidAllocation, zone_info: Dict) -> Dict:
        """Plan in-kind procurement strategy."""
        if allocation.inkind_amount_usd < 0.1 * allocation.total_amount_usd:
            return {'method': 'none', 'reason': 'Minimal in-kind component'}
        
        # Procurement strategy
        if zone_info.get('local_market_capacity', 0) > 0.7:
            return {
                'method': 'local_procurement',
                'source': 'Local traders and farmers',
                'lead_time': '2 weeks',
                'advantages': 'Supports local economy'
            }
        else:
            return {
                'method': 'regional_procurement',
                'source': 'Regional suppliers (Dubai, Djibouti)',
                'lead_time': '4-6 weeks',
                'logistics': 'Sea freight to Aden/Hodeidah'
            }
    
    def _plan_distribution_points(self, allocation: AidAllocation, zone_info: Dict) -> List[Dict]:
        """Plan distribution points."""
        n_points = max(1, allocation.beneficiaries // 5000)  # One point per 5000 beneficiaries
        
        points = []
        for i in range(n_points):
            points.append({
                'id': f"{allocation.zone}_DP_{i+1}",
                'type': 'fixed' if zone_info.get('security_level', 5) < 3 else 'mobile',
                'capacity': allocation.beneficiaries // n_points,
                'staffing': 10 if allocation.modality == 'mixed' else 5
            })
        
        return points
    
    def _identify_risks_and_mitigation(self,
                                     allocations: List[AidAllocation],
                                     zone_data: Dict[str, Dict],
                                     exchange_rates: pd.DataFrame) -> Dict:
        """Identify risks and mitigation strategies."""
        risks = {
            'high': [],
            'medium': [],
            'low': []
        }
        
        # Currency devaluation risk
        for zone in exchange_rates.columns:
            volatility = exchange_rates[zone].pct_change().std()
            
            if volatility > 0.2:
                risks['high'].append({
                    'risk': f'Currency devaluation in {zone}',
                    'impact': 'Reduced purchasing power of cash transfers',
                    'mitigation': 'Increase in-kind share, implement rapid disbursement',
                    'contingency': 'Emergency commodity distribution'
                })
        
        # Market disruption risk
        large_cash_zones = [a.zone for a in allocations 
                           if a.cash_amount_usd > 0.5 * a.total_amount_usd]
        
        if large_cash_zones:
            risks['medium'].append({
                'risk': 'Market price inflation from cash injection',
                'impact': 'Reduced effectiveness, harm to non-beneficiaries',
                'mitigation': 'Phased disbursement, market monitoring',
                'contingency': 'Switch to in-kind if prices rise >15%'
            })
        
        # Security risks
        for zone, info in zone_data.items():
            if info.get('conflict_intensity', 0) > 7:
                risks['high'].append({
                    'risk': f'Security deterioration in {zone}',
                    'impact': 'Inability to deliver aid',
                    'mitigation': 'Pre-positioned supplies, remote programming',
                    'contingency': 'Shift to adjacent accessible areas'
                })
        
        return risks
    
    def _calculate_naive_welfare(self, budget: float, zone_data: Dict[str, Dict]) -> float:
        """Calculate welfare from naive equal allocation."""
        n_zones = len(zone_data)
        equal_alloc = budget / n_zones
        
        total_welfare = 0
        for zone, info in zone_data.items():
            # Simple welfare calculation
            welfare = np.log1p(equal_alloc / info['population']) * info['population']
            total_welfare += welfare
        
        return total_welfare
    
    def _fallback_allocation(self,
                           budget: float,
                           zone_data: Dict[str, Dict],
                           need_assessment: pd.DataFrame) -> OptimizationResults:
        """Simple fallback allocation if optimization fails."""
        allocations = []
        
        # Allocate proportionally to need
        if not need_assessment.empty:
            total_need = need_assessment['total_need_usd'].sum()
            
            for zone in zone_data:
                if zone in need_assessment.index:
                    zone_need = need_assessment.loc[zone, 'total_need_usd']
                    zone_budget = budget * (zone_need / total_need)
                else:
                    zone_budget = budget / len(zone_data)
                
                # 70/30 cash/in-kind split by default
                allocations.append(AidAllocation(
                    zone=zone,
                    cash_amount_usd=zone_budget * 0.7,
                    inkind_amount_usd=zone_budget * 0.3,
                    total_amount_usd=zone_budget,
                    beneficiaries=int(zone_budget / self.min_beneficiary_transfer),
                    modality='mixed',
                    expected_impact=50,
                    cost_effectiveness=1.0
                ))
        
        return OptimizationResults(
            allocations=allocations,
            total_budget_usd=budget,
            total_beneficiaries=sum(a.beneficiaries for a in allocations),
            expected_welfare_gain=sum(a.expected_impact for a in allocations),
            cost_per_beneficiary=budget / sum(a.beneficiaries for a in allocations),
            efficiency_gain_pct=0,
            sensitivity_analysis=pd.DataFrame(),
            implementation_plan={}
        )
    
    def simulate_impact(self,
                       allocation: OptimizationResults,
                       market_model: 'MarketModel',
                       time_periods: int = 12) -> pd.DataFrame:
        """
        Simulate impact of aid allocation over time.
        
        Args:
            allocation: Optimized allocation results
            market_model: Market simulation model
            time_periods: Number of periods to simulate
            
        Returns:
            DataFrame with simulated outcomes
        """
        results = []
        
        for t in range(time_periods):
            period_results = {
                'period': t,
                'total_welfare': 0,
                'price_level': 100,
                'beneficiary_welfare': 0
            }
            
            # Simulate each zone
            for alloc in allocation.allocations:
                # Market price response
                cash_shock = alloc.cash_amount_usd / time_periods
                price_response = 1 + (cash_shock / 1e6) * 0.05  # 5% per million
                
                # Welfare impact
                beneficiary_welfare = alloc.expected_impact * (1 - t * 0.05)  # Decay
                
                period_results['total_welfare'] += beneficiary_welfare
                period_results['price_level'] *= price_response
            
            results.append(period_results)
        
        return pd.DataFrame(results)