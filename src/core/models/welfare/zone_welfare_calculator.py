"""
Zone-specific welfare calculations accounting for currency fragmentation.

Implements welfare analysis that properly accounts for different
purchasing power and price levels across currency zones.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy import integrate, optimize
import warnings

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class WelfareMetrics:
    """Welfare metrics for a currency zone."""
    zone: str
    population: int
    avg_income_local: float
    avg_income_usd: float
    price_index_local: float
    price_index_usd: float
    purchasing_power: float
    welfare_index: float
    poverty_rate: float
    food_security_score: float


@dataclass
class ZoneWelfareResults:
    """Results from zone-specific welfare analysis."""
    zone_metrics: Dict[str, WelfareMetrics]
    welfare_gaps: pd.DataFrame
    inequality_measures: Dict[str, float]
    vulnerability_scores: pd.DataFrame
    policy_recommendations: List[Dict]
    total_welfare_loss: float


class ZoneSpecificWelfareCalculator:
    """
    Calculates welfare metrics accounting for currency zones.
    
    Key innovations:
    - Separate welfare calculations for each currency zone
    - Proper conversion between YER and USD valuations
    - Accounts for differential purchasing power
    - Identifies zone-specific vulnerabilities
    """
    
    def __init__(self,
                 poverty_line_usd: float = 1.90,
                 food_basket_items: Optional[List[str]] = None,
                 utility_function: str = 'cobb_douglas'):
        """
        Initialize welfare calculator.
        
        Args:
            poverty_line_usd: International poverty line in USD/day
            food_basket_items: List of essential food items
            utility_function: Type of utility function to use
        """
        self.poverty_line_usd = poverty_line_usd
        self.food_basket_items = food_basket_items or [
            'wheat_flour', 'rice', 'sugar', 'oil', 'beans',
            'eggs', 'milk_powder', 'tomatoes', 'onions'
        ]
        self.utility_function = utility_function
        
        # Utility parameters (from literature)
        self.utility_params = {
            'cobb_douglas': {
                'alpha_food': 0.6,  # Food share in utility
                'alpha_nonfood': 0.4,
                'subsistence_food': 0.7  # Minimum food requirement
            },
            'stone_geary': {
                'gamma_food': 0.5,  # Subsistence levels
                'gamma_nonfood': 0.3,
                'beta_food': 0.65,  # Budget shares above subsistence
                'beta_nonfood': 0.35
            }
        }
    
    def calculate_zone_welfare(self,
                             price_data: pd.DataFrame,
                             income_data: pd.DataFrame,
                             exchange_rates: pd.DataFrame,
                             population_data: pd.DataFrame,
                             zone_mapping: pd.DataFrame) -> ZoneWelfareResults:
        """
        Calculate welfare metrics for each currency zone.
        
        Args:
            price_data: Prices by market and commodity
            income_data: Income/expenditure data by location
            exchange_rates: Exchange rates by zone over time
            population_data: Population by location
            zone_mapping: Mapping of locations to currency zones
            
        Returns:
            Comprehensive welfare analysis results
        """
        logger.info("Calculating zone-specific welfare metrics")
        
        # Map markets to zones
        market_zones = self._map_markets_to_zones(price_data.columns, zone_mapping)
        
        # Calculate metrics for each zone
        zone_metrics = {}
        
        for zone in market_zones.unique():
            logger.info(f"Processing welfare for zone: {zone}")
            
            # Filter data for this zone
            zone_markets = market_zones[market_zones == zone].index
            zone_prices = price_data[zone_markets]
            zone_income = income_data[income_data.index.isin(zone_markets)]
            zone_population = population_data[population_data.index.isin(zone_markets)]
            
            # Get zone exchange rate
            zone_exchange_rate = exchange_rates[zone].mean()
            
            # Calculate welfare metrics
            metrics = self._calculate_zone_metrics(
                zone, zone_prices, zone_income, zone_population, zone_exchange_rate
            )
            
            zone_metrics[zone] = metrics
        
        # Calculate welfare gaps between zones
        welfare_gaps = self._calculate_welfare_gaps(zone_metrics)
        
        # Calculate inequality measures
        inequality = self._calculate_inequality_measures(zone_metrics)
        
        # Calculate vulnerability scores
        vulnerability = self._calculate_vulnerability_scores(
            zone_metrics, price_data, exchange_rates
        )
        
        # Generate policy recommendations
        recommendations = self._generate_policy_recommendations(
            zone_metrics, welfare_gaps, vulnerability
        )
        
        # Total welfare loss from fragmentation
        total_loss = self._calculate_total_welfare_loss(zone_metrics, welfare_gaps)
        
        return ZoneWelfareResults(
            zone_metrics=zone_metrics,
            welfare_gaps=welfare_gaps,
            inequality_measures=inequality,
            vulnerability_scores=vulnerability,
            policy_recommendations=recommendations,
            total_welfare_loss=total_loss
        )
    
    def _map_markets_to_zones(self,
                            markets: List[str],
                            zone_mapping: pd.DataFrame) -> pd.Series:
        """Map markets to currency zones."""
        # Simplified - would use actual mapping
        # Assume 40% Houthi, 60% Government zones
        n_markets = len(markets)
        n_houthi = int(n_markets * 0.4)
        
        zones = ['houthi'] * n_houthi + ['government'] * (n_markets - n_houthi)
        return pd.Series(zones, index=markets)
    
    def _calculate_zone_metrics(self,
                              zone: str,
                              prices: pd.DataFrame,
                              income: pd.DataFrame,
                              population: pd.DataFrame,
                              exchange_rate: float) -> WelfareMetrics:
        """Calculate welfare metrics for a single zone."""
        # Population
        total_pop = population.sum().sum() if not population.empty else 1000000
        
        # Average income (local currency)
        avg_income_local = income.mean().mean() if not income.empty else 50000
        avg_income_usd = avg_income_local / exchange_rate
        
        # Price indices
        if not prices.empty:
            # Food basket cost in local currency
            basket_cost_local = self._calculate_food_basket_cost(prices)
            basket_cost_usd = basket_cost_local / exchange_rate
            
            # Price index (relative to reference)
            reference_basket_usd = 5.0  # USD per day reference
            price_index_local = basket_cost_local / (reference_basket_usd * exchange_rate)
            price_index_usd = basket_cost_usd / reference_basket_usd
        else:
            price_index_local = 1.0
            price_index_usd = 1.0
            basket_cost_local = 5.0 * exchange_rate
            basket_cost_usd = 5.0
        
        # Purchasing power (income relative to food basket cost)
        purchasing_power = avg_income_usd / basket_cost_usd
        
        # Welfare index (composite measure)
        welfare_index = self._calculate_welfare_index(
            avg_income_usd, price_index_usd, purchasing_power
        )
        
        # Poverty rate
        poverty_rate = self._estimate_poverty_rate(
            income, exchange_rate, self.poverty_line_usd
        )
        
        # Food security score (0-100)
        food_security = self._calculate_food_security_score(
            purchasing_power, price_index_usd
        )
        
        return WelfareMetrics(
            zone=zone,
            population=int(total_pop),
            avg_income_local=float(avg_income_local),
            avg_income_usd=float(avg_income_usd),
            price_index_local=float(price_index_local),
            price_index_usd=float(price_index_usd),
            purchasing_power=float(purchasing_power),
            welfare_index=float(welfare_index),
            poverty_rate=float(poverty_rate),
            food_security_score=float(food_security)
        )
    
    def _calculate_food_basket_cost(self, prices: pd.DataFrame) -> float:
        """Calculate cost of basic food basket."""
        # Daily requirements (simplified)
        daily_requirements = {
            'wheat_flour': 0.3,  # kg
            'rice': 0.2,         # kg
            'oil': 0.05,         # liters
            'sugar': 0.05,       # kg
            'beans': 0.1         # kg
        }
        
        total_cost = 0
        for item, quantity in daily_requirements.items():
            if item in prices.index:
                # Use median price across markets
                item_price = prices.loc[item].median()
                total_cost += item_price * quantity
        
        return total_cost if total_cost > 0 else 2500  # Default YER
    
    def _calculate_welfare_index(self,
                               income_usd: float,
                               price_index: float,
                               purchasing_power: float) -> float:
        """Calculate composite welfare index (0-100)."""
        # Normalize components
        income_score = np.clip(income_usd / 10, 0, 100)  # $10/day = 100
        price_score = np.clip(100 / price_index, 0, 100)  # Lower prices = higher score
        power_score = np.clip(purchasing_power * 20, 0, 100)  # 5x basket = 100
        
        # Weighted average
        welfare = 0.4 * income_score + 0.3 * price_score + 0.3 * power_score
        
        return welfare
    
    def _estimate_poverty_rate(self,
                             income_data: pd.DataFrame,
                             exchange_rate: float,
                             poverty_line: float) -> float:
        """Estimate poverty rate in the zone."""
        if income_data.empty:
            # Use zone-specific estimates
            if exchange_rate < 1000:  # Houthi zone (stable rate)
                return 0.75  # 75% poverty rate
            else:  # Government zone (depreciated rate)
                return 0.85  # 85% poverty rate
        
        # Convert income to USD
        income_usd = income_data / exchange_rate
        
        # Calculate percentage below poverty line
        poverty_rate = (income_usd < poverty_line).mean().mean()
        
        return poverty_rate
    
    def _calculate_food_security_score(self,
                                     purchasing_power: float,
                                     price_index: float) -> float:
        """Calculate food security score (0-100)."""
        # Based on ability to afford food basket
        affordability = purchasing_power / price_index
        
        # Transform to 0-100 scale
        if affordability >= 2:  # Can afford 2x minimum
            return 100
        elif affordability >= 1:  # Can afford minimum
            return 50 + affordability * 25
        else:  # Cannot afford minimum
            return affordability * 50
    
    def _calculate_welfare_gaps(self,
                              zone_metrics: Dict[str, WelfareMetrics]) -> pd.DataFrame:
        """Calculate welfare gaps between zones."""
        zones = list(zone_metrics.keys())
        n_zones = len(zones)
        
        # Create matrix of welfare gaps
        gaps = pd.DataFrame(
            np.zeros((n_zones, n_zones)),
            index=zones,
            columns=zones
        )
        
        for i, zone1 in enumerate(zones):
            for j, zone2 in enumerate(zones):
                if i != j:
                    # Welfare gap as percentage difference
                    welfare1 = zone_metrics[zone1].welfare_index
                    welfare2 = zone_metrics[zone2].welfare_index
                    
                    gap = (welfare1 - welfare2) / welfare2 * 100
                    gaps.loc[zone1, zone2] = gap
        
        return gaps
    
    def _calculate_inequality_measures(self,
                                     zone_metrics: Dict[str, WelfareMetrics]) -> Dict[str, float]:
        """Calculate various inequality measures."""
        # Extract welfare indices and populations
        welfares = [m.welfare_index for m in zone_metrics.values()]
        populations = [m.population for m in zone_metrics.values()]
        
        # Population-weighted welfare
        total_pop = sum(populations)
        weights = [p / total_pop for p in populations]
        
        # Gini coefficient (simplified)
        gini = self._calculate_gini(welfares, weights)
        
        # Theil index
        theil = self._calculate_theil(welfares, weights)
        
        # Coefficient of variation
        weighted_mean = sum(w * v for w, v in zip(weights, welfares))
        weighted_var = sum(w * (v - weighted_mean)**2 for w, v in zip(weights, welfares))
        cv = np.sqrt(weighted_var) / weighted_mean if weighted_mean > 0 else 0
        
        # 90/10 ratio
        sorted_welfares = sorted(welfares)
        if len(sorted_welfares) >= 2:
            ratio_90_10 = sorted_welfares[-1] / sorted_welfares[0]
        else:
            ratio_90_10 = 1.0
        
        return {
            'gini_coefficient': gini,
            'theil_index': theil,
            'coefficient_variation': cv,
            'ratio_90_10': ratio_90_10,
            'max_welfare_gap': max(welfares) - min(welfares) if welfares else 0
        }
    
    def _calculate_gini(self, values: List[float], weights: List[float]) -> float:
        """Calculate Gini coefficient."""
        # Sort by values
        sorted_pairs = sorted(zip(values, weights))
        values_sorted = [v for v, _ in sorted_pairs]
        weights_sorted = [w for _, w in sorted_pairs]
        
        # Calculate cumulative shares
        cumsum_weights = np.cumsum(weights_sorted)
        cumsum_weighted_values = np.cumsum([v * w for v, w in sorted_pairs])
        
        if cumsum_weighted_values[-1] == 0:
            return 0
        
        # Normalize
        cumsum_weighted_values = cumsum_weighted_values / cumsum_weighted_values[-1]
        
        # Calculate area under Lorenz curve
        area = 0
        for i in range(1, len(cumsum_weights)):
            area += (cumsum_weights[i] - cumsum_weights[i-1]) * \
                   (cumsum_weighted_values[i] + cumsum_weighted_values[i-1]) / 2
        
        # Gini = 1 - 2 * area under Lorenz curve
        return 1 - 2 * area
    
    def _calculate_theil(self, values: List[float], weights: List[float]) -> float:
        """Calculate Theil index."""
        # Population-weighted mean
        mean = sum(v * w for v, w in zip(values, weights))
        
        if mean == 0:
            return 0
        
        # Theil index
        theil = sum(
            w * (v / mean) * np.log(v / mean) 
            for v, w in zip(values, weights) 
            if v > 0
        )
        
        return theil
    
    def _calculate_vulnerability_scores(self,
                                      zone_metrics: Dict[str, WelfareMetrics],
                                      price_data: pd.DataFrame,
                                      exchange_rates: pd.DataFrame) -> pd.DataFrame:
        """Calculate vulnerability scores for each zone."""
        scores = []
        
        for zone, metrics in zone_metrics.items():
            # Price volatility
            zone_prices = price_data.mean(axis=0)  # Simplified
            price_volatility = zone_prices.pct_change().std() if len(zone_prices) > 1 else 0.1
            
            # Exchange rate volatility
            if zone in exchange_rates.columns:
                exr_volatility = exchange_rates[zone].pct_change().std()
            else:
                exr_volatility = 0.05
            
            # Food insecurity risk
            food_risk = 100 - metrics.food_security_score
            
            # Poverty depth
            poverty_depth = metrics.poverty_rate * 100
            
            # Composite vulnerability score
            vulnerability = (
                0.3 * price_volatility * 100 +
                0.3 * exr_volatility * 100 +
                0.25 * food_risk +
                0.15 * poverty_depth
            )
            
            scores.append({
                'zone': zone,
                'vulnerability_score': vulnerability,
                'price_volatility': price_volatility,
                'exchange_volatility': exr_volatility,
                'food_insecurity_risk': food_risk,
                'poverty_depth': poverty_depth
            })
        
        return pd.DataFrame(scores)
    
    def _generate_policy_recommendations(self,
                                       zone_metrics: Dict[str, WelfareMetrics],
                                       welfare_gaps: pd.DataFrame,
                                       vulnerability: pd.DataFrame) -> List[Dict]:
        """Generate zone-specific policy recommendations."""
        recommendations = []
        
        for zone, metrics in zone_metrics.items():
            zone_vuln = vulnerability[vulnerability['zone'] == zone].iloc[0]
            
            rec = {
                'zone': zone,
                'priority': 'high' if zone_vuln['vulnerability_score'] > 70 else 'medium',
                'interventions': []
            }
            
            # Cash vs in-kind recommendation
            if zone_vuln['exchange_volatility'] > 0.1:
                rec['interventions'].append({
                    'type': 'in_kind_aid',
                    'reason': 'High exchange rate volatility',
                    'expected_impact': 'Stabilize purchasing power'
                })
            else:
                rec['interventions'].append({
                    'type': 'cash_transfer',
                    'reason': 'Stable exchange rate',
                    'expected_impact': 'Maximize beneficiary choice'
                })
            
            # Market support
            if metrics.price_index_usd > 1.5:
                rec['interventions'].append({
                    'type': 'market_support',
                    'reason': 'High price levels',
                    'expected_impact': 'Reduce prices by 15-20%'
                })
            
            # Exchange rate stabilization
            if zone == 'government' and zone_vuln['exchange_volatility'] > 0.15:
                rec['interventions'].append({
                    'type': 'forex_intervention',
                    'reason': 'Extreme currency depreciation',
                    'expected_impact': 'Reduce volatility by 30%'
                })
            
            recommendations.append(rec)
        
        return recommendations
    
    def _calculate_total_welfare_loss(self,
                                    zone_metrics: Dict[str, WelfareMetrics],
                                    welfare_gaps: pd.DataFrame) -> float:
        """Calculate total welfare loss from fragmentation."""
        # Baseline: unified market welfare (best zone welfare)
        best_welfare = max(m.welfare_index for m in zone_metrics.values())
        
        # Current welfare (population-weighted average)
        total_pop = sum(m.population for m in zone_metrics.values())
        current_welfare = sum(
            m.welfare_index * m.population / total_pop 
            for m in zone_metrics.values()
        )
        
        # Loss as percentage
        welfare_loss = (best_welfare - current_welfare) / best_welfare * 100
        
        return welfare_loss
    
    def calculate_utility(self,
                        consumption: Dict[str, float],
                        prices: Dict[str, float],
                        income: float) -> float:
        """
        Calculate utility from consumption bundle.
        
        Args:
            consumption: Quantities consumed by commodity
            prices: Prices by commodity
            income: Total income/budget
            
        Returns:
            Utility level
        """
        if self.utility_function == 'cobb_douglas':
            return self._cobb_douglas_utility(consumption)
        elif self.utility_function == 'stone_geary':
            return self._stone_geary_utility(consumption)
        else:
            raise ValueError(f"Unknown utility function: {self.utility_function}")
    
    def _cobb_douglas_utility(self, consumption: Dict[str, float]) -> float:
        """Cobb-Douglas utility function."""
        params = self.utility_params['cobb_douglas']
        
        # Separate food and non-food
        food_items = [item for item in consumption if item in self.food_basket_items]
        food_consumption = sum(consumption.get(item, 0) for item in food_items)
        nonfood_consumption = sum(
            consumption[item] for item in consumption 
            if item not in food_items
        )
        
        # U = C_food^alpha * C_nonfood^(1-alpha)
        if food_consumption > 0 and nonfood_consumption > 0:
            utility = (food_consumption ** params['alpha_food']) * \
                     (nonfood_consumption ** params['alpha_nonfood'])
        else:
            utility = 0
        
        return utility
    
    def _stone_geary_utility(self, consumption: Dict[str, float]) -> float:
        """Stone-Geary (Linear Expenditure System) utility."""
        params = self.utility_params['stone_geary']
        
        # Separate food and non-food
        food_items = [item for item in consumption if item in self.food_basket_items]
        food_consumption = sum(consumption.get(item, 0) for item in food_items)
        nonfood_consumption = sum(
            consumption[item] for item in consumption 
            if item not in food_items
        )
        
        # Check subsistence
        if food_consumption < params['gamma_food']:
            return -np.inf  # Below subsistence
        
        # U = beta_food * ln(C_food - gamma_food) + beta_nonfood * ln(C_nonfood - gamma_nonfood)
        utility = 0
        
        if food_consumption > params['gamma_food']:
            utility += params['beta_food'] * np.log(food_consumption - params['gamma_food'])
        
        if nonfood_consumption > params['gamma_nonfood']:
            utility += params['beta_nonfood'] * np.log(nonfood_consumption - params['gamma_nonfood'])
        
        return utility