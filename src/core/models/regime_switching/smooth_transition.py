"""
Smooth Transition Autoregressive (STAR) models for gradual regime changes.

Implements STAR models that capture smooth transitions between currency regimes,
particularly useful for modeling gradual fragmentation processes.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Callable
from dataclasses import dataclass
from scipy.optimize import minimize, differential_evolution
from scipy.stats import norm
import warnings

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class STARResults:
    """Results from STAR model estimation."""
    linear_params: np.ndarray
    nonlinear_params: np.ndarray
    threshold: float
    smoothness: float
    transition_variable: str
    transition_function: Callable
    fitted_values: pd.Series
    residuals: pd.Series
    log_likelihood: float
    aic: float
    bic: float
    r_squared: float
    regime_probabilities: pd.Series


class SmoothTransitionModel:
    """
    Smooth Transition Autoregressive model for gradual regime changes.
    
    Captures smooth transitions between integrated and fragmented states,
    allowing for gradual changes rather than abrupt switches.
    """
    
    def __init__(self, 
                 transition_type: str = 'logistic',
                 max_lags: int = 4,
                 transition_variable: str = 'self'):
        """
        Initialize STAR model.
        
        Args:
            transition_type: 'logistic' or 'exponential' transition function
            max_lags: Maximum number of lags to consider
            transition_variable: Variable driving transitions ('self' or external)
        """
        self.transition_type = transition_type
        self.max_lags = max_lags
        self.transition_variable = transition_variable
        self.results = None
        
        # Define transition functions
        self.transition_functions = {
            'logistic': self._logistic_transition,
            'exponential': self._exponential_transition
        }
    
    def _logistic_transition(self, z: np.ndarray, gamma: float, c: float) -> np.ndarray:
        """
        Logistic transition function.
        
        G(z; γ, c) = 1 / (1 + exp(-γ(z - c)))
        
        Args:
            z: Transition variable
            gamma: Smoothness parameter (higher = more abrupt)
            c: Threshold/location parameter
        """
        with warnings.catch_warnings():
            warnings.filterwarnings('ignore', category=RuntimeWarning)
            return 1 / (1 + np.exp(-gamma * (z - c)))
    
    def _exponential_transition(self, z: np.ndarray, gamma: float, c: float) -> np.ndarray:
        """
        Exponential transition function.
        
        G(z; γ, c) = 1 - exp(-γ(z - c)²)
        
        Args:
            z: Transition variable
            gamma: Smoothness parameter
            c: Location parameter
        """
        return 1 - np.exp(-gamma * (z - c)**2)
    
    def prepare_data(self, spreads: pd.Series, 
                    transition_var: Optional[pd.Series] = None) -> Tuple[pd.DataFrame, pd.Series]:
        """
        Prepare data for STAR estimation.
        
        Args:
            spreads: Exchange rate spreads time series
            transition_var: External transition variable (if not self-transition)
            
        Returns:
            Tuple of (features DataFrame, target Series)
        """
        # Create lagged variables
        data = pd.DataFrame(index=spreads.index)
        data['y'] = spreads
        
        for lag in range(1, self.max_lags + 1):
            data[f'y_lag{lag}'] = spreads.shift(lag)
        
        # Add transition variable
        if self.transition_variable == 'self':
            data['z'] = spreads.shift(1)  # Self-exciting
        elif transition_var is not None:
            data['z'] = transition_var
        else:
            raise ValueError("External transition variable required")
        
        # Drop missing values
        data = data.dropna()
        
        # Separate features and target
        y = data['y']
        X = data.drop('y', axis=1)
        
        return X, y
    
    def fit(self, spreads: pd.Series, 
            transition_var: Optional[pd.Series] = None) -> STARResults:
        """
        Fit STAR model to exchange rate spreads.
        
        Args:
            spreads: Time series of exchange rate spreads
            transition_var: External transition variable (optional)
            
        Returns:
            Model estimation results
        """
        logger.info(f"Fitting {self.transition_type} STAR model")
        
        # Prepare data
        X, y = self.prepare_data(spreads, transition_var)
        
        # Get optimal lag order
        optimal_lags = self._select_lags(X, y)
        
        # Subset features to optimal lags
        lag_cols = [f'y_lag{i}' for i in range(1, optimal_lags + 1)]
        X_opt = X[lag_cols + ['z']]
        
        # Estimate model parameters
        params = self._estimate_parameters(X_opt, y)
        
        # Extract results
        self.results = self._extract_results(X_opt, y, params)
        
        logger.info(f"STAR model fit complete. R²: {self.results.r_squared:.4f}")
        
        return self.results
    
    def _select_lags(self, X: pd.DataFrame, y: pd.Series) -> int:
        """Select optimal number of lags using information criteria."""
        best_aic = np.inf
        best_lags = 1
        
        for lags in range(1, self.max_lags + 1):
            lag_cols = [f'y_lag{i}' for i in range(1, lags + 1)]
            X_subset = X[lag_cols + ['z']]
            
            # Quick linear fit for lag selection
            from sklearn.linear_model import LinearRegression
            model = LinearRegression()
            model.fit(X_subset.drop('z', axis=1), y)
            
            # Calculate AIC
            resid = y - model.predict(X_subset.drop('z', axis=1))
            sse = np.sum(resid**2)
            n = len(y)
            k = lags + 1
            
            aic = n * np.log(sse/n) + 2 * k
            
            if aic < best_aic:
                best_aic = aic
                best_lags = lags
        
        logger.info(f"Selected {best_lags} lags based on AIC")
        return best_lags
    
    def _estimate_parameters(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """
        Estimate STAR model parameters using grid search + optimization.
        """
        n_linear = X.shape[1] - 1  # Exclude transition variable
        
        # Define objective function
        def objective(params):
            # Split parameters
            beta1 = params[:n_linear]
            beta2 = params[n_linear:2*n_linear]
            gamma = params[-2]
            c = params[-1]
            
            # Get transition variable
            z = X['z'].values
            
            # Calculate transition function
            g = self.transition_functions[self.transition_type](z, gamma, c)
            
            # Calculate fitted values
            X_linear = X.drop('z', axis=1).values
            y_fit = X_linear @ beta1 + g * (X_linear @ beta2)
            
            # Calculate residuals
            resid = y.values - y_fit
            
            # Return negative log-likelihood (to minimize)
            sigma2 = np.var(resid)
            n = len(y)
            
            return n/2 * np.log(2*np.pi*sigma2) + np.sum(resid**2) / (2*sigma2)
        
        # Grid search for initial values of gamma and c
        z_values = X['z'].values
        c_grid = np.percentile(z_values, [20, 40, 50, 60, 80])
        gamma_grid = [0.5, 1.0, 2.0, 5.0, 10.0]
        
        best_obj = np.inf
        best_init = None
        
        for c_init in c_grid:
            for gamma_init in gamma_grid:
                # Linear model for initial beta values
                from sklearn.linear_model import LinearRegression
                lr = LinearRegression()
                lr.fit(X.drop('z', axis=1), y)
                
                # Initial parameter vector
                init_params = np.concatenate([
                    lr.coef_,  # beta1
                    lr.coef_ * 0.5,  # beta2 (start different from beta1)
                    [gamma_init, c_init]
                ])
                
                # Evaluate objective
                obj_val = objective(init_params)
                
                if obj_val < best_obj:
                    best_obj = obj_val
                    best_init = init_params
        
        # Optimize from best grid point
        bounds = [(None, None)] * (2 * n_linear) + [(0.1, 50), (None, None)]
        
        result = minimize(
            objective,
            best_init,
            method='L-BFGS-B',
            bounds=bounds,
            options={'maxiter': 1000}
        )
        
        # Return parameter dictionary
        params = result.x
        return {
            'beta1': params[:n_linear],
            'beta2': params[n_linear:2*n_linear],
            'gamma': params[-2],
            'c': params[-1],
            'success': result.success,
            'obj_value': result.fun
        }
    
    def _extract_results(self, X: pd.DataFrame, y: pd.Series, params: Dict) -> STARResults:
        """Extract and organize model results."""
        # Get parameters
        beta1 = params['beta1']
        beta2 = params['beta2']
        gamma = params['gamma']
        c = params['c']
        
        # Calculate transition function values
        z = X['z'].values
        g = self.transition_functions[self.transition_type](z, gamma, c)
        
        # Calculate fitted values
        X_linear = X.drop('z', axis=1).values
        y_fit = X_linear @ beta1 + g * (X_linear @ beta2)
        y_fit = pd.Series(y_fit, index=y.index)
        
        # Calculate residuals
        residuals = y - y_fit
        
        # Calculate R-squared
        ss_res = np.sum(residuals**2)
        ss_tot = np.sum((y - y.mean())**2)
        r_squared = 1 - ss_res / ss_tot
        
        # Log-likelihood
        n = len(y)
        sigma2 = np.var(residuals)
        log_lik = -n/2 * np.log(2*np.pi*sigma2) - np.sum(residuals**2) / (2*sigma2)
        
        # Information criteria
        k = len(beta1) + len(beta2) + 2  # Total parameters
        aic = -2 * log_lik + 2 * k
        bic = -2 * log_lik + k * np.log(n)
        
        # Regime probabilities (transition function values)
        regime_probs = pd.Series(g, index=y.index, name='fragmentation_probability')
        
        return STARResults(
            linear_params=beta1,
            nonlinear_params=beta2,
            threshold=c,
            smoothness=gamma,
            transition_variable=self.transition_variable,
            transition_function=self.transition_functions[self.transition_type],
            fitted_values=y_fit,
            residuals=residuals,
            log_likelihood=log_lik,
            aic=aic,
            bic=bic,
            r_squared=r_squared,
            regime_probabilities=regime_probs
        )
    
    def predict(self, X_new: pd.DataFrame) -> pd.Series:
        """
        Predict using fitted STAR model.
        
        Args:
            X_new: New data with same structure as training data
            
        Returns:
            Predicted values
        """
        if self.results is None:
            raise ValueError("Model must be fitted first")
        
        # Get transition variable
        z = X_new['z'].values
        
        # Calculate transition function
        g = self.results.transition_function(z, self.results.smoothness, self.results.threshold)
        
        # Calculate predictions
        X_linear = X_new.drop('z', axis=1).values
        y_pred = X_linear @ self.results.linear_params + g * (X_linear @ self.results.nonlinear_params)
        
        return pd.Series(y_pred, index=X_new.index)
    
    def get_regime_contributions(self) -> pd.DataFrame:
        """
        Decompose fitted values into linear and nonlinear regime contributions.
        
        Returns:
            DataFrame with regime contributions
        """
        if self.results is None:
            raise ValueError("Model must be fitted first")
        
        # This would require storing X from fitting
        # For now, return regime probabilities
        return pd.DataFrame({
            'integrated_regime': 1 - self.results.regime_probabilities,
            'fragmented_regime': self.results.regime_probabilities
        })
    
    def test_linearity(self, spreads: pd.Series, 
                       transition_var: Optional[pd.Series] = None) -> Dict:
        """
        Test null hypothesis of linearity against STAR alternative.
        
        Uses auxiliary regression approach for testing.
        
        Args:
            spreads: Exchange rate spreads
            transition_var: Transition variable
            
        Returns:
            Test results dictionary
        """
        # Prepare data
        X, y = self.prepare_data(spreads, transition_var)
        
        # Fit linear model
        from sklearn.linear_model import LinearRegression
        lr = LinearRegression()
        lr.fit(X.drop('z', axis=1), y)
        resid_linear = y - lr.predict(X.drop('z', axis=1))
        
        # Fit STAR model
        self.fit(spreads, transition_var)
        resid_star = self.results.residuals
        
        # Calculate test statistic
        n = len(y)
        rss_linear = np.sum(resid_linear**2)
        rss_star = np.sum(resid_star**2)
        
        # F-test
        k_star = len(self.results.linear_params) + len(self.results.nonlinear_params) + 2
        k_linear = len(self.results.linear_params)
        
        f_stat = ((rss_linear - rss_star) / (k_star - k_linear)) / (rss_star / (n - k_star))
        p_value = 1 - stats.f.cdf(f_stat, k_star - k_linear, n - k_star)
        
        return {
            'f_statistic': f_stat,
            'p_value': p_value,
            'reject_linearity': p_value < 0.05,
            'rss_linear': rss_linear,
            'rss_star': rss_star
        }