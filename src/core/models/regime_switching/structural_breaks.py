"""
Structural break detection using Bai-Perron methodology.

Implements methods to detect multiple structural breaks in
exchange rate relationships and market integration patterns.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy import stats
from itertools import combinations

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class BreakPoint:
    """Represents a structural break point."""
    date: pd.Timestamp
    confidence_interval: Tuple[pd.Timestamp, pd.Timestamp]
    magnitude: float
    type: str  # 'mean', 'variance', 'relationship'


@dataclass
class BaiPerronResults:
    """Results from Bai-Perron structural break test."""
    break_dates: List[pd.Timestamp]
    n_breaks: int
    break_points: List[BreakPoint]
    regime_parameters: Dict[int, Dict]
    test_statistics: Dict[str, float]
    information_criteria: Dict[str, float]
    residual_variance: Dict[int, float]


class BaiPerronBreakDetector:
    """
    Detects structural breaks using Bai-<PERSON>ron (1998, 2003) methodology.
    
    Identifies endogenous break points in time series relationships,
    particularly useful for detecting currency regime changes.
    """
    
    def __init__(self,
                 min_segment_pct: float = 0.15,
                 max_breaks: int = 5,
                 sig_level: float = 0.05):
        """
        Initialize break detector.
        
        Args:
            min_segment_pct: Minimum segment length as % of sample
            max_breaks: Maximum number of breaks to consider
            sig_level: Significance level for tests
        """
        self.min_segment_pct = min_segment_pct
        self.max_breaks = max_breaks
        self.sig_level = sig_level
        self.results = None
    
    def detect_breaks(self,
                     y: pd.Series,
                     X: pd.DataFrame,
                     break_type: str = 'coefficients') -> BaiPerronResults:
        """
        Detect structural breaks in regression relationship.
        
        Args:
            y: Dependent variable (e.g., price integration measure)
            X: Independent variables (e.g., exchange rates, conflict)
            break_type: 'coefficients', 'variance', or 'both'
            
        Returns:
            Structural break test results
        """
        logger.info(f"Detecting structural breaks with max {self.max_breaks} breaks")
        
        # Ensure datetime index
        if not isinstance(y.index, pd.DatetimeIndex):
            raise ValueError("Series must have datetime index")
        
        # Prepare data
        data = pd.DataFrame({'y': y})
        for col in X.columns:
            data[col] = X[col]
        data = data.dropna()
        
        # Sequential testing procedure
        n_breaks = 0
        break_dates = []
        
        for m in range(1, self.max_breaks + 1):
            logger.info(f"Testing for {m} break(s)")
            
            # Test m vs m-1 breaks
            if m == 1:
                # Test 0 vs 1 break
                test_stat, p_value = self._test_no_break_vs_breaks(data, X.columns)
                
                if p_value < self.sig_level:
                    # Find optimal break
                    break_date = self._find_single_break(data, X.columns)
                    break_dates.append(break_date)
                    n_breaks = 1
                else:
                    logger.info("No structural breaks detected")
                    break
            else:
                # Test m-1 vs m breaks
                test_stat, p_value = self._test_l_vs_lplus1_breaks(
                    data, X.columns, break_dates
                )
                
                if p_value < self.sig_level:
                    # Find additional break
                    new_break = self._find_additional_break(data, X.columns, break_dates)
                    break_dates.append(new_break)
                    break_dates.sort()
                    n_breaks = m
                else:
                    logger.info(f"No additional break detected. Stopping at {n_breaks} break(s)")
                    break
        
        # Estimate final model with detected breaks
        if n_breaks > 0:
            results = self._estimate_break_model(data, X.columns, break_dates, break_type)
        else:
            results = self._no_break_results(data, X.columns)
        
        self.results = results
        logger.info(f"Break detection complete. Found {n_breaks} break(s)")
        
        return results
    
    def _test_no_break_vs_breaks(self,
                                data: pd.DataFrame,
                                x_cols: List[str]) -> Tuple[float, float]:
        """Test null of no breaks against alternative of breaks."""
        n = len(data)
        min_seg = int(n * self.min_segment_pct)
        
        # Calculate sup F statistic
        f_stats = []
        
        for t in range(min_seg, n - min_seg):
            # Split sample
            data1 = data.iloc[:t]
            data2 = data.iloc[t:]
            
            # Estimate restricted (no break) model
            y_full = data['y'].values
            X_full = data[x_cols].values
            beta_r = np.linalg.lstsq(X_full, y_full, rcond=None)[0]
            ssr_r = np.sum((y_full - X_full @ beta_r)**2)
            
            # Estimate unrestricted (with break) model
            y1 = data1['y'].values
            X1 = data1[x_cols].values
            beta1 = np.linalg.lstsq(X1, y1, rcond=None)[0]
            ssr1 = np.sum((y1 - X1 @ beta1)**2)
            
            y2 = data2['y'].values
            X2 = data2[x_cols].values
            beta2 = np.linalg.lstsq(X2, y2, rcond=None)[0]
            ssr2 = np.sum((y2 - X2 @ beta2)**2)
            
            ssr_u = ssr1 + ssr2
            
            # F statistic
            k = len(x_cols)
            f_stat = ((ssr_r - ssr_u) / k) / (ssr_u / (n - 2*k))
            f_stats.append(f_stat)
        
        # Sup F statistic
        sup_f = max(f_stats)
        
        # Critical values (Andrews 1993 approximation)
        # These would normally come from tables
        crit_val = self._get_critical_value(k, 1, self.sig_level)
        p_value = 1 - stats.f.cdf(sup_f, k, n - 2*k)
        
        return sup_f, p_value
    
    def _test_l_vs_lplus1_breaks(self,
                                data: pd.DataFrame,
                                x_cols: List[str],
                                current_breaks: List[pd.Timestamp]) -> Tuple[float, float]:
        """Test l breaks vs l+1 breaks."""
        # Simplified implementation
        # In practice, would test each segment for additional break
        
        n = len(data)
        k = len(x_cols)
        l = len(current_breaks)
        
        # Estimate model with l breaks
        ssr_l = self._calculate_ssr_with_breaks(data, x_cols, current_breaks)
        
        # Find best additional break
        segments = self._get_segments(data.index, current_breaks)
        best_ssr = np.inf
        
        for seg_start, seg_end in segments:
            seg_data = data.loc[seg_start:seg_end]
            
            if len(seg_data) < 2 * int(n * self.min_segment_pct):
                continue
            
            # Test for break in this segment
            for t in seg_data.index[int(len(seg_data)*0.15):-int(len(seg_data)*0.15)]:
                test_breaks = current_breaks + [t]
                test_breaks.sort()
                
                ssr = self._calculate_ssr_with_breaks(data, x_cols, test_breaks)
                
                if ssr < best_ssr:
                    best_ssr = ssr
        
        # F statistic
        f_stat = ((ssr_l - best_ssr) / k) / (best_ssr / (n - (l+2)*k))
        
        # Approximate p-value
        p_value = 1 - stats.f.cdf(f_stat, k, n - (l+2)*k)
        
        return f_stat, p_value
    
    def _find_single_break(self,
                          data: pd.DataFrame,
                          x_cols: List[str]) -> pd.Timestamp:
        """Find optimal single break point."""
        n = len(data)
        min_seg = int(n * self.min_segment_pct)
        
        best_ssr = np.inf
        best_break = None
        
        for t in range(min_seg, n - min_seg):
            break_date = data.index[t]
            
            # Calculate SSR with break at t
            ssr = self._calculate_ssr_with_breaks(data, x_cols, [break_date])
            
            if ssr < best_ssr:
                best_ssr = ssr
                best_break = break_date
        
        return best_break
    
    def _find_additional_break(self,
                             data: pd.DataFrame,
                             x_cols: List[str],
                             current_breaks: List[pd.Timestamp]) -> pd.Timestamp:
        """Find optimal additional break given existing breaks."""
        segments = self._get_segments(data.index, current_breaks)
        
        best_ssr = np.inf
        best_break = None
        
        for seg_start, seg_end in segments:
            seg_data = data.loc[seg_start:seg_end]
            n_seg = len(seg_data)
            min_seg = int(n_seg * self.min_segment_pct)
            
            if n_seg < 2 * min_seg:
                continue
            
            for i in range(min_seg, n_seg - min_seg):
                break_date = seg_data.index[i]
                test_breaks = current_breaks + [break_date]
                test_breaks.sort()
                
                ssr = self._calculate_ssr_with_breaks(data, x_cols, test_breaks)
                
                if ssr < best_ssr:
                    best_ssr = ssr
                    best_break = break_date
        
        return best_break
    
    def _calculate_ssr_with_breaks(self,
                                 data: pd.DataFrame,
                                 x_cols: List[str],
                                 break_dates: List[pd.Timestamp]) -> float:
        """Calculate SSR for model with given breaks."""
        segments = self._get_segments(data.index, break_dates)
        total_ssr = 0
        
        for seg_start, seg_end in segments:
            seg_data = data.loc[seg_start:seg_end]
            
            if len(seg_data) > len(x_cols):
                y = seg_data['y'].values
                X = seg_data[x_cols].values
                
                try:
                    beta = np.linalg.lstsq(X, y, rcond=None)[0]
                    residuals = y - X @ beta
                    total_ssr += np.sum(residuals**2)
                except:
                    return np.inf
        
        return total_ssr
    
    def _get_segments(self,
                     index: pd.DatetimeIndex,
                     breaks: List[pd.Timestamp]) -> List[Tuple[pd.Timestamp, pd.Timestamp]]:
        """Get date segments based on break points."""
        segments = []
        
        if not breaks:
            segments.append((index[0], index[-1]))
        else:
            # First segment
            segments.append((index[0], breaks[0]))
            
            # Middle segments
            for i in range(len(breaks) - 1):
                segments.append((breaks[i], breaks[i+1]))
            
            # Last segment
            segments.append((breaks[-1], index[-1]))
        
        return segments
    
    def _estimate_break_model(self,
                            data: pd.DataFrame,
                            x_cols: List[str],
                            break_dates: List[pd.Timestamp],
                            break_type: str) -> BaiPerronResults:
        """Estimate final model with detected breaks."""
        segments = self._get_segments(data.index, break_dates)
        
        # Estimate parameters for each regime
        regime_parameters = {}
        residual_variance = {}
        break_points = []
        
        for i, (seg_start, seg_end) in enumerate(segments):
            seg_data = data.loc[seg_start:seg_end]
            
            y = seg_data['y'].values
            X = seg_data[x_cols].values
            
            # OLS for this regime
            beta = np.linalg.lstsq(X, y, rcond=None)[0]
            residuals = y - X @ beta
            var = np.var(residuals)
            
            regime_parameters[i] = {col: beta[j] for j, col in enumerate(x_cols)}
            residual_variance[i] = var
            
            # Create break point info
            if i < len(break_dates):
                # Calculate magnitude of break
                if i > 0:
                    # Change in coefficients
                    prev_beta = np.array(list(regime_parameters[i-1].values()))
                    curr_beta = np.array(list(regime_parameters[i].values()))
                    magnitude = np.linalg.norm(curr_beta - prev_beta)
                else:
                    magnitude = 0
                
                # Confidence interval (simplified)
                n_seg = len(seg_data)
                ci_width = int(1.96 * np.sqrt(n_seg) * 0.1)  # Rough approximation
                ci_lower = max(0, seg_data.index.get_loc(break_dates[i]) - ci_width)
                ci_upper = min(len(data)-1, seg_data.index.get_loc(break_dates[i]) + ci_width)
                
                break_points.append(BreakPoint(
                    date=break_dates[i],
                    confidence_interval=(data.index[ci_lower], data.index[ci_upper]),
                    magnitude=magnitude,
                    type=break_type
                ))
        
        # Calculate information criteria
        n = len(data)
        k = len(x_cols)
        m = len(break_dates)
        
        # Total SSR
        total_ssr = sum(
            residual_variance[i] * len(data.loc[seg[0]:seg[1]])
            for i, seg in enumerate(segments)
        )
        
        # BIC and AIC
        log_lik = -n/2 * np.log(2*np.pi) - n/2 * np.log(total_ssr/n) - n/2
        aic = -2 * log_lik + 2 * k * (m + 1)
        bic = -2 * log_lik + np.log(n) * k * (m + 1)
        
        # Test statistics
        test_statistics = {
            'sup_f': self._calculate_sup_f_stat(data, x_cols, break_dates),
            'seq_f': [bp.magnitude for bp in break_points]  # Simplified
        }
        
        information_criteria = {
            'aic': aic,
            'bic': bic,
            'log_likelihood': log_lik
        }
        
        return BaiPerronResults(
            break_dates=break_dates,
            n_breaks=len(break_dates),
            break_points=break_points,
            regime_parameters=regime_parameters,
            test_statistics=test_statistics,
            information_criteria=information_criteria,
            residual_variance=residual_variance
        )
    
    def _no_break_results(self,
                         data: pd.DataFrame,
                         x_cols: List[str]) -> BaiPerronResults:
        """Results when no breaks are detected."""
        y = data['y'].values
        X = data[x_cols].values
        
        # Single regime estimation
        beta = np.linalg.lstsq(X, y, rcond=None)[0]
        residuals = y - X @ beta
        var = np.var(residuals)
        
        regime_params = {0: {col: beta[i] for i, col in enumerate(x_cols)}}
        
        # Information criteria
        n = len(data)
        k = len(x_cols)
        log_lik = -n/2 * np.log(2*np.pi*var) - n/2
        
        return BaiPerronResults(
            break_dates=[],
            n_breaks=0,
            break_points=[],
            regime_parameters=regime_params,
            test_statistics={'sup_f': 0},
            information_criteria={
                'aic': -2 * log_lik + 2 * k,
                'bic': -2 * log_lik + np.log(n) * k,
                'log_likelihood': log_lik
            },
            residual_variance={0: var}
        )
    
    def _calculate_sup_f_stat(self,
                            data: pd.DataFrame,
                            x_cols: List[str],
                            breaks: List[pd.Timestamp]) -> float:
        """Calculate supremum F statistic."""
        # Simplified - would need full implementation
        n = len(data)
        k = len(x_cols)
        m = len(breaks)
        
        # SSR without breaks
        y = data['y'].values
        X = data[x_cols].values
        beta = np.linalg.lstsq(X, y, rcond=None)[0]
        ssr_no_break = np.sum((y - X @ beta)**2)
        
        # SSR with breaks
        ssr_with_breaks = self._calculate_ssr_with_breaks(data, x_cols, breaks)
        
        # F statistic
        f_stat = ((ssr_no_break - ssr_with_breaks) / (m * k)) / (ssr_with_breaks / (n - (m+1)*k))
        
        return f_stat
    
    def _get_critical_value(self, k: int, m: int, sig_level: float) -> float:
        """Get critical values for structural break tests."""
        # Simplified - in practice would use Andrews/Bai-Perron tables
        # These are rough approximations
        critical_values = {
            (1, 1, 0.05): 8.58,
            (2, 1, 0.05): 10.13,
            (3, 1, 0.05): 11.47,
            (1, 2, 0.05): 7.22,
            (2, 2, 0.05): 8.67,
            (3, 2, 0.05): 9.92
        }
        
        return critical_values.get((k, m, sig_level), 10.0)
    
    def plot_break_analysis(self,
                          y: pd.Series,
                          results: BaiPerronResults,
                          save_path: Optional[str] = None):
        """Plot time series with identified break points."""
        import matplotlib.pyplot as plt
        import matplotlib.dates as mdates
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Plot time series
        ax.plot(y.index, y.values, 'b-', alpha=0.7, label='Exchange Rate Spread')
        
        # Add break points
        for bp in results.break_points:
            ax.axvline(x=bp.date, color='red', linestyle='--', alpha=0.8)
            
            # Add confidence interval
            ax.axvspan(bp.confidence_interval[0], bp.confidence_interval[1],
                      alpha=0.2, color='red')
        
        # Format x-axis
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
        plt.xticks(rotation=45)
        
        # Labels
        ax.set_xlabel('Date')
        ax.set_ylabel('Exchange Rate Spread')
        ax.set_title(f'Structural Break Analysis ({results.n_breaks} breaks detected)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Break analysis plot saved to {save_path}")
        
        return fig