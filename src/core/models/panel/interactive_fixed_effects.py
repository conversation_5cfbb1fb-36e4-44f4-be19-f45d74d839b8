"""
Interactive Fixed Effects (IFE) Model Implementation

Based on Bai (2009) and subsequent developments, this implements panel regression
with interactive fixed effects to control for unobserved heterogeneity that
varies over time.

Key features:
- Captures time-varying unobserved factors (e.g., Ramadan effects)
- Handles cross-sectional dependence
- Robust to various forms of heterogeneity
"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional, Dict, List
from dataclasses import dataclass
from scipy.linalg import svd
from sklearn.preprocessing import StandardScaler

from src.core.utils.logging import get_logger


logger = get_logger(__name__)


@dataclass
class IFEResults:
    """Results from Interactive Fixed Effects estimation."""
    coefficients: pd.Series
    standard_errors: pd.Series
    t_statistics: pd.Series
    p_values: pd.Series
    factors: np.ndarray
    loadings: np.ndarray
    residuals: np.ndarray
    r_squared: float
    adjusted_r_squared: float
    n_factors: int
    iterations: int
    converged: bool
    factor_contribution: float  # R² from factors alone


class InteractiveFixedEffectsModel:
    """
    Interactive Fixed Effects for panel data.
    
    Model: Y_it = X_it'β + λ_i'F_t + ε_it
    
    Where:
    - Y_it: Dependent variable for unit i at time t
    - X_it: Observable covariates
    - β: Coefficients on observables
    - λ_i: Factor loadings (unit-specific)
    - F_t: Common factors (time-specific)
    - ε_it: Idiosyncratic error
    """
    
    def __init__(
        self,
        n_factors: int = 3,
        max_iterations: int = 1000,
        tolerance: float = 1e-6,
        standardize: bool = True
    ):
        """
        Initialize IFE model.
        
        Args:
            n_factors: Number of interactive factors to extract
            max_iterations: Maximum iterations for convergence
            tolerance: Convergence tolerance
            standardize: Whether to standardize variables
        """
        self.n_factors = n_factors
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.standardize = standardize
        
        self.factors_: Optional[np.ndarray] = None
        self.loadings_: Optional[np.ndarray] = None
        self.coefficients_: Optional[np.ndarray] = None
        self.scaler_x_: Optional[StandardScaler] = None
        self.scaler_y_: Optional[StandardScaler] = None
        
    def fit(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        entity_var: str = 'entity',
        time_var: str = 'time',
        weights: Optional[pd.Series] = None
    ) -> IFEResults:
        """
        Estimate Interactive Fixed Effects model.
        
        Uses iterative algorithm:
        1. Initialize with PCA on residuals
        2. Given factors, estimate loadings and β
        3. Given loadings and β, estimate factors
        4. Iterate until convergence
        
        Args:
            X: Panel data covariates (must have MultiIndex with entity and time)
            y: Dependent variable
            entity_var: Name of entity level in MultiIndex
            time_var: Name of time level in MultiIndex
            weights: Optional weights for WLS
            
        Returns:
            IFEResults object with estimates
        """
        logger.info(f"Fitting IFE model with {self.n_factors} factors")
        
        # Validate inputs
        self._validate_inputs(X, y)
        
        # Extract panel structure
        entities = X.index.get_level_values(entity_var).unique()
        times = X.index.get_level_values(time_var).unique()
        n_entities = len(entities)
        n_times = len(times)
        
        # Convert to matrices
        X_mat, y_vec = self._panel_to_matrices(X, y, entities, times)
        
        # Standardize if requested
        if self.standardize:
            self.scaler_x_ = StandardScaler()
            self.scaler_y_ = StandardScaler()
            X_mat = self.scaler_x_.fit_transform(X_mat)
            y_vec = self.scaler_y_.fit_transform(y_vec.reshape(-1, 1)).flatten()
        
        # Initialize with OLS residuals
        beta_init = np.linalg.lstsq(X_mat, y_vec, rcond=None)[0]
        residuals = y_vec - X_mat @ beta_init
        
        # Reshape residuals to panel format (entities × times)
        resid_panel = residuals.reshape(n_entities, n_times)
        
        # Initialize factors and loadings with PCA
        factors, loadings = self._initialize_with_pca(resid_panel)
        
        # Iterative estimation
        beta = beta_init.copy()
        converged = False
        
        for iteration in range(self.max_iterations):
            beta_old = beta.copy()
            factors_old = factors.copy()
            
            # Step 1: Given factors, estimate loadings and β
            beta, loadings = self._estimate_beta_lambda(
                X_mat, y_vec, factors, n_entities, n_times
            )
            
            # Step 2: Given loadings and β, estimate factors
            factors = self._estimate_factors(
                X_mat, y_vec, beta, loadings, n_entities, n_times
            )
            
            # Check convergence
            beta_change = np.max(np.abs(beta - beta_old))
            factor_change = np.max(np.abs(factors - factors_old))
            
            if beta_change < self.tolerance and factor_change < self.tolerance:
                converged = True
                logger.info(f"IFE converged after {iteration + 1} iterations")
                break
        
        if not converged:
            logger.warning(f"IFE did not converge after {self.max_iterations} iterations")
        
        # Store results
        self.factors_ = factors
        self.loadings_ = loadings
        self.coefficients_ = beta
        
        # Calculate final residuals and statistics
        factor_component = self._compute_factor_component(loadings, factors, n_entities, n_times)
        fitted_values = X_mat @ beta + factor_component
        residuals = y_vec - fitted_values
        
        # Calculate R-squared
        if weights is not None:
            w = weights.values
            tss = np.sum(w * (y_vec - np.average(y_vec, weights=w))**2)
            rss = np.sum(w * residuals**2)
        else:
            tss = np.sum((y_vec - np.mean(y_vec))**2)
            rss = np.sum(residuals**2)
        
        r_squared = 1 - rss / tss
        n_params = len(beta) + self.n_factors * (n_entities + n_times)
        adjusted_r_squared = 1 - (1 - r_squared) * (len(y_vec) - 1) / (len(y_vec) - n_params)
        
        # Factor contribution to R²
        factor_only_fitted = factor_component + np.mean(y_vec)
        factor_tss = np.sum((y_vec - np.mean(y_vec))**2)
        factor_rss = np.sum((y_vec - factor_only_fitted)**2)
        factor_r2 = 1 - factor_rss / factor_tss
        
        # Calculate standard errors (simplified - should use HAC in production)
        se_beta = self._calculate_standard_errors(
            X_mat, residuals, n_entities, n_times
        )
        
        # Create results
        coef_series = pd.Series(beta, index=X.columns)
        se_series = pd.Series(se_beta, index=X.columns)
        t_stats = coef_series / se_series
        p_values = pd.Series(
            2 * (1 - pd.Series(np.abs(t_stats)).apply(
                lambda x: pd.Series([x]).map(lambda t: 1 - 0.5 * (1 + np.sign(t) * (1 - np.exp(-2 * t**2 / np.pi))))
            )),
            index=X.columns
        )
        
        return IFEResults(
            coefficients=coef_series,
            standard_errors=se_series,
            t_statistics=t_stats,
            p_values=p_values,
            factors=factors,
            loadings=loadings,
            residuals=residuals,
            r_squared=r_squared,
            adjusted_r_squared=adjusted_r_squared,
            n_factors=self.n_factors,
            iterations=iteration + 1,
            converged=converged,
            factor_contribution=factor_r2
        )
    
    def extract_seasonal_effects(
        self,
        time_index: pd.DatetimeIndex,
        factors: Optional[np.ndarray] = None
    ) -> Dict[str, pd.Series]:
        """
        Extract seasonal patterns from estimated factors.
        
        Useful for identifying Ramadan effects, seasonal agricultural patterns, etc.
        
        Args:
            time_index: DateTime index corresponding to time dimension
            factors: Factor matrix (uses self.factors_ if None)
            
        Returns:
            Dictionary with seasonal components
        """
        if factors is None:
            if self.factors_ is None:
                raise ValueError("Model must be fitted first")
            factors = self.factors_
        
        seasonal_components = {}
        
        # Extract month effects
        for i in range(factors.shape[1]):
            factor_series = pd.Series(factors[:, i], index=time_index)
            
            # Monthly seasonality
            monthly_pattern = factor_series.groupby(factor_series.index.month).mean()
            seasonal_components[f'factor_{i+1}_monthly'] = monthly_pattern
            
            # Check for Ramadan effect (would need Hijri calendar conversion)
            # This is simplified - in production, use proper Islamic calendar
            april_may_effect = monthly_pattern[[4, 5]].mean()
            other_months_effect = monthly_pattern[~monthly_pattern.index.isin([4, 5])].mean()
            ramadan_effect = april_may_effect - other_months_effect
            
            seasonal_components[f'factor_{i+1}_ramadan_effect'] = ramadan_effect
        
        return seasonal_components
    
    def predict_with_factors(
        self,
        X_new: pd.DataFrame,
        entity_id: Optional[str] = None,
        time_id: Optional[str] = None
    ) -> np.ndarray:
        """
        Predict using estimated model with factors.
        
        Args:
            X_new: New covariate data
            entity_id: Entity identifier for loading lookup
            time_id: Time identifier for factor lookup
            
        Returns:
            Predicted values
        """
        if self.coefficients_ is None:
            raise ValueError("Model must be fitted first")
        
        # Transform covariates
        X_mat = X_new.values
        if self.standardize and self.scaler_x_ is not None:
            X_mat = self.scaler_x_.transform(X_mat)
        
        # Linear prediction
        linear_pred = X_mat @ self.coefficients_
        
        # Add factor contribution if entity/time info provided
        if entity_id is not None and time_id is not None:
            # This would require mapping logic in production
            logger.warning("Factor contribution for new entities/times not yet implemented")
        
        # Inverse transform if standardized
        if self.standardize and self.scaler_y_ is not None:
            linear_pred = self.scaler_y_.inverse_transform(linear_pred.reshape(-1, 1)).flatten()
        
        return linear_pred
    
    def _validate_inputs(self, X: pd.DataFrame, y: pd.Series) -> None:
        """Validate input data."""
        if not isinstance(X.index, pd.MultiIndex):
            raise ValueError("X must have MultiIndex with entity and time levels")
        
        if len(X) != len(y):
            raise ValueError("X and y must have same length")
        
        if X.isnull().any().any() or y.isnull().any():
            raise ValueError("Missing values not allowed in IFE")
    
    def _panel_to_matrices(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        entities: pd.Index,
        times: pd.Index
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Convert panel data to matrix format."""
        # Create entity-time grid
        full_index = pd.MultiIndex.from_product(
            [entities, times],
            names=X.index.names
        )
        
        # Reindex to ensure balanced panel
        X_balanced = X.reindex(full_index)
        y_balanced = y.reindex(full_index)
        
        # Check for missing values after reindexing
        if X_balanced.isnull().any().any() or y_balanced.isnull().any():
            raise ValueError("Unbalanced panel detected. IFE requires balanced panel.")
        
        return X_balanced.values, y_balanced.values
    
    def _initialize_with_pca(self, residual_panel: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Initialize factors and loadings using PCA on residuals."""
        # Demean residuals
        resid_demeaned = residual_panel - np.mean(residual_panel, axis=0)
        
        # SVD for PCA
        U, S, Vt = svd(resid_demeaned, full_matrices=False)
        
        # Extract factors and loadings
        # Factors: T × r matrix (time-specific)
        factors = Vt[:self.n_factors, :].T * np.sqrt(residual_panel.shape[0])
        
        # Loadings: N × r matrix (entity-specific)
        loadings = U[:, :self.n_factors] * S[:self.n_factors] / np.sqrt(residual_panel.shape[0])
        
        return factors, loadings
    
    def _estimate_beta_lambda(
        self,
        X: np.ndarray,
        y: np.ndarray,
        factors: np.ndarray,
        n_entities: int,
        n_times: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Estimate β and λ given factors F."""
        # Construct augmented regressor matrix
        # For each entity, stack [X_i, F ⊗ e_i]
        
        augmented_list = []
        y_list = []
        
        for i in range(n_entities):
            # Get observations for entity i
            start_idx = i * n_times
            end_idx = (i + 1) * n_times
            
            X_i = X[start_idx:end_idx, :]
            y_i = y[start_idx:end_idx]
            
            # Create factor component for entity i
            factor_i = np.zeros((n_times, n_entities * self.n_factors))
            factor_i[:, i*self.n_factors:(i+1)*self.n_factors] = factors
            
            # Combine
            augmented_i = np.hstack([X_i, factor_i])
            augmented_list.append(augmented_i)
            y_list.append(y_i)
        
        # Stack all entities
        augmented_full = np.vstack(augmented_list)
        y_full = np.hstack(y_list)
        
        # OLS on augmented system
        params = np.linalg.lstsq(augmented_full, y_full, rcond=None)[0]
        
        # Extract β and λ
        n_beta = X.shape[1]
        beta = params[:n_beta]
        lambda_vec = params[n_beta:]
        loadings = lambda_vec.reshape(n_entities, self.n_factors)
        
        return beta, loadings
    
    def _estimate_factors(
        self,
        X: np.ndarray,
        y: np.ndarray,
        beta: np.ndarray,
        loadings: np.ndarray,
        n_entities: int,
        n_times: int
    ) -> np.ndarray:
        """Estimate factors F given β and λ."""
        # Remove effect of observables
        y_adjusted = y - X @ beta
        
        # Reshape to panel
        y_panel = y_adjusted.reshape(n_entities, n_times)
        
        # For each time period, estimate factors
        factors = np.zeros((n_times, self.n_factors))
        
        for t in range(n_times):
            # y_t - X_t'β = Λ'F_t
            # F_t = (Λ'Λ)^(-1)Λ'(y_t - X_t'β)
            y_t = y_panel[:, t]
            factors[t, :] = np.linalg.lstsq(loadings, y_t, rcond=None)[0]
        
        return factors
    
    def _compute_factor_component(
        self,
        loadings: np.ndarray,
        factors: np.ndarray,
        n_entities: int,
        n_times: int
    ) -> np.ndarray:
        """Compute λ_i'F_t component in vectorized form."""
        factor_component = np.zeros(n_entities * n_times)
        
        for i in range(n_entities):
            start_idx = i * n_times
            end_idx = (i + 1) * n_times
            factor_component[start_idx:end_idx] = factors @ loadings[i, :]
        
        return factor_component
    
    def _calculate_standard_errors(
        self,
        X: np.ndarray,
        residuals: np.ndarray,
        n_entities: int,
        n_times: int
    ) -> np.ndarray:
        """
        Calculate standard errors for β.
        
        This is a simplified version. In production, should use:
        - HAC standard errors for time series dependence
        - Clustered standard errors for cross-sectional dependence
        - Bootstrap for small samples
        """
        # Simple homoskedastic standard errors
        n = len(residuals)
        k = X.shape[1]
        
        # Residual variance
        sigma2 = np.sum(residuals**2) / (n - k - self.n_factors * (n_entities + n_times))
        
        # Variance-covariance matrix
        XtX_inv = np.linalg.inv(X.T @ X)
        var_beta = sigma2 * XtX_inv
        
        # Standard errors
        se_beta = np.sqrt(np.diag(var_beta))
        
        return se_beta


def estimate_optimal_factors(
    X: pd.DataFrame,
    y: pd.Series,
    max_factors: int = 10,
    criterion: str = 'bai_ng'
) -> int:
    """
    Estimate optimal number of factors using information criteria.
    
    Args:
        X: Covariates
        y: Dependent variable
        max_factors: Maximum factors to consider
        criterion: 'bai_ng' for Bai & Ng (2002) criteria
        
    Returns:
        Optimal number of factors
    """
    logger.info(f"Estimating optimal number of factors using {criterion} criterion")
    
    criteria_values = []
    
    for r in range(1, max_factors + 1):
        model = InteractiveFixedEffectsModel(n_factors=r)
        results = model.fit(X, y)
        
        # Calculate information criterion
        n = len(y)
        rss = np.sum(results.residuals**2)
        
        if criterion == 'bai_ng':
            # PC_p2 criterion from Bai & Ng (2002)
            penalty = r * np.log(n) / n
            ic_value = np.log(rss / n) + penalty
        else:
            raise ValueError(f"Unknown criterion: {criterion}")
        
        criteria_values.append(ic_value)
        
        logger.info(f"r={r}: IC={ic_value:.4f}, R²={results.r_squared:.4f}")
    
    # Find minimum
    optimal_r = np.argmin(criteria_values) + 1
    logger.info(f"Optimal number of factors: {optimal_r}")
    
    return optimal_r