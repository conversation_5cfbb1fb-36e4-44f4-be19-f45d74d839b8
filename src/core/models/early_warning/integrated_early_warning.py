"""
Integrated Early Warning System for Currency Fragmentation

Combines multiple models and data sources to provide comprehensive
early warning for currency fragmentation and market disruption.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import warnings

from ..regime_switching.markov_switching import MarkovSwitchingCurrencyModel, RegimePrediction
from ..regime_switching.panel_threshold import PanelThresholdModel
from ..hypothesis_testing import HypothesisRegistry
from ..policy.early_warning_system import EarlyWarningSystem, EarlyWarningAlert, CrisisIndicators
from ...domain.shared.value_objects import AlertLevel
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class FragmentationRiskAssessment:
    """Comprehensive fragmentation risk assessment."""
    fragmentation_index: float  # 0-1 scale
    fragmentation_probability: float
    time_to_fragmentation: Optional[int]  # Days
    regime_change_probability: float
    threshold_proximity: float  # Distance to critical threshold
    contributing_factors: Dict[str, float]
    risk_trajectory: str  # 'increasing', 'stable', 'decreasing'
    confidence_level: float


@dataclass
class IntegratedWarning:
    """Integrated early warning combining all sources."""
    alert_level: AlertLevel
    warning_type: str
    primary_risk: str
    secondary_risks: List[str]
    affected_regions: List[str]
    time_horizon: int  # Days
    combined_probability: float
    recommended_actions: Dict[str, List[str]]  # By stakeholder
    model_agreement: float  # Consensus across models
    supporting_evidence: Dict[str, Dict]


class IntegratedEarlyWarningSystem:
    """
    Integrates multiple early warning components for comprehensive monitoring.
    
    Combines:
    - Regime-switching models for currency regime detection
    - Threshold models for critical points
    - Hypothesis tests for validation
    - ML-based crisis prediction
    - Real-time monitoring
    """
    
    def __init__(self):
        """Initialize integrated early warning system."""
        # Component models
        self.markov_model = MarkovSwitchingCurrencyModel(n_regimes=3)
        self.threshold_model = PanelThresholdModel(max_thresholds=2)
        self.crisis_predictor = EarlyWarningSystem(None)  # Will be configured
        self.hypothesis_registry = HypothesisRegistry()
        
        # System state
        self.is_initialized = False
        self.last_assessment = None
        self.historical_assessments = []
        
        # Configuration
        self.warning_thresholds = {
            'fragmentation_index': {
                'low': 0.3,
                'medium': 0.5,
                'high': 0.7,
                'critical': 0.85
            },
            'regime_probability': {
                'low': 0.2,
                'medium': 0.4,
                'high': 0.6,
                'critical': 0.8
            }
        }
    
    def initialize(self, historical_data: pd.DataFrame, 
                  historical_crises: Optional[pd.DataFrame] = None) -> None:
        """
        Initialize system with historical data.
        
        Args:
            historical_data: Historical market and economic data
            historical_crises: Historical crisis events for training
        """
        logger.info("Initializing integrated early warning system")
        
        # Train regime-switching model
        if 'exchange_spread' in historical_data.columns:
            self.markov_model.fit(historical_data['exchange_spread'])
        
        # Train crisis predictor
        self.crisis_predictor.train(historical_data)
        
        # Store initialization data
        self.initialization_date = datetime.now()
        self.is_initialized = True
        
        logger.info("Integrated early warning system initialized")
    
    def assess_fragmentation_risk(self, current_data: pd.DataFrame) -> FragmentationRiskAssessment:
        """
        Assess current fragmentation risk using all available models.
        
        Args:
            current_data: Current market and economic data
            
        Returns:
            Comprehensive fragmentation risk assessment
        """
        if not self.is_initialized:
            raise ValueError("System must be initialized before assessment")
        
        # Calculate fragmentation index
        fragmentation_index = self._calculate_fragmentation_index(current_data)
        
        # Get regime predictions
        regime_prediction = self._get_regime_prediction(current_data)
        
        # Check threshold proximity
        threshold_proximity = self._check_threshold_proximity(current_data)
        
        # Analyze contributing factors
        contributing_factors = self._analyze_contributing_factors(current_data)
        
        # Estimate time to fragmentation
        time_to_fragmentation = self._estimate_time_to_fragmentation(
            fragmentation_index, regime_prediction, threshold_proximity
        )
        
        # Determine risk trajectory
        risk_trajectory = self._determine_risk_trajectory(current_data)
        
        # Calculate confidence
        confidence = self._calculate_assessment_confidence(current_data)
        
        assessment = FragmentationRiskAssessment(
            fragmentation_index=fragmentation_index,
            fragmentation_probability=self._calculate_fragmentation_probability(
                fragmentation_index, regime_prediction
            ),
            time_to_fragmentation=time_to_fragmentation,
            regime_change_probability=regime_prediction.change_probability,
            threshold_proximity=threshold_proximity,
            contributing_factors=contributing_factors,
            risk_trajectory=risk_trajectory,
            confidence_level=confidence
        )
        
        # Store assessment
        self.last_assessment = assessment
        self.historical_assessments.append({
            'timestamp': datetime.now(),
            'assessment': assessment
        })
        
        return assessment
    
    def generate_integrated_warnings(self, current_data: pd.DataFrame,
                                   forecast_horizon: int = 30) -> List[IntegratedWarning]:
        """
        Generate integrated warnings combining all sources.
        
        Args:
            current_data: Current data
            forecast_horizon: Days to forecast ahead
            
        Returns:
            List of integrated warnings
        """
        # Get fragmentation assessment
        fragmentation_risk = self.assess_fragmentation_risk(current_data)
        
        # Get crisis warnings
        crisis_warnings = self.crisis_predictor.generate_alerts(current_data, forecast_horizon)
        
        # Run relevant hypothesis tests
        hypothesis_results = self._run_hypothesis_tests(current_data)
        
        # Combine into integrated warnings
        integrated_warnings = self._integrate_warnings(
            fragmentation_risk, crisis_warnings, hypothesis_results
        )
        
        # Prioritize warnings
        prioritized_warnings = self._prioritize_integrated_warnings(integrated_warnings)
        
        return prioritized_warnings
    
    def _calculate_fragmentation_index(self, data: pd.DataFrame) -> float:
        """Calculate composite fragmentation index."""
        index_components = []
        weights = []
        
        # Exchange rate divergence component
        if 'exchange_spread' in data.columns:
            spread = data['exchange_spread'].iloc[-1]
            spread_normalized = min(1, spread / 2000)  # Normalize by max expected spread
            index_components.append(spread_normalized)
            weights.append(0.4)  # High weight
        
        # Price correlation breakdown
        if 'price_correlation' in data.columns:
            correlation = data['price_correlation'].iloc[-1]
            correlation_index = 1 - correlation  # Lower correlation = higher fragmentation
            index_components.append(correlation_index)
            weights.append(0.3)
        
        # Trade flow disruption
        if 'trade_volume' in data.columns:
            current_trade = data['trade_volume'].iloc[-1]
            historical_trade = data['trade_volume'].mean()
            trade_disruption = 1 - (current_trade / historical_trade)
            index_components.append(max(0, min(1, trade_disruption)))
            weights.append(0.2)
        
        # Aid effectiveness decline
        if 'aid_effectiveness' in data.columns:
            effectiveness = data['aid_effectiveness'].iloc[-1]
            effectiveness_index = 1 - effectiveness  # Lower effectiveness = higher fragmentation
            index_components.append(effectiveness_index)
            weights.append(0.1)
        
        # Calculate weighted average
        if index_components:
            total_weight = sum(weights)
            weighted_sum = sum(c * w for c, w in zip(index_components, weights))
            return weighted_sum / total_weight
        else:
            return 0.5  # Default medium risk
    
    def _get_regime_prediction(self, data: pd.DataFrame) -> RegimePrediction:
        """Get regime prediction from Markov-switching model."""
        if 'exchange_spread' in data.columns:
            current_spread = data['exchange_spread'].iloc[-1]
            recent_spreads = data['exchange_spread'].tail(30)
            
            return self.markov_model.predict_regime_change(
                current_spread, recent_spreads, horizon=4
            )
        else:
            # Return default prediction
            return RegimePrediction(
                current_regime=1,
                regime_probs={0: 0.3, 1: 0.5, 2: 0.2},
                change_probability=0.3,
                expected_duration=10,
                warning_level='medium'
            )
    
    def _check_threshold_proximity(self, data: pd.DataFrame) -> float:
        """Check proximity to critical thresholds."""
        # Key threshold: 100% exchange rate differential
        critical_threshold = 100
        
        if 'exchange_diff_pct' in data.columns:
            current_diff = data['exchange_diff_pct'].iloc[-1]
            
            # Calculate proximity (0 = at threshold, 1 = far from threshold)
            if current_diff >= critical_threshold:
                return 0  # Already past threshold
            else:
                return 1 - (current_diff / critical_threshold)
        
        return 0.5  # Unknown
    
    def _analyze_contributing_factors(self, data: pd.DataFrame) -> Dict[str, float]:
        """Analyze factors contributing to fragmentation risk."""
        factors = {}
        
        # Exchange rate factors
        if 'exchange_spread' in data.columns:
            spread_change = data['exchange_spread'].pct_change(30).iloc[-1]
            factors['exchange_rate_divergence'] = abs(spread_change)
        
        # Conflict factors
        if 'conflict_intensity' in data.columns:
            factors['conflict_escalation'] = data['conflict_intensity'].iloc[-1] / data['conflict_intensity'].max()
        
        # Economic factors
        if 'inflation_differential' in data.columns:
            factors['inflation_divergence'] = data['inflation_differential'].iloc[-1] / 100
        
        # Political factors
        if 'political_stability' in data.columns:
            factors['political_instability'] = 1 - data['political_stability'].iloc[-1]
        
        # Trade factors
        if 'trade_restrictions' in data.columns:
            factors['trade_barriers'] = data['trade_restrictions'].iloc[-1]
        
        # Normalize all factors to 0-1 scale
        for key in factors:
            factors[key] = max(0, min(1, factors[key]))
        
        return factors
    
    def _estimate_time_to_fragmentation(self, 
                                      fragmentation_index: float,
                                      regime_prediction: RegimePrediction,
                                      threshold_proximity: float) -> Optional[int]:
        """Estimate days until fragmentation event."""
        # If already fragmented
        if fragmentation_index > 0.85:
            return 0
        
        # If low risk
        if fragmentation_index < 0.3:
            return None  # No imminent fragmentation
        
        # Estimate based on current trajectory
        if regime_prediction.current_regime == 2:  # Already in crisis
            return int(7 * (1 - fragmentation_index))  # 0-7 days
        elif regime_prediction.current_regime == 1:  # Transition
            if regime_prediction.change_probability > 0.7:
                return int(14 * (1 - fragmentation_index))  # 0-14 days
            else:
                return int(30 * (1 - fragmentation_index))  # 0-30 days
        else:  # Stable
            if regime_prediction.change_probability > 0.5:
                return int(60 * (1 - fragmentation_index))  # 0-60 days
            else:
                return None
    
    def _determine_risk_trajectory(self, data: pd.DataFrame) -> str:
        """Determine if risk is increasing, stable, or decreasing."""
        if len(self.historical_assessments) < 3:
            return 'stable'  # Not enough history
        
        # Get recent fragmentation indices
        recent_indices = [
            a['assessment'].fragmentation_index 
            for a in self.historical_assessments[-5:]
        ]
        
        # Calculate trend
        if len(recent_indices) >= 2:
            trend = np.polyfit(range(len(recent_indices)), recent_indices, 1)[0]
            
            if trend > 0.01:
                return 'increasing'
            elif trend < -0.01:
                return 'decreasing'
            else:
                return 'stable'
        
        return 'stable'
    
    def _calculate_assessment_confidence(self, data: pd.DataFrame) -> float:
        """Calculate confidence in the assessment."""
        confidence = 1.0
        
        # Reduce confidence for missing data
        missing_ratio = data.isnull().sum().sum() / data.size
        confidence *= (1 - missing_ratio)
        
        # Reduce confidence for limited history
        if len(data) < 100:
            confidence *= len(data) / 100
        
        # Reduce confidence for model disagreement
        # (would implement model agreement check)
        
        return max(0.3, min(0.95, confidence))
    
    def _calculate_fragmentation_probability(self, 
                                           fragmentation_index: float,
                                           regime_prediction: RegimePrediction) -> float:
        """Calculate probability of fragmentation event."""
        # Base probability from index
        base_prob = fragmentation_index
        
        # Adjust for regime
        regime_multiplier = {
            0: 0.5,   # Stable regime reduces probability
            1: 1.0,   # Transition regime neutral
            2: 1.5    # Crisis regime increases probability
        }
        
        adjusted_prob = base_prob * regime_multiplier.get(regime_prediction.current_regime, 1.0)
        
        # Consider regime change probability
        if regime_prediction.current_regime < 2:
            adjusted_prob += regime_prediction.change_probability * 0.3
        
        return min(0.95, adjusted_prob)
    
    def _run_hypothesis_tests(self, data: pd.DataFrame) -> Dict[str, Dict]:
        """Run relevant hypothesis tests for current conditions."""
        results = {}
        
        # Run H5 (Cross-border arbitrage) if exchange differential high
        if 'exchange_diff_pct' in data.columns and data['exchange_diff_pct'].iloc[-1] > 50:
            h5_test = self.hypothesis_registry.get('H5')
            if h5_test:
                h5_data = h5_test.prepare_data(data)
                h5_results = h5_test.run_test(h5_data)
                results['H5'] = {
                    'passed': h5_results.test_passed,
                    'confidence': h5_results.confidence,
                    'summary': h5_results.summary
                }
        
        # Run H9 (Threshold effects) if approaching threshold
        if 'exchange_diff_pct' in data.columns and 80 < data['exchange_diff_pct'].iloc[-1] < 120:
            h9_test = self.hypothesis_registry.get('H9')
            if h9_test:
                h9_data = h9_test.prepare_data(data)
                h9_results = h9_test.run_test(h9_data)
                results['H9'] = {
                    'passed': h9_results.test_passed,
                    'threshold': h9_results.optimal_threshold,
                    'effect_size': h9_results.threshold_effect_size
                }
        
        return results
    
    def _integrate_warnings(self, 
                           fragmentation_risk: FragmentationRiskAssessment,
                           crisis_warnings: List[EarlyWarningAlert],
                           hypothesis_results: Dict[str, Dict]) -> List[IntegratedWarning]:
        """Integrate warnings from all sources."""
        integrated_warnings = []
        
        # Create fragmentation warning if risk is high
        if fragmentation_risk.fragmentation_index > 0.7:
            # Determine alert level
            if fragmentation_risk.fragmentation_index > 0.85:
                alert_level = AlertLevel.CRITICAL
            elif fragmentation_risk.fragmentation_index > 0.7:
                alert_level = AlertLevel.HIGH
            else:
                alert_level = AlertLevel.MEDIUM
            
            # Compile recommended actions by stakeholder
            actions = {
                'government': [
                    'Coordinate monetary policy across regions',
                    'Address underlying fiscal imbalances',
                    'Strengthen currency reserves',
                    'Implement capital controls if necessary'
                ],
                'humanitarian': [
                    'Adjust aid modalities for currency zones',
                    'Pre-position supplies in stable currency areas',
                    'Develop zone-specific response plans',
                    'Monitor vulnerable population movements'
                ],
                'private_sector': [
                    'Hedge currency exposure',
                    'Diversify supply chains across zones',
                    'Adjust pricing strategies by zone',
                    'Build buffer stocks'
                ]
            }
            
            # Add specific actions based on time horizon
            if fragmentation_risk.time_to_fragmentation and fragmentation_risk.time_to_fragmentation < 30:
                actions['government'].insert(0, 'URGENT: Convene emergency economic council')
                actions['humanitarian'].insert(0, 'URGENT: Activate crisis response protocols')
            
            # Create integrated warning
            warning = IntegratedWarning(
                alert_level=alert_level,
                warning_type='currency_fragmentation',
                primary_risk='Currency zone fragmentation',
                secondary_risks=self._identify_secondary_risks(fragmentation_risk, crisis_warnings),
                affected_regions=self._identify_affected_regions(fragmentation_risk),
                time_horizon=fragmentation_risk.time_to_fragmentation or 90,
                combined_probability=fragmentation_risk.fragmentation_probability,
                recommended_actions=actions,
                model_agreement=self._calculate_model_agreement(fragmentation_risk, hypothesis_results),
                supporting_evidence={
                    'fragmentation_assessment': {
                        'index': fragmentation_risk.fragmentation_index,
                        'trajectory': fragmentation_risk.risk_trajectory,
                        'key_factors': fragmentation_risk.contributing_factors
                    },
                    'hypothesis_tests': hypothesis_results,
                    'regime_analysis': {
                        'change_probability': fragmentation_risk.regime_change_probability,
                        'threshold_proximity': fragmentation_risk.threshold_proximity
                    }
                }
            )
            
            integrated_warnings.append(warning)
        
        # Integrate crisis warnings
        for crisis_warning in crisis_warnings:
            if crisis_warning.alert_level in [AlertLevel.HIGH, AlertLevel.CRITICAL]:
                # Check if this crisis could trigger fragmentation
                fragmentation_trigger = self._assess_fragmentation_trigger(crisis_warning, fragmentation_risk)
                
                if fragmentation_trigger > 0.5:
                    # Enhance warning with fragmentation risk
                    integrated_warning = self._enhance_crisis_warning(
                        crisis_warning, fragmentation_risk, fragmentation_trigger
                    )
                    integrated_warnings.append(integrated_warning)
        
        return integrated_warnings
    
    def _identify_secondary_risks(self, 
                                fragmentation_risk: FragmentationRiskAssessment,
                                crisis_warnings: List[EarlyWarningAlert]) -> List[str]:
        """Identify secondary risks from fragmentation."""
        secondary_risks = []
        
        # Economic risks
        if fragmentation_risk.fragmentation_index > 0.7:
            secondary_risks.extend([
                'Hyperinflation in weaker currency zone',
                'Trade collapse between zones',
                'Banking system fragmentation'
            ])
        
        # Humanitarian risks
        if any(w.alert_type == 'food_security_crisis' for w in crisis_warnings):
            secondary_risks.extend([
                'Differential food access by zone',
                'Aid effectiveness decline',
                'Population displacement'
            ])
        
        # Political risks
        if fragmentation_risk.contributing_factors.get('political_instability', 0) > 0.5:
            secondary_risks.extend([
                'Governance breakdown',
                'Inter-zone conflict escalation'
            ])
        
        return secondary_risks[:5]  # Top 5 risks
    
    def _identify_affected_regions(self, fragmentation_risk: FragmentationRiskAssessment) -> List[str]:
        """Identify regions affected by fragmentation."""
        # In practice, would use actual geographic data
        affected = []
        
        if fragmentation_risk.fragmentation_index > 0.5:
            affected.extend(['Northern governorates', 'Southern governorates'])
        
        if fragmentation_risk.fragmentation_index > 0.7:
            affected.extend(['Border regions', 'Contested areas'])
        
        if fragmentation_risk.fragmentation_index > 0.85:
            affected.append('All regions')
        
        return list(set(affected))
    
    def _calculate_model_agreement(self, 
                                 fragmentation_risk: FragmentationRiskAssessment,
                                 hypothesis_results: Dict[str, Dict]) -> float:
        """Calculate agreement across different models."""
        agreement_scores = []
        
        # Check if regime model agrees with high risk
        if fragmentation_risk.regime_change_probability > 0.5:
            agreement_scores.append(1 if fragmentation_risk.fragmentation_index > 0.5 else 0)
        
        # Check if hypothesis tests support risk assessment
        if 'H9' in hypothesis_results:
            # Threshold test should show effect if close to threshold
            threshold_agreement = 1 if hypothesis_results['H9']['effect_size'] > 0.5 else 0
            agreement_scores.append(threshold_agreement)
        
        if 'H5' in hypothesis_results:
            # Arbitrage test should fail if fragmentation high
            arbitrage_agreement = 0 if hypothesis_results['H5']['passed'] else 1
            agreement_scores.append(arbitrage_agreement)
        
        return np.mean(agreement_scores) if agreement_scores else 0.5
    
    def _assess_fragmentation_trigger(self, 
                                    crisis_warning: EarlyWarningAlert,
                                    fragmentation_risk: FragmentationRiskAssessment) -> float:
        """Assess if crisis could trigger fragmentation."""
        trigger_probability = 0
        
        # Price crises can trigger fragmentation
        if crisis_warning.alert_type == 'price_spike':
            trigger_probability += 0.3
            if fragmentation_risk.fragmentation_index > 0.6:
                trigger_probability += 0.3
        
        # Supply shocks increase fragmentation risk
        if crisis_warning.alert_type == 'supply_shock':
            trigger_probability += 0.4
            if 'conflict' in str(crisis_warning.supporting_evidence):
                trigger_probability += 0.2
        
        return min(1, trigger_probability)
    
    def _enhance_crisis_warning(self, 
                              crisis_warning: EarlyWarningAlert,
                              fragmentation_risk: FragmentationRiskAssessment,
                              trigger_probability: float) -> IntegratedWarning:
        """Enhance crisis warning with fragmentation risk information."""
        # Upgrade alert level if fragmentation risk is high
        alert_level = crisis_warning.alert_level
        if trigger_probability > 0.7 and fragmentation_risk.fragmentation_index > 0.6:
            if alert_level == AlertLevel.MEDIUM:
                alert_level = AlertLevel.HIGH
            elif alert_level == AlertLevel.HIGH:
                alert_level = AlertLevel.CRITICAL
        
        # Add fragmentation-specific actions
        enhanced_actions = {
            'government': crisis_warning.recommended_actions[:2] + [
                'Monitor currency stability',
                'Prepare for potential zone-specific interventions'
            ],
            'humanitarian': crisis_warning.recommended_actions[2:] + [
                'Develop multi-currency contingency plans'
            ]
        }
        
        return IntegratedWarning(
            alert_level=alert_level,
            warning_type=f"{crisis_warning.alert_type}_with_fragmentation_risk",
            primary_risk=crisis_warning.alert_type,
            secondary_risks=['Currency fragmentation'] + list(crisis_warning.affected_commodities),
            affected_regions=crisis_warning.affected_markets,
            time_horizon=crisis_warning.time_horizon,
            combined_probability=max(crisis_warning.probability, trigger_probability),
            recommended_actions=enhanced_actions,
            model_agreement=0.7,  # Moderate agreement
            supporting_evidence={
                'crisis_warning': crisis_warning.supporting_evidence,
                'fragmentation_trigger': trigger_probability,
                'current_fragmentation': fragmentation_risk.fragmentation_index
            }
        )
    
    def _prioritize_integrated_warnings(self, warnings: List[IntegratedWarning]) -> List[IntegratedWarning]:
        """Prioritize integrated warnings by urgency and impact."""
        # Sort by: alert level, time horizon, probability
        priority_map = {
            AlertLevel.CRITICAL: 4,
            AlertLevel.HIGH: 3,
            AlertLevel.MEDIUM: 2,
            AlertLevel.LOW: 1
        }
        
        warnings.sort(
            key=lambda w: (
                priority_map.get(w.alert_level, 0),
                -w.time_horizon,  # Negative so sooner = higher priority
                w.combined_probability
            ),
            reverse=True
        )
        
        return warnings[:5]  # Top 5 warnings
    
    def generate_dashboard_data(self) -> Dict[str, Union[float, str, Dict]]:
        """Generate data for monitoring dashboard."""
        if not self.last_assessment:
            return {'status': 'No assessment available'}
        
        assessment = self.last_assessment
        
        # Determine overall status
        if assessment.fragmentation_index > 0.85:
            status = 'CRITICAL'
            status_color = 'red'
        elif assessment.fragmentation_index > 0.7:
            status = 'WARNING'
            status_color = 'orange'
        elif assessment.fragmentation_index > 0.5:
            status = 'ELEVATED'
            status_color = 'yellow'
        else:
            status = 'STABLE'
            status_color = 'green'
        
        dashboard_data = {
            'status': status,
            'status_color': status_color,
            'fragmentation_index': round(assessment.fragmentation_index, 3),
            'fragmentation_probability': round(assessment.fragmentation_probability, 3),
            'days_to_fragmentation': assessment.time_to_fragmentation,
            'risk_trajectory': assessment.risk_trajectory,
            'confidence_level': round(assessment.confidence_level, 2),
            'last_updated': datetime.now().isoformat(),
            'key_risks': list(assessment.contributing_factors.keys())[:3],
            'regime_status': {
                'current': 'Stable/Transition/Crisis',  # Would get from regime model
                'change_probability': round(assessment.regime_change_probability, 3)
            },
            'threshold_status': {
                'proximity': round(assessment.threshold_proximity, 3),
                'critical_value': 100  # % exchange differential
            }
        }
        
        return dashboard_data
    
    def export_assessment_report(self, assessment: FragmentationRiskAssessment,
                                warnings: List[IntegratedWarning]) -> str:
        """Export assessment as formatted report."""
        report = f"""
INTEGRATED EARLY WARNING SYSTEM REPORT
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}

EXECUTIVE SUMMARY
================
Fragmentation Risk Level: {self._get_risk_level_text(assessment.fragmentation_index)}
Overall Fragmentation Index: {assessment.fragmentation_index:.2f}
Probability of Fragmentation Event: {assessment.fragmentation_probability:.1%}
Estimated Time to Event: {assessment.time_to_fragmentation or 'No imminent threat'} days
Risk Trajectory: {assessment.risk_trajectory.upper()}

KEY RISK FACTORS
===============
"""
        for factor, value in sorted(assessment.contributing_factors.items(), 
                                   key=lambda x: x[1], reverse=True)[:5]:
            report += f"- {factor.replace('_', ' ').title()}: {value:.2f}\n"
        
        report += f"""
ACTIVE WARNINGS ({len(warnings)})
===============
"""
        for i, warning in enumerate(warnings[:3], 1):
            report += f"""
{i}. {warning.warning_type.upper()} - {warning.alert_level.name}
   Primary Risk: {warning.primary_risk}
   Time Horizon: {warning.time_horizon} days
   Probability: {warning.combined_probability:.1%}
   Affected Regions: {', '.join(warning.affected_regions)}
"""
        
        report += """
RECOMMENDED ACTIONS
==================
"""
        if warnings:
            for stakeholder, actions in warnings[0].recommended_actions.items():
                report += f"\n{stakeholder.upper()}:\n"
                for action in actions[:3]:
                    report += f"  • {action}\n"
        
        report += f"""
TECHNICAL DETAILS
================
Confidence Level: {assessment.confidence_level:.1%}
Regime Change Probability: {assessment.regime_change_probability:.1%}
Threshold Proximity: {assessment.threshold_proximity:.2f}

END OF REPORT
"""
        return report
    
    def _get_risk_level_text(self, index: float) -> str:
        """Convert fragmentation index to risk level text."""
        if index > 0.85:
            return "CRITICAL"
        elif index > 0.7:
            return "HIGH"
        elif index > 0.5:
            return "ELEVATED"
        elif index > 0.3:
            return "MODERATE"
        else:
            return "LOW"