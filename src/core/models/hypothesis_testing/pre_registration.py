"""
Pre-registration enforcement for hypothesis testing.

Ensures analysis plans are registered before data access to prevent
p-hacking and data-driven hypothesis generation.
"""

from functools import wraps
from typing import Dict, Any, Callable, Optional
from datetime import datetime
import json
import hashlib
from pathlib import Path

from src.core.utils.logging import get_logger

logger = get_logger(__name__)


class PreRegistrationError(Exception):
    """Raised when pre-registration requirements are not met."""
    pass


class PreRegistrationManager:
    """Manages pre-registration of analysis plans."""
    
    def __init__(self, registry_path: str = ".hypothesis_registry"):
        self.registry_path = Path(registry_path)
        self.registry_path.mkdir(exist_ok=True)
        self._registered_plans = {}
        self._load_registry()
        
    def register_plan(self, hypothesis_id: str, plan: Dict[str, Any]) -> str:
        """
        Register an analysis plan.
        
        Args:
            hypothesis_id: Hypothesis identifier
            plan: Analysis plan including specifications, decision rules, etc.
            
        Returns:
            Registration hash for verification
        """
        # Add timestamp
        plan['registered_at'] = datetime.now().isoformat()
        plan['hypothesis_id'] = hypothesis_id
        
        # Create hash
        plan_str = json.dumps(plan, sort_keys=True)
        plan_hash = hashlib.sha256(plan_str.encode()).hexdigest()[:16]
        
        # Save to registry
        plan_file = self.registry_path / f"{hypothesis_id}_{plan_hash}.json"
        with open(plan_file, 'w') as f:
            json.dump(plan, f, indent=2)
            
        self._registered_plans[hypothesis_id] = {
            'hash': plan_hash,
            'plan': plan,
            'file': str(plan_file)
        }
        
        logger.info(f"Registered analysis plan for {hypothesis_id} with hash {plan_hash}")
        return plan_hash
        
    def verify_registration(self, hypothesis_id: str) -> bool:
        """Check if hypothesis has registered plan."""
        return hypothesis_id in self._registered_plans
        
    def get_plan(self, hypothesis_id: str) -> Optional[Dict[str, Any]]:
        """Get registered plan for hypothesis."""
        if hypothesis_id in self._registered_plans:
            return self._registered_plans[hypothesis_id]['plan']
        return None
        
    def _load_registry(self):
        """Load existing registrations from disk."""
        for plan_file in self.registry_path.glob("*.json"):
            try:
                with open(plan_file, 'r') as f:
                    plan = json.load(f)
                    
                hypothesis_id = plan.get('hypothesis_id')
                if hypothesis_id:
                    plan_hash = plan_file.stem.split('_')[-1]
                    self._registered_plans[hypothesis_id] = {
                        'hash': plan_hash,
                        'plan': plan,
                        'file': str(plan_file)
                    }
            except Exception as e:
                logger.warning(f"Failed to load plan from {plan_file}: {e}")


# Global registry instance
_registry = PreRegistrationManager()


def requires_preregistration(func: Callable) -> Callable:
    """
    Decorator that enforces pre-registration before data access.
    
    Use on methods that access data or run analyses.
    """
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        # Check if this is a hypothesis test instance
        if hasattr(self, 'hypothesis_id'):
            hypothesis_id = self.hypothesis_id
            
            # Check registration
            if not _registry.verify_registration(hypothesis_id):
                raise PreRegistrationError(
                    f"Hypothesis {hypothesis_id} requires pre-registration before data access. "
                    f"Use register_analysis_plan() first."
                )
                
            # Log data access
            logger.info(f"Data access granted for pre-registered hypothesis {hypothesis_id}")
            
        return func(self, *args, **kwargs)
        
    return wrapper


def register_analysis_plan(hypothesis_id: str,
                          primary_specification: Dict[str, Any],
                          decision_rules: Dict[str, Any],
                          secondary_analyses: Optional[List[Dict[str, Any]]] = None,
                          power_analysis: Optional[Dict[str, Any]] = None) -> str:
    """
    Register an analysis plan before seeing data.
    
    Args:
        hypothesis_id: Hypothesis identifier (e.g., 'H1')
        primary_specification: Main model specification
        decision_rules: How results will be interpreted
        secondary_analyses: Additional planned analyses
        power_analysis: Power calculations and sample size
        
    Returns:
        Registration hash
        
    Example:
        >>> plan_hash = register_analysis_plan(
        ...     hypothesis_id='H1',
        ...     primary_specification={
        ...         'model': 'PanelOLS',
        ...         'fixed_effects': ['market', 'time'],
        ...         'clustering': 'market',
        ...         'controls': ['conflict_intensity', 'aid_presence']
        ...     },
        ...     decision_rules={
        ...         'alpha': 0.05,
        ...         'minimum_effect_size': 0.10,
        ...         'one_tailed': False
        ...     },
        ...     power_analysis={
        ...         'target_power': 0.80,
        ...         'assumed_effect_size': 0.25
        ...     }
        ... )
    """
    plan = {
        'hypothesis_id': hypothesis_id,
        'primary_specification': primary_specification,
        'decision_rules': decision_rules,
        'secondary_analyses': secondary_analyses or [],
        'power_analysis': power_analysis or {}
    }
    
    return _registry.register_plan(hypothesis_id, plan)


def get_registered_plan(hypothesis_id: str) -> Optional[Dict[str, Any]]:
    """Get the registered plan for a hypothesis."""
    return _registry.get_plan(hypothesis_id)


class DegreesOfFreedomTracker:
    """
    Track analytical choices to quantify researcher degrees of freedom.
    
    Based on Simmons, Nelson & Simonsohn (2011) "False-Positive Psychology"
    """
    
    def __init__(self, hypothesis_id: str):
        self.hypothesis_id = hypothesis_id
        self.choices_made = []
        self.choice_points = {
            'outlier_handling': None,
            'transformation': None,
            'controls_included': None,
            'sample_restrictions': None,
            'clustering': None,
            'weights': None,
            'missing_data': None,
            'measurement': None
        }
        
    def record_choice(self, choice_type: str, choice_made: Any, 
                     alternatives: List[Any]) -> None:
        """Record an analytical choice."""
        if choice_type not in self.choice_points:
            logger.warning(f"Unknown choice type: {choice_type}")
            
        self.choices_made.append({
            'timestamp': datetime.now().isoformat(),
            'choice_type': choice_type,
            'choice_made': choice_made,
            'alternatives': alternatives,
            'n_alternatives': len(alternatives)
        })
        
        self.choice_points[choice_type] = choice_made
        
    def calculate_degrees_of_freedom(self) -> int:
        """Calculate total researcher degrees of freedom."""
        if not self.choices_made:
            return 0
            
        # Product of alternatives at each choice point
        total_combinations = 1
        for choice in self.choices_made:
            total_combinations *= choice['n_alternatives']
            
        return total_combinations
        
    def generate_report(self) -> Dict[str, Any]:
        """Generate report on analytical choices."""
        return {
            'hypothesis_id': self.hypothesis_id,
            'n_choice_points': len(self.choices_made),
            'total_degrees_of_freedom': self.calculate_degrees_of_freedom(),
            'choices': self.choices_made,
            'choice_summary': self.choice_points
        }


def track_degrees_of_freedom(hypothesis_id: str) -> DegreesOfFreedomTracker:
    """Create a degrees of freedom tracker for a hypothesis."""
    return DegreesOfFreedomTracker(hypothesis_id)