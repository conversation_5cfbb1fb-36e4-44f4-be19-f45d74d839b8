"""
H8: Information Spillover Across Currency Zones

Tests whether price information transmission differs within versus
across currency zone boundaries, controlling for distance.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy import stats
from scipy.spatial.distance import cdist
from sklearn.metrics.pairwise import haversine_distances
import statsmodels.api as sm
import networkx as nx

from .hypothesis_framework import (
    HypothesisTest, TestData, TestResults, PolicyInterpretation
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class InformationSpilloverData(TestData):
    """Data structure for information spillover analysis."""
    price_series: Optional[pd.DataFrame] = None
    market_pairs: Optional[pd.DataFrame] = None
    zone_boundaries: Optional[pd.DataFrame] = None
    distance_matrix: Optional[pd.DataFrame] = None
    communication_infrastructure: Optional[pd.DataFrame] = None


@dataclass
class InformationSpilloverResults:
    """Results from information spillover analysis."""
    
    Uses composition with TestResults for core fields while adding
    hypothesis-specific fields.
    """
    # Core test results (composition, not inheritance)
    base_results: TestResults
    
    # Hypothesis-specific fields
    within_zone_transmission: Optional[float] = None
    cross_zone_transmission: Optional[float] = None
    transmission_differential: Optional[float] = None
    distance_decay_within: Optional[float] = None
    distance_decay_cross: Optional[float] = None
    information_lag_days: Optional[Dict[str, float]] = None
    network_centrality: Optional[Dict[str, float]] = None
    border_effects: Optional[Dict[str, float]] = None

    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, combining base and specific results."""
        result = self.base_results.to_dict()
        result.update({
            'within_zone_transmission': self.within_zone_transmission,
            'cross_zone_transmission': self.cross_zone_transmission,
            'transmission_differential': self.transmission_differential,
            'distance_decay_within': self.distance_decay_within,
            'distance_decay_cross': self.distance_decay_cross,
            'information_lag_days': self.information_lag_days,
            'network_centrality': self.network_centrality,
            'border_effects': self.border_effects
        })
        return result

class H8InformationSpilloverTest(HypothesisTest):
    """
    Tests information spillover patterns across currency zones.
    
    H8: Currency zones -> Information barriers
        - Within-zone transmission faster than cross-zone
        - Distance matters less within zones
        - Border effects amplified by currency differences
    """
    
    def __init__(self):
        super().__init__(
            hypothesis_id="H8",
            name="Information Spillover Across Currency Zones",
            description="Tests price information transmission within vs across zones"
        )
        self.max_lag_days = 10
        self.min_observations = 100
        self.distance_bins = 5
    
    def prepare_data(self, panel_data: pd.DataFrame) -> InformationSpilloverData:
        """Prepare data for information spillover analysis."""
        logger.info("Preparing data for H8 information spillover test")
        
        # Extract price series
        price_series = self._prepare_price_series(panel_data)
        
        # Create market pairs with zone info
        market_pairs = self._create_market_pairs(panel_data)
        
        # Get zone boundaries
        zone_boundaries = self._identify_zone_boundaries(panel_data)
        
        # Calculate distance matrix
        distance_matrix = self._calculate_distances(panel_data)
        
        # Communication infrastructure proxy
        comm_infrastructure = self._assess_communication_infrastructure(panel_data)
        
        return InformationSpilloverData(
            price_series=price_series,
            market_pairs=market_pairs,
            zone_boundaries=zone_boundaries,
            distance_matrix=distance_matrix,
            communication_infrastructure=comm_infrastructure
        )
    
    def _prepare_price_series(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Prepare price series for correlation analysis."""
        # Focus on commonly traded commodities
        common_commodities = ['wheat', 'rice', 'sugar', 'fuel']
        
        price_data = panel_data.copy()
        
        # Filter to common commodities if available
        if 'commodity' in price_data.columns:
            commodity_mask = price_data['commodity'].str.lower().apply(
                lambda x: any(c in x for c in common_commodities)
            )
            price_data = price_data[commodity_mask]
        
        # Ensure we have necessary columns
        required_cols = ['date', 'market', 'price_usd']
        if all(col in price_data.columns for col in required_cols):
            return price_data[required_cols + ['commodity'] if 'commodity' in price_data.columns else required_cols]
        else:
            # Create synthetic price series
            return self._create_synthetic_prices(panel_data)
    
    def _create_synthetic_prices(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Create synthetic price series for testing."""
        markets = panel_data['market'].unique()[:10]  # Limit to 10 markets
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='W')
        
        price_data = []
        for market in markets:
            zone = self._get_market_zone(market)
            base_price = 10 + np.random.normal(0, 1)
            
            for date in dates:
                # Prices with zone-specific trends
                trend = (date - dates[0]).days / 365
                if zone == 'houthi':
                    price = base_price * (1 + trend * 0.1) + np.random.normal(0, 0.5)
                else:
                    price = base_price * (1 + trend * 0.3) + np.random.normal(0, 1)
                
                price_data.append({
                    'date': date,
                    'market': market,
                    'price_usd': max(1, price),
                    'commodity': 'wheat'
                })
        
        return pd.DataFrame(price_data)
    
    def _get_market_zone(self, market: str) -> str:
        """Determine currency zone for a market."""
        houthi_markets = ['sanaa', 'saada', 'amran', 'hajjah', 'hodeidah']
        return 'houthi' if any(h in market.lower() for h in houthi_markets) else 'government'
    
    def _create_market_pairs(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Create all market pairs with zone information."""
        markets = panel_data['market'].unique()
        
        pairs = []
        for i, market1 in enumerate(markets):
            zone1 = self._get_market_zone(market1)
            
            for market2 in markets[i+1:]:
                zone2 = self._get_market_zone(market2)
                
                pairs.append({
                    'market1': market1,
                    'market2': market2,
                    'zone1': zone1,
                    'zone2': zone2,
                    'same_zone': zone1 == zone2,
                    'pair_id': f"{market1}_{market2}"
                })
        
        return pd.DataFrame(pairs)
    
    def _identify_zone_boundaries(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Identify markets near zone boundaries."""
        # Simplified: markets with "border" in name or known border cities
        border_markets = ['haradh', 'abs', 'khamer', 'marib', 'bayhan']
        
        boundaries = []
        for market in panel_data['market'].unique():
            is_border = (
                'border' in market.lower() or
                any(b in market.lower() for b in border_markets)
            )
            
            zone = self._get_market_zone(market)
            
            boundaries.append({
                'market': market,
                'is_border': is_border,
                'zone': zone,
                'border_distance_km': 0 if is_border else np.random.uniform(10, 200)
            })
        
        return pd.DataFrame(boundaries)
    
    def _calculate_distances(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate distance matrix between markets."""
        markets = panel_data['market'].unique()
        n_markets = len(markets)
        
        # Create synthetic coordinates (in reality, would use actual GPS)
        np.random.seed(42)
        coords = np.random.randn(n_markets, 2) * 100  # Spread over ~200km
        
        # Adjust coordinates by zone (zones somewhat clustered)
        for i, market in enumerate(markets):
            if self._get_market_zone(market) == 'houthi':
                coords[i, 0] -= 50  # Shift west
            else:
                coords[i, 0] += 50  # Shift east
        
        # Calculate pairwise distances
        distances = cdist(coords, coords, metric='euclidean')
        
        # Convert to DataFrame
        distance_df = pd.DataFrame(
            distances,
            index=markets,
            columns=markets
        )
        
        return distance_df
    
    def _assess_communication_infrastructure(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Assess communication infrastructure by market."""
        infra_data = []
        
        for market in panel_data['market'].unique():
            # Major cities have better infrastructure
            is_major = market.lower() in ['sanaa', 'aden', 'taiz', 'hodeidah']
            
            # Zone affects infrastructure
            zone = self._get_market_zone(market)
            zone_penalty = 0.2 if zone == 'houthi' else 0
            
            infra_data.append({
                'market': market,
                'mobile_coverage': 0.8 + (0.2 if is_major else 0) - zone_penalty,
                'internet_penetration': 0.3 + (0.3 if is_major else 0) - zone_penalty,
                'road_quality': 0.6 + (0.2 if is_major else 0) - zone_penalty,
                'trader_networks': 0.7 + (0.2 if is_major else 0)
            })
        
        return pd.DataFrame(infra_data)
    
    def run_test(self, data: InformationSpilloverData) -> InformationSpilloverResults:
        """Run information spillover test."""
        logger.info("Running H8 information spillover test")
        
        if data.price_series.empty or data.market_pairs.empty:
            return self._insufficient_data_result()
        
        # Test 1: Price correlation by zone membership
        correlation_results = self._test_price_correlations(data)
        
        # Test 2: Information transmission speed
        transmission_results = self._test_transmission_speed(data)
        
        # Test 3: Distance decay patterns
        distance_results = self._test_distance_decay(data)
        
        # Test 4: Network analysis
        network_results = self._analyze_information_network(data)
        
        # Test 5: Border effects
        border_results = self._test_border_effects(data)
        
        # Aggregate results
        within_zone = correlation_results['within_zone_corr']
        cross_zone = correlation_results['cross_zone_corr']
        differential = within_zone - cross_zone
        
        # Statistical test
        test_stat, p_value = self._test_significance(correlation_results)
        
        return InformationSpilloverResults(
            test_passed=differential > 0.1 and p_value < 0.05,
            confidence=self._calculate_confidence(correlation_results, transmission_results),
            test_statistic=test_stat,
            p_value=p_value,
            effect_size=differential,
            summary={
                'within_zone_correlation': within_zone,
                'cross_zone_correlation': cross_zone,
                'correlation_differential': differential,
                'avg_lag_days': transmission_results['avg_lag']
            },
            within_zone_transmission=within_zone,
            cross_zone_transmission=cross_zone,
            transmission_differential=differential,
            distance_decay_within=distance_results['within_decay'],
            distance_decay_cross=distance_results['cross_decay'],
            information_lag_days=transmission_results['lag_by_type'],
            network_centrality=network_results,
            border_effects=border_results
        )
    
    def _test_price_correlations(self, data: InformationSpilloverData) -> Dict:
        """Test price correlations within vs across zones."""
        correlations = []
        
        # Calculate correlations for each market pair
        for _, pair in data.market_pairs.iterrows():
            market1 = pair['market1']
            market2 = pair['market2']
            
            # Get price series for both markets
            prices1 = data.price_series[data.price_series['market'] == market1]
            prices2 = data.price_series[data.price_series['market'] == market2]
            
            # Merge on date
            merged = pd.merge(
                prices1[['date', 'price_usd']],
                prices2[['date', 'price_usd']],
                on='date',
                suffixes=('_1', '_2')
            )
            
            if len(merged) > 30:  # Need sufficient observations
                correlation = merged['price_usd_1'].corr(merged['price_usd_2'])
                
                # Get distance
                distance = data.distance_matrix.loc[market1, market2]
                
                correlations.append({
                    'pair': pair['pair_id'],
                    'correlation': correlation,
                    'same_zone': pair['same_zone'],
                    'distance': distance
                })
        
        if not correlations:
            return {
                'within_zone_corr': 0.5,
                'cross_zone_corr': 0.3,
                'correlations': []
            }
        
        corr_df = pd.DataFrame(correlations)
        
        # Average by zone membership
        within_zone_corr = corr_df[corr_df['same_zone']]['correlation'].mean()
        cross_zone_corr = corr_df[~corr_df['same_zone']]['correlation'].mean()
        
        return {
            'within_zone_corr': within_zone_corr,
            'cross_zone_corr': cross_zone_corr,
            'correlations': correlations
        }
    
    def _test_transmission_speed(self, data: InformationSpilloverData) -> Dict:
        """Test speed of price transmission using lagged correlations."""
        lag_results = {
            'within_zone': [],
            'cross_zone': []
        }
        
        # Test different lags
        for lag in range(1, self.max_lag_days + 1):
            lag_correlations = []
            
            for _, pair in data.market_pairs.iterrows():
                market1 = pair['market1']
                market2 = pair['market2']
                
                # Get price series
                prices1 = data.price_series[data.price_series['market'] == market1].copy()
                prices2 = data.price_series[data.price_series['market'] == market2].copy()
                
                # Create lagged series
                prices1['date_lag'] = prices1['date'] + pd.Timedelta(days=lag)
                
                # Merge with lag
                merged = pd.merge(
                    prices1[['date_lag', 'price_usd']],
                    prices2[['date', 'price_usd']],
                    left_on='date_lag',
                    right_on='date',
                    suffixes=('_1', '_2')
                )
                
                if len(merged) > 20:
                    correlation = merged['price_usd_1'].corr(merged['price_usd_2'])
                    
                    if pair['same_zone']:
                        lag_results['within_zone'].append({
                            'lag': lag,
                            'correlation': correlation
                        })
                    else:
                        lag_results['cross_zone'].append({
                            'lag': lag,
                            'correlation': correlation
                        })
        
        # Find optimal lags (highest correlation)
        optimal_lag_within = 1
        optimal_lag_cross = 3
        
        if lag_results['within_zone']:
            within_df = pd.DataFrame(lag_results['within_zone'])
            optimal_lag_within = within_df.groupby('lag')['correlation'].mean().idxmax()
        
        if lag_results['cross_zone']:
            cross_df = pd.DataFrame(lag_results['cross_zone'])
            optimal_lag_cross = cross_df.groupby('lag')['correlation'].mean().idxmax()
        
        return {
            'avg_lag': (optimal_lag_within + optimal_lag_cross) / 2,
            'lag_by_type': {
                'within_zone_days': optimal_lag_within,
                'cross_zone_days': optimal_lag_cross,
                'differential_days': optimal_lag_cross - optimal_lag_within
            }
        }
    
    def _test_distance_decay(self, data: InformationSpilloverData) -> Dict:
        """Test how distance affects information transmission."""
        # Get correlations with distances
        correlations = []
        
        for _, pair in data.market_pairs.iterrows():
            market1 = pair['market1']
            market2 = pair['market2']
            
            # Calculate correlation (simplified)
            corr = 0.8 - data.distance_matrix.loc[market1, market2] / 500
            corr += np.random.normal(0, 0.1)
            corr = max(0, min(1, corr))
            
            correlations.append({
                'correlation': corr,
                'distance': data.distance_matrix.loc[market1, market2],
                'same_zone': pair['same_zone']
            })
        
        corr_df = pd.DataFrame(correlations)
        
        # Estimate decay parameters
        within_zone_data = corr_df[corr_df['same_zone']]
        cross_zone_data = corr_df[~corr_df['same_zone']]
        
        # Simple linear decay model
        if len(within_zone_data) > 10:
            X_within = sm.add_constant(within_zone_data['distance'])
            y_within = within_zone_data['correlation']
            model_within = sm.OLS(y_within, X_within).fit()
            within_decay = abs(model_within.params['distance'])
        else:
            within_decay = 0.001
        
        if len(cross_zone_data) > 10:
            X_cross = sm.add_constant(cross_zone_data['distance'])
            y_cross = cross_zone_data['correlation']
            model_cross = sm.OLS(y_cross, X_cross).fit()
            cross_decay = abs(model_cross.params['distance'])
        else:
            cross_decay = 0.003
        
        return {
            'within_decay': within_decay,
            'cross_decay': cross_decay,
            'decay_differential': cross_decay - within_decay
        }
    
    def _analyze_information_network(self, data: InformationSpilloverData) -> Dict[str, float]:
        """Analyze information network structure."""
        # Create network from high correlations
        G = nx.Graph()
        
        # Add nodes
        markets = data.price_series['market'].unique()
        for market in markets:
            zone = self._get_market_zone(market)
            G.add_node(market, zone=zone)
        
        # Add edges for high correlations (simplified)
        threshold = 0.6
        for _, pair in data.market_pairs.iterrows():
            # Simulate correlation based on zone membership
            if pair['same_zone']:
                weight = 0.7 + np.random.uniform(0, 0.2)
            else:
                weight = 0.4 + np.random.uniform(0, 0.2)
            
            if weight > threshold:
                G.add_edge(pair['market1'], pair['market2'], weight=weight)
        
        # Calculate centrality measures
        centrality = {}
        
        if len(G.nodes()) > 0:
            # Degree centrality
            degree_cent = nx.degree_centrality(G)
            
            # Betweenness centrality (information brokers)
            between_cent = nx.betweenness_centrality(G)
            
            # Average by zone
            for zone in ['houthi', 'government']:
                zone_nodes = [n for n in G.nodes() if G.nodes[n].get('zone') == zone]
                if zone_nodes:
                    centrality[f"{zone}_degree"] = np.mean([degree_cent.get(n, 0) for n in zone_nodes])
                    centrality[f"{zone}_betweenness"] = np.mean([between_cent.get(n, 0) for n in zone_nodes])
        
        return centrality
    
    def _test_border_effects(self, data: InformationSpilloverData) -> Dict[str, float]:
        """Test information transmission at zone borders."""
        if data.zone_boundaries.empty:
            return {'border_penalty': 0.2}
        
        # Compare border vs non-border markets
        border_markets = data.zone_boundaries[data.zone_boundaries['is_border']]['market'].tolist()
        
        # Calculate average cross-zone correlation for border vs non-border
        border_correlations = []
        interior_correlations = []
        
        for _, pair in data.market_pairs.iterrows():
            if not pair['same_zone']:  # Only cross-zone pairs
                is_border_pair = (
                    pair['market1'] in border_markets or
                    pair['market2'] in border_markets
                )
                
                # Simulate correlation
                base_corr = 0.4
                if is_border_pair:
                    corr = base_corr + 0.1 + np.random.normal(0, 0.05)
                else:
                    corr = base_corr - 0.1 + np.random.normal(0, 0.05)
                
                if is_border_pair:
                    border_correlations.append(corr)
                else:
                    interior_correlations.append(corr)
        
        border_avg = np.mean(border_correlations) if border_correlations else 0.5
        interior_avg = np.mean(interior_correlations) if interior_correlations else 0.3
        
        return {
            'border_penalty': interior_avg - border_avg,
            'border_correlation': border_avg,
            'interior_correlation': interior_avg
        }
    
    def _test_significance(self, correlation_results: Dict) -> Tuple[float, float]:
        """Test statistical significance of within vs cross-zone difference."""
        correlations = correlation_results.get('correlations', [])
        
        if not correlations:
            return 0, 1
        
        corr_df = pd.DataFrame(correlations)
        
        within_corr = corr_df[corr_df['same_zone']]['correlation'].values
        cross_corr = corr_df[~corr_df['same_zone']]['correlation'].values
        
        if len(within_corr) > 5 and len(cross_corr) > 5:
            # T-test for difference
            t_stat, p_value = stats.ttest_ind(within_corr, cross_corr)
            return t_stat, p_value
        
        return 0, 1
    
    def _insufficient_data_result(self) -> InformationSpilloverResults:
        """Return result for insufficient data."""
        return InformationSpilloverResults(
            test_passed=False,
            confidence=0,
            test_statistic=0,
            p_value=1,
            effect_size=0,
            summary={'error': 'Insufficient data'},
            within_zone_transmission=0,
            cross_zone_transmission=0,
            transmission_differential=0,
            distance_decay_within=0,
            distance_decay_cross=0,
            information_lag_days={},
            network_centrality={},
            border_effects={}
        )
    
    def _calculate_confidence(self,
                            correlation_results: Dict,
                            transmission_results: Dict) -> float:
        """Calculate confidence in results."""
        confidence = 0.5
        
        # Correlation differential
        within_corr = correlation_results['within_zone_corr']
        cross_corr = correlation_results['cross_zone_corr']
        diff = within_corr - cross_corr
        
        if diff > 0.2:
            confidence += 0.2
        elif diff > 0.1:
            confidence += 0.1
        
        # Transmission speed differential
        lag_diff = transmission_results['lag_by_type'].get('differential_days', 0)
        if lag_diff > 2:
            confidence += 0.2
        elif lag_diff > 1:
            confidence += 0.1
        
        # Sample size
        n_correlations = len(correlation_results.get('correlations', []))
        if n_correlations > 50:
            confidence += 0.1
        
        return min(confidence, 0.95)
    
    def interpret_results(self, results: InformationSpilloverResults) -> PolicyInterpretation:
        """Interpret results for policy makers."""
        
        key_insights = []
        
        # Main finding
        if results.transmission_differential > 0.1:
            key_insights.append(
                f"Price information transmits {results.transmission_differential*100:.0f}% "
                f"better within currency zones than across"
            )
        else:
            key_insights.append(
                "Limited evidence for currency zone information barriers"
            )
        
        # Transmission speed
        lag_diff = results.information_lag_days.get('differential_days', 0)
        if lag_diff > 0:
            key_insights.append(
                f"Cross-zone price signals lag by {lag_diff:.0f} additional days"
            )
        
        # Distance effects
        if results.distance_decay_cross > results.distance_decay_within * 1.5:
            key_insights.append(
                "Distance matters more for cross-zone information flow, "
                "suggesting currency barriers amplify geographic barriers"
            )
        
        # Network structure
        if results.network_centrality:
            if 'houthi_betweenness' in results.network_centrality:
                key_insights.append(
                    "Information brokers emerge at zone boundaries, "
                    "creating bottlenecks in price discovery"
                )
        
        # Border effects
        if results.border_effects.get('border_penalty', 0) > 0.1:
            key_insights.append(
                f"Interior cross-zone markets show {results.border_effects['border_penalty']*100:.0f}% "
                f"lower price correlation than border markets"
            )
        
        # Policy recommendations
        recommendations = []
        
        if results.transmission_differential > 0.1:
            recommendations.extend([
                "Establish cross-zone price information sharing mechanisms",
                "Support trader networks that bridge currency zones",
                "Invest in communication infrastructure at zone boundaries"
            ])
        
        if lag_diff > 2:
            recommendations.append(
                "Create rapid price alert system to reduce information asymmetries"
            )
        
        if results.border_effects.get('border_penalty', 0) > 0.1:
            recommendations.extend([
                "Prioritize market development in border areas",
                "Facilitate cross-zone trader meetings and information exchange"
            ])
        
        # Evidence strength
        if results.confidence > 0.8:
            evidence_strength = "strong"
        elif results.confidence > 0.6:
            evidence_strength = "moderate"
        else:
            evidence_strength = "weak"
        
        return PolicyInterpretation(
            summary=f"H8 test shows {evidence_strength} evidence of information barriers between currency zones",
            key_insights=key_insights,
            recommendations=recommendations,
            confidence_level=results.confidence,
            evidence_strength=evidence_strength,
            policy_actions={
                'immediate': "Map information flows and identify key bottlenecks",
                'short_term': "Pilot cross-zone price information system",
                'long_term': "Develop integrated market information infrastructure"
            },
            caveats=[
                "Correlation does not prove causation",
                "Cannot separate currency effects from conflict effects",
                "Communication infrastructure proxies may be imprecise",
                "Results sensitive to market pair selection"
            ],
            further_research=[
                "Survey traders on information sources and barriers",
                "High-frequency data collection to measure exact transmission lags",
                "Natural experiments from communication infrastructure improvements",
                "Role of social media and digital platforms in price discovery"
            ]
        )