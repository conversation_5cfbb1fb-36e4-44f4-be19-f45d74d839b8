"""
H9: Threshold Effects in Market Integration

Tests whether market integration exhibits non-linear threshold effects,
with integration breaking down beyond ~100% exchange rate differential.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy import stats, optimize
import statsmodels.api as sm
from sklearn.metrics import r2_score
import warnings

from .hypothesis_framework import (
    HypothesisTest, TestData, TestResults, PolicyInterpretation
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ThresholdData(TestData):
    """Data structure for threshold analysis."""
    integration_measures: Optional[pd.DataFrame] = None
    exchange_differentials: Optional[pd.DataFrame] = None
    market_characteristics: Optional[pd.DataFrame] = None
    time_series_data: Optional[pd.DataFrame] = None
    regime_indicators: Optional[pd.DataFrame] = None


@dataclass
class ThresholdResults:
    """Results from threshold analysis."""
    
    Uses composition with TestResults for core fields while adding
    hypothesis-specific fields.
    """
    # Core test results (composition, not inheritance)
    base_results: TestResults
    
    # Hypothesis-specific fields
    optimal_threshold: Optional[float] = None
    regime1_integration: Optional[float] = None
    regime2_integration: Optional[float] = None
    threshold_effect_size: Optional[float] = None
    nonlinearity_r2_improvement: Optional[float] = None
    regime_persistence: Optional[Dict[str, float]] = None
    heterogeneous_effects: Optional[Dict[str, float]] = None
    structural_break_date: Optional[pd.Timestamp] = None

    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, combining base and specific results."""
        result = self.base_results.to_dict()
        result.update({
            'optimal_threshold': self.optimal_threshold,
            'regime1_integration': self.regime1_integration,
            'regime2_integration': self.regime2_integration,
            'threshold_effect_size': self.threshold_effect_size,
            'nonlinearity_r2_improvement': self.nonlinearity_r2_improvement,
            'regime_persistence': self.regime_persistence,
            'heterogeneous_effects': self.heterogeneous_effects,
            'structural_break_date': self.structural_break_date
        })
        return result

class H9ThresholdEffectsTest(HypothesisTest):
    """
    Tests non-linear threshold effects in market integration.
    
    H9: Large exchange differentials -> Regime switch
        - Threshold around 100% differential
        - Non-linear relationship
        - Fundamentally different dynamics in each regime
    """
    
    def __init__(self):
        super().__init__(
            hypothesis_id="H9",
            name="Threshold Effects in Integration",
            description="Tests for non-linear regime switching in market relationships"
        )
        self.min_observations = 100
        self.threshold_search_range = (50, 200)  # % exchange differential
        self.significance_level = 0.05
    
    def prepare_data(self, panel_data: pd.DataFrame) -> ThresholdData:
        """Prepare data for threshold analysis."""
        logger.info("Preparing data for H9 threshold effects test")
        
        # Calculate integration measures
        integration_measures = self._calculate_integration_measures(panel_data)
        
        # Calculate exchange differentials
        exchange_differentials = self._calculate_exchange_differentials(panel_data)
        
        # Extract market characteristics
        market_characteristics = self._extract_market_characteristics(panel_data)
        
        # Prepare time series for regime analysis
        time_series_data = self._prepare_time_series(panel_data)
        
        # Identify regime indicators
        regime_indicators = self._identify_regimes(exchange_differentials)
        
        return ThresholdData(
            integration_measures=integration_measures,
            exchange_differentials=exchange_differentials,
            market_characteristics=market_characteristics,
            time_series_data=time_series_data,
            regime_indicators=regime_indicators
        )
    
    def _calculate_integration_measures(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate various measures of market integration."""
        integration_data = []
        
        # Get unique market pairs
        markets = panel_data['market'].unique() if 'market' in panel_data.columns else []
        
        for i, market1 in enumerate(markets):
            for market2 in markets[i+1:]:
                # Extract price series for both markets
                prices1 = panel_data[panel_data['market'] == market1].set_index('date')['price_usd']
                prices2 = panel_data[panel_data['market'] == market2].set_index('date')['price_usd']
                
                # Align series
                aligned_prices = pd.concat([prices1, prices2], axis=1, join='inner')
                aligned_prices.columns = ['price1', 'price2']
                
                if len(aligned_prices) < 30:
                    continue
                
                # Calculate rolling correlation
                rolling_corr = aligned_prices['price1'].rolling(window=12).corr(aligned_prices['price2'])
                
                # Calculate price differential
                price_diff = abs(aligned_prices['price1'] - aligned_prices['price2'])
                price_diff_pct = price_diff / aligned_prices[['price1', 'price2']].mean(axis=1) * 100
                
                # Estimate adjustment speed (simplified error correction)
                price_changes = aligned_prices.diff()
                if len(price_changes.dropna()) > 10:
                    # Simple ECM: Δp1 = α(p2 - p1) + ε
                    price_gap = aligned_prices['price2'] - aligned_prices['price1']
                    X = sm.add_constant(price_gap.shift(1))
                    y = price_changes['price1']
                    
                    try:
                        model = sm.OLS(y.dropna(), X.dropna()).fit()
                        adjustment_speed = abs(model.params.iloc[1])
                    except:
                        adjustment_speed = 0
                else:
                    adjustment_speed = 0
                
                # Store results
                for date in aligned_prices.index:
                    if pd.notna(rolling_corr.loc[date]):
                        integration_data.append({
                            'date': date,
                            'market_pair': f"{market1}_{market2}",
                            'price_correlation': rolling_corr.loc[date],
                            'price_diff_pct': price_diff_pct.loc[date],
                            'adjustment_speed': adjustment_speed,
                            'integration_index': rolling_corr.loc[date] * adjustment_speed
                        })
        
        return pd.DataFrame(integration_data)
    
    def _calculate_exchange_differentials(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate exchange rate differentials between currency zones."""
        # Define exchange rates by zone (simplified)
        zone_rates = {
            'houthi': 535,
            'government': 2000,
            'contested': 1200
        }
        
        # Get zone information
        if 'currency_zone' in panel_data.columns:
            zone_data = panel_data[['market', 'currency_zone']].drop_duplicates()
        else:
            # Default zones based on market names
            zone_data = self._infer_currency_zones(panel_data)
        
        differentials = []
        
        markets = zone_data['market'].unique()
        for i, market1 in enumerate(markets):
            zone1 = zone_data[zone_data['market'] == market1]['currency_zone'].iloc[0]
            rate1 = zone_rates.get(zone1, 1000)
            
            for market2 in markets[i+1:]:
                zone2 = zone_data[zone_data['market'] == market2]['currency_zone'].iloc[0]
                rate2 = zone_rates.get(zone2, 1000)
                
                # Calculate differential
                if rate1 > 0:
                    diff_pct = abs(rate2 - rate1) / min(rate1, rate2) * 100
                else:
                    diff_pct = 0
                
                differentials.append({
                    'market_pair': f"{market1}_{market2}",
                    'zone1': zone1,
                    'zone2': zone2,
                    'rate1': rate1,
                    'rate2': rate2,
                    'exchange_diff_pct': diff_pct,
                    'same_zone': zone1 == zone2
                })
        
        return pd.DataFrame(differentials)
    
    def _infer_currency_zones(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Infer currency zones from market names."""
        zone_mapping = {
            'Sana\'a': 'houthi',
            'Sa\'ada': 'houthi',
            'Hajjah': 'houthi',
            'Aden': 'government',
            'Mukalla': 'government',
            'Marib': 'government',
            'Taiz': 'contested',
            'Al Hudaydah': 'contested'
        }
        
        zones = []
        for market in panel_data['market'].unique():
            # Find matching zone
            zone = 'unknown'
            for key, value in zone_mapping.items():
                if key.lower() in market.lower():
                    zone = value
                    break
            
            zones.append({
                'market': market,
                'currency_zone': zone
            })
        
        return pd.DataFrame(zones)
    
    def _extract_market_characteristics(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Extract market pair characteristics."""
        markets = panel_data['market'].unique() if 'market' in panel_data.columns else []
        
        characteristics = []
        
        # Define market types based on volume/importance
        major_markets = ['Sana\'a', 'Aden', 'Taiz']
        
        for i, market1 in enumerate(markets):
            for market2 in markets[i+1:]:
                # Determine pair type
                if any(m in market1 for m in major_markets) and any(m in market2 for m in major_markets):
                    pair_type = 'high_volume'
                elif any(m in market1 for m in major_markets) or any(m in market2 for m in major_markets):
                    pair_type = 'medium_volume'
                else:
                    pair_type = 'low_volume'
                
                # Calculate distance (simplified)
                distance = self._calculate_distance(market1, market2)
                
                characteristics.append({
                    'market_pair': f"{market1}_{market2}",
                    'pair_type': pair_type,
                    'distance_km': distance,
                    'is_major_corridor': pair_type == 'high_volume'
                })
        
        return pd.DataFrame(characteristics)
    
    def _calculate_distance(self, market1: str, market2: str) -> float:
        """Calculate distance between markets (simplified)."""
        # Simplified distance matrix
        distances = {
            ('Sana\'a', 'Aden'): 320,
            ('Sana\'a', 'Taiz'): 250,
            ('Sana\'a', 'Sa\'ada'): 240,
            ('Aden', 'Mukalla'): 480,
            ('Taiz', 'Al Hudaydah'): 150,
        }
        
        key1 = (market1, market2)
        key2 = (market2, market1)
        
        if key1 in distances:
            return distances[key1]
        elif key2 in distances:
            return distances[key2]
        else:
            return 200  # Default
    
    def _prepare_time_series(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Prepare time series data for regime analysis."""
        # Create balanced panel for main market pairs
        if 'date' in panel_data.columns:
            time_series = panel_data.pivot_table(
                index='date',
                columns='market',
                values='price_usd',
                aggfunc='mean'
            )
            
            # Calculate returns
            returns = time_series.pct_change()
            
            # Add to dataframe
            time_data = []
            for date in time_series.index:
                time_data.append({
                    'date': date,
                    'mean_price': time_series.loc[date].mean(),
                    'price_dispersion': time_series.loc[date].std(),
                    'mean_return': returns.loc[date].mean() if date in returns.index else 0,
                    'return_correlation': returns.loc[date].corr().mean() if date in returns.index else 0
                })
            
            return pd.DataFrame(time_data)
        else:
            return pd.DataFrame()
    
    def _identify_regimes(self, exchange_differentials: pd.DataFrame) -> pd.DataFrame:
        """Identify market regimes based on exchange differentials."""
        # For each market pair, identify regime
        regime_data = []
        
        for _, row in exchange_differentials.iterrows():
            # Simple regime classification based on differential
            if row['exchange_diff_pct'] < 50:
                regime = 'integrated'
                regime_code = 1
            elif row['exchange_diff_pct'] < 100:
                regime = 'transitional'
                regime_code = 2
            else:
                regime = 'fragmented'
                regime_code = 3
            
            regime_data.append({
                'market_pair': row['market_pair'],
                'regime': regime,
                'regime_code': regime_code,
                'exchange_diff_pct': row['exchange_diff_pct']
            })
        
        return pd.DataFrame(regime_data)
    
    def run_test(self, data: ThresholdData) -> ThresholdResults:
        """Run threshold effects test."""
        logger.info("Running H9 threshold effects test")
        
        if data.integration_measures.empty or data.exchange_differentials.empty:
            return self._insufficient_data_result()
        
        # Test 1: Find optimal threshold
        threshold_results = self._find_optimal_threshold(data)
        
        # Test 2: Test non-linearity
        nonlinearity_results = self._test_nonlinearity(data)
        
        # Test 3: Regime-specific dynamics
        regime_dynamics = self._test_regime_dynamics(data, threshold_results['threshold'])
        
        # Test 4: Heterogeneous effects
        heterogeneous_effects = self._test_heterogeneous_effects(data, threshold_results['threshold'])
        
        # Test 5: Structural break analysis
        structural_break = self._test_structural_break(data)
        
        # Statistical tests
        test_stat, p_value = self._calculate_test_statistics(
            threshold_results, regime_dynamics
        )
        
        # Determine if hypothesis is supported
        test_passed = (
            threshold_results['threshold'] is not None and
            80 < threshold_results['threshold'] < 120 and
            threshold_results['effect_size'] > 0.5 and
            p_value < 0.05
        )
        
        return ThresholdResults(
            test_passed=test_passed,
            confidence=self._calculate_confidence(
                threshold_results, nonlinearity_results, regime_dynamics
            ),
            test_statistic=test_stat,
            p_value=p_value,
            effect_size=threshold_results['effect_size'],
            summary={
                'threshold': threshold_results['threshold'],
                'regime1_mean': regime_dynamics['regime1_mean'],
                'regime2_mean': regime_dynamics['regime2_mean'],
                'r2_improvement': nonlinearity_results['r2_improvement']
            },
            optimal_threshold=threshold_results['threshold'],
            regime1_integration=regime_dynamics['regime1_mean'],
            regime2_integration=regime_dynamics['regime2_mean'],
            threshold_effect_size=threshold_results['effect_size'],
            nonlinearity_r2_improvement=nonlinearity_results['r2_improvement'],
            regime_persistence=regime_dynamics['persistence'],
            heterogeneous_effects=heterogeneous_effects,
            structural_break_date=structural_break
        )
    
    def _find_optimal_threshold(self, data: ThresholdData) -> Dict:
        """Find optimal threshold value using grid search."""
        # Merge integration and exchange differential data
        analysis_df = data.integration_measures.merge(
            data.exchange_differentials,
            on='market_pair',
            how='inner'
        )
        
        if len(analysis_df) < self.min_observations:
            return {
                'threshold': 100,  # Default
                'effect_size': 0,
                'r_squared': 0
            }
        
        # Grid search over potential thresholds
        thresholds = np.arange(
            self.threshold_search_range[0],
            self.threshold_search_range[1],
            10
        )
        
        best_threshold = None
        best_criterion = -np.inf
        
        for threshold in thresholds:
            # Split data by threshold
            below = analysis_df[analysis_df['exchange_diff_pct'] < threshold]
            above = analysis_df[analysis_df['exchange_diff_pct'] >= threshold]
            
            if len(below) < 20 or len(above) < 20:
                continue
            
            # Calculate sum of squared residuals for piecewise model
            analysis_df['regime'] = (analysis_df['exchange_diff_pct'] >= threshold).astype(int)
            analysis_df['gap_above'] = (
                analysis_df['exchange_diff_pct'] - threshold
            ) * analysis_df['regime']
            
            # Fit piecewise linear model
            X = analysis_df[['exchange_diff_pct', 'gap_above']]
            X = sm.add_constant(X)
            y = analysis_df['integration_index']
            
            try:
                model = sm.OLS(y, X).fit()
                
                # Use AIC as criterion (lower is better)
                criterion = -model.aic  # Negative so higher is better
                
                # Also require significant slope change
                if model.pvalues['gap_above'] < 0.1:
                    if criterion > best_criterion:
                        best_criterion = criterion
                        best_threshold = threshold
            except:
                continue
        
        if best_threshold is None:
            best_threshold = 100  # Default
        
        # Calculate effect size at optimal threshold
        below = analysis_df[analysis_df['exchange_diff_pct'] < best_threshold]
        above = analysis_df[analysis_df['exchange_diff_pct'] >= best_threshold]
        
        mean_below = below['integration_index'].mean()
        mean_above = above['integration_index'].mean()
        
        # Cohen's d
        pooled_std = np.sqrt((below['integration_index'].var() + above['integration_index'].var()) / 2)
        effect_size = (mean_below - mean_above) / pooled_std if pooled_std > 0 else 0
        
        return {
            'threshold': best_threshold,
            'effect_size': effect_size,
            'mean_below': mean_below,
            'mean_above': mean_above
        }
    
    def _test_nonlinearity(self, data: ThresholdData) -> Dict:
        """Test for non-linear relationship."""
        # Merge data
        analysis_df = data.integration_measures.merge(
            data.exchange_differentials,
            on='market_pair',
            how='inner'
        )
        
        if len(analysis_df) < self.min_observations:
            return {'r2_improvement': 0, 'nonlinear': False}
        
        X = analysis_df['exchange_diff_pct']
        y = analysis_df['integration_index']
        
        # Fit linear model
        X_linear = sm.add_constant(X)
        try:
            linear_model = sm.OLS(y, X_linear).fit()
            linear_r2 = linear_model.rsquared
        except:
            linear_r2 = 0
        
        # Fit polynomial model
        X_poly = pd.DataFrame({
            'x': X,
            'x2': X**2,
            'x3': X**3
        })
        X_poly = sm.add_constant(X_poly)
        
        try:
            poly_model = sm.OLS(y, X_poly).fit()
            poly_r2 = poly_model.rsquared
        except:
            poly_r2 = 0
        
        # Fit exponential decay model
        try:
            def exp_decay(x, a, b, c):
                return a * np.exp(-b * x) + c
            
            popt, _ = optimize.curve_fit(
                exp_decay, X, y,
                p0=[1, 0.01, 0],
                maxfev=5000
            )
            
            y_pred = exp_decay(X, *popt)
            exp_r2 = r2_score(y, y_pred)
        except:
            exp_r2 = 0
        
        # Calculate improvement
        best_nonlinear_r2 = max(poly_r2, exp_r2)
        r2_improvement = (best_nonlinear_r2 - linear_r2) / linear_r2 if linear_r2 > 0 else 0
        
        return {
            'r2_improvement': r2_improvement,
            'linear_r2': linear_r2,
            'best_nonlinear_r2': best_nonlinear_r2,
            'nonlinear': r2_improvement > 0.1
        }
    
    def _test_regime_dynamics(self, data: ThresholdData, threshold: float) -> Dict:
        """Test regime-specific market dynamics."""
        # Merge data
        analysis_df = data.integration_measures.merge(
            data.exchange_differentials,
            on='market_pair',
            how='inner'
        )
        
        # Split by regime
        regime1 = analysis_df[analysis_df['exchange_diff_pct'] < threshold]
        regime2 = analysis_df[analysis_df['exchange_diff_pct'] >= threshold]
        
        results = {
            'regime1_mean': regime1['integration_index'].mean() if len(regime1) > 0 else 0,
            'regime2_mean': regime2['integration_index'].mean() if len(regime2) > 0 else 0,
            'regime1_std': regime1['integration_index'].std() if len(regime1) > 0 else 0,
            'regime2_std': regime2['integration_index'].std() if len(regime2) > 0 else 0,
        }
        
        # Test variance difference
        if len(regime1) > 10 and len(regime2) > 10:
            f_stat, p_value = stats.levene(
                regime1['integration_index'],
                regime2['integration_index']
            )
            results['variance_test_pvalue'] = p_value
        else:
            results['variance_test_pvalue'] = 1
        
        # Estimate persistence (autocorrelation)
        persistence = {}
        
        if 'date' in analysis_df.columns:
            for regime_name, regime_data in [('regime1', regime1), ('regime2', regime2)]:
                if len(regime_data) > 20:
                    # Sort by date and calculate autocorrelation
                    regime_sorted = regime_data.sort_values('date')
                    autocorr = regime_sorted['integration_index'].autocorr(lag=1)
                    persistence[regime_name] = autocorr if not np.isnan(autocorr) else 0
                else:
                    persistence[regime_name] = 0
        
        results['persistence'] = persistence
        
        return results
    
    def _test_heterogeneous_effects(self, data: ThresholdData, threshold: float) -> Dict[str, float]:
        """Test heterogeneous threshold effects by market characteristics."""
        # Merge all data
        analysis_df = data.integration_measures.merge(
            data.exchange_differentials,
            on='market_pair',
            how='inner'
        ).merge(
            data.market_characteristics,
            on='market_pair',
            how='inner'
        )
        
        effects = {}
        
        # Test by pair type
        for pair_type in analysis_df['pair_type'].unique():
            pair_data = analysis_df[analysis_df['pair_type'] == pair_type]
            
            below = pair_data[pair_data['exchange_diff_pct'] < threshold]
            above = pair_data[pair_data['exchange_diff_pct'] >= threshold]
            
            if len(below) > 5 and len(above) > 5:
                mean_below = below['integration_index'].mean()
                mean_above = above['integration_index'].mean()
                
                # Percentage drop
                effect = (mean_below - mean_above) / mean_below * 100 if mean_below > 0 else 0
                effects[f'{pair_type}_effect'] = effect
        
        # Test same zone vs cross zone
        same_zone = analysis_df[analysis_df['same_zone']]
        cross_zone = analysis_df[~analysis_df['same_zone']]
        
        for zone_type, zone_data in [('same_zone', same_zone), ('cross_zone', cross_zone)]:
            below = zone_data[zone_data['exchange_diff_pct'] < threshold]
            above = zone_data[zone_data['exchange_diff_pct'] >= threshold]
            
            if len(below) > 5 and len(above) > 5:
                mean_below = below['integration_index'].mean()
                mean_above = above['integration_index'].mean()
                
                effect = (mean_below - mean_above) / mean_below * 100 if mean_below > 0 else 0
                effects[f'{zone_type}_effect'] = effect
        
        return effects
    
    def _test_structural_break(self, data: ThresholdData) -> Optional[pd.Timestamp]:
        """Test for structural break in time series."""
        if data.time_series_data.empty or 'date' not in data.time_series_data.columns:
            return None
        
        time_series = data.time_series_data.sort_values('date')
        
        if len(time_series) < 50:
            return None
        
        # Simple CUSUM test for structural break
        y = time_series['mean_price'].values
        n = len(y)
        
        # Calculate recursive residuals
        cumsum_stats = []
        
        for t in range(20, n-20):  # Need minimum observations on each side
            # Split series
            y1 = y[:t]
            y2 = y[t:]
            
            # Test for mean difference
            t_stat, p_value = stats.ttest_ind(y1, y2)
            
            cumsum_stats.append({
                'date': time_series.iloc[t]['date'],
                't_stat': abs(t_stat),
                'p_value': p_value
            })
        
        if cumsum_stats:
            cumsum_df = pd.DataFrame(cumsum_stats)
            
            # Find maximum statistic
            max_idx = cumsum_df['t_stat'].idxmax()
            max_stat = cumsum_df.loc[max_idx]
            
            if max_stat['p_value'] < 0.01:  # Strong evidence of break
                return max_stat['date']
        
        return None
    
    def _calculate_test_statistics(self, 
                                 threshold_results: Dict,
                                 regime_dynamics: Dict) -> Tuple[float, float]:
        """Calculate test statistics for threshold hypothesis."""
        # Test statistic: standardized difference in means
        mean1 = regime_dynamics['regime1_mean']
        mean2 = regime_dynamics['regime2_mean']
        std1 = regime_dynamics['regime1_std']
        std2 = regime_dynamics['regime2_std']
        
        if std1 > 0 and std2 > 0:
            # Welch's t-test
            se = np.sqrt(std1**2/100 + std2**2/100)  # Assume n=100 for each
            t_stat = (mean1 - mean2) / se if se > 0 else 0
            
            # Approximate degrees of freedom
            df = 100  # Simplified
            p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df))
        else:
            t_stat = 0
            p_value = 1
        
        return t_stat, p_value
    
    def _insufficient_data_result(self) -> ThresholdResults:
        """Return result for insufficient data."""
        return ThresholdResults(
            test_passed=False,
            confidence=0,
            test_statistic=0,
            p_value=1,
            effect_size=0,
            summary={'error': 'Insufficient data'},
            optimal_threshold=100,
            regime1_integration=0,
            regime2_integration=0,
            threshold_effect_size=0,
            nonlinearity_r2_improvement=0,
            regime_persistence={},
            heterogeneous_effects={},
            structural_break_date=None
        )
    
    def _calculate_confidence(self,
                            threshold_results: Dict,
                            nonlinearity_results: Dict,
                            regime_dynamics: Dict) -> float:
        """Calculate confidence in results."""
        confidence = 0.5
        
        # Threshold near expected value
        if 80 < threshold_results['threshold'] < 120:
            confidence += 0.15
        
        # Large effect size
        if threshold_results['effect_size'] > 0.8:
            confidence += 0.15
        elif threshold_results['effect_size'] > 0.5:
            confidence += 0.1
        
        # Strong non-linearity
        if nonlinearity_results['r2_improvement'] > 0.2:
            confidence += 0.1
        
        # Different regime dynamics
        if regime_dynamics.get('variance_test_pvalue', 1) < 0.05:
            confidence += 0.1
        
        # Clear persistence difference
        persistence = regime_dynamics.get('persistence', {})
        if len(persistence) == 2:
            persist_diff = abs(persistence.get('regime1', 0) - persistence.get('regime2', 0))
            if persist_diff > 0.2:
                confidence += 0.1
        
        return min(confidence, 0.95)
    
    def interpret_results(self, results: ThresholdResults) -> PolicyInterpretation:
        """Interpret results for policy makers."""
        
        key_insights = []
        
        # Main finding
        if results.test_passed:
            key_insights.append(
                f"Market integration shows clear threshold effect at "
                f"{results.optimal_threshold:.0f}% exchange rate differential"
            )
            
            # Effect size
            pct_drop = (results.regime1_integration - results.regime2_integration) / \
                      results.regime1_integration * 100 if results.regime1_integration > 0 else 0
            
            key_insights.append(
                f"Integration drops {pct_drop:.0f}% when exchange differential "
                f"exceeds threshold (from {results.regime1_integration:.2f} to "
                f"{results.regime2_integration:.2f})"
            )
        else:
            key_insights.append(
                "Limited evidence for clear threshold effect in market integration"
            )
        
        # Non-linearity
        if results.nonlinearity_r2_improvement > 0.1:
            key_insights.append(
                f"Non-linear models explain {results.nonlinearity_r2_improvement*100:.0f}% "
                f"more variance than linear models"
            )
        
        # Regime persistence
        if results.regime_persistence:
            if results.regime_persistence.get('regime2', 0) > results.regime_persistence.get('regime1', 0):
                key_insights.append(
                    "Price shocks persist longer in fragmented regime, indicating "
                    "breakdown of arbitrage mechanisms"
                )
        
        # Heterogeneous effects
        if results.heterogeneous_effects:
            same_zone_effect = results.heterogeneous_effects.get('same_zone_effect', 0)
            cross_zone_effect = results.heterogeneous_effects.get('cross_zone_effect', 0)
            
            if same_zone_effect < cross_zone_effect * 0.5:
                key_insights.append(
                    f"Same-zone market pairs show {(1 - same_zone_effect/cross_zone_effect)*100:.0f}% "
                    f"less threshold sensitivity"
                )
        
        # Structural break
        if results.structural_break_date:
            key_insights.append(
                f"Structural break detected around {results.structural_break_date.strftime('%Y-%m')}, "
                f"suggesting regime shift"
            )
        
        # Policy recommendations
        recommendations = []
        
        if results.test_passed:
            if results.optimal_threshold < 100:
                recommendations.append(
                    f"Critical threshold is {results.optimal_threshold:.0f}% - "
                    f"prioritize interventions before reaching this level"
                )
            
            recommendations.extend([
                "Monitor exchange rate gaps closely - small changes near threshold have large effects",
                "Develop contingency plans for markets approaching threshold",
                "Focus integration efforts on keeping differentials below critical level"
            ])
        
        if results.heterogeneous_effects.get('high_volume_effect', 100) < 50:
            recommendations.append(
                "Prioritize maintaining high-volume corridors which show more resilience"
            )
        
        if results.regime_persistence.get('regime2', 0) > 0.7:
            recommendations.append(
                "Once fragmentation occurs, recovery is slow - prevention is critical"
            )
        
        # Evidence strength
        if results.confidence > 0.8:
            evidence_strength = "strong"
        elif results.confidence > 0.6:
            evidence_strength = "moderate"
        else:
            evidence_strength = "weak"
        
        return PolicyInterpretation(
            summary=f"H9 test shows {evidence_strength} evidence for threshold effects in integration",
            key_insights=key_insights,
            recommendations=recommendations,
            confidence_level=results.confidence,
            evidence_strength=evidence_strength,
            policy_actions={
                'immediate': "Identify market pairs approaching critical threshold",
                'short_term': "Implement early warning system for threshold breaches",
                'long_term': "Design policies to maintain exchange differentials below threshold"
            },
            caveats=[
                "Threshold may vary by commodity and market characteristics",
                "Analysis assumes symmetric effects (threshold same for increasing/decreasing)",
                "Does not capture all dimensions of market fragmentation",
                "Time-varying thresholds not modeled"
            ],
            further_research=[
                "Commodity-specific threshold estimation",
                "Time-varying threshold models",
                "Asymmetric threshold effects (different for entry/exit)",
                "Optimal policy response near thresholds"
            ]
        )