"""
Hypothesis Testing Framework for Yemen Market Integration

This module provides the base infrastructure for testing all research hypotheses
(H1-H10, S1, N1, P1) in a systematic and reproducible manner.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from scipy import stats

from src.core.domain.market.entities import PanelData
from src.core.utils.logging import get_logger

# Import robustness framework if available
try:
    from ..robustness.comprehensive_framework import ComprehensiveRobustnessFramework, RobustTestResults
    ROBUSTNESS_AVAILABLE = True
except ImportError:
    ROBUSTNESS_AVAILABLE = False
    logger.warning("Robustness framework not available - tests will run without comprehensive robustness checks")


logger = get_logger(__name__)


class HypothesisOutcome(Enum):
    """Possible outcomes of hypothesis tests - NEUTRAL framing."""
    NULL_REJECTED = "null_rejected"           # Null hypothesis rejected at significance level
    FAIL_TO_REJECT_NULL = "fail_to_reject_null"  # Cannot reject null hypothesis
    INSUFFICIENT_POWER = "insufficient_power"     # Test lacks statistical power
    INCONCLUSIVE = "inconclusive"            # Mixed or unclear evidence


class TestRequirement(Enum):
    """Data requirements for hypothesis tests."""
    PRICE_DATA = "price_data"
    EXCHANGE_RATES = "exchange_rates"
    CONFLICT_DATA = "conflict_data"
    AID_DATA = "aid_data"
    CONTROL_CHANGES = "control_changes"
    DISPLACEMENT_DATA = "displacement_data"
    TRADER_NETWORK = "trader_network"
    FISCAL_DATA = "fiscal_data"


@dataclass
class TestData:
    """Container for data required by hypothesis tests."""
    panel_data: Optional[PanelData] = None
    exchange_rates: Optional[pd.DataFrame] = None
    conflict_events: Optional[pd.DataFrame] = None
    aid_distribution: Optional[pd.DataFrame] = None
    control_changes: Optional[pd.DataFrame] = None
    displacement: Optional[pd.DataFrame] = None
    network_metrics: Optional[pd.DataFrame] = None
    fiscal_indicators: Optional[pd.DataFrame] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class TestResults:
    """Base results from hypothesis test execution.
    
    This class contains the core fields that all hypothesis tests must provide.
    Specific hypothesis tests should create their own result classes that
    contain a TestResults instance via composition rather than inheritance.
    """
    hypothesis_id: str
    outcome: HypothesisOutcome
    test_statistic: float
    p_value: float
    alpha: float  # Significance level used for test (e.g., 0.05)
    effect_size: Optional[float] = None
    confidence_interval: Optional[Tuple[float, float]] = None
    ci_level: float = 0.95  # Confidence interval level (e.g., 0.95 for 95% CI)
    statistical_power: Optional[float] = None  # Post-hoc power analysis
    minimum_detectable_effect: Optional[float] = None  # MDE at 80% power
    adjusted_p_value: Optional[float] = None  # Multiple testing corrected
    fdr_rejected: Optional[bool] = None  # False discovery rate rejection
    n_observations: Optional[int] = None  # Sample size used
    degrees_of_freedom: Optional[int] = None  # Degrees of freedom
    diagnostic_tests: Optional[Dict[str, float]] = None
    robustness_checks: Optional[Dict[str, Any]] = None
    detailed_results: Optional[Dict[str, Any]] = None
    specification_curve_results: Optional[Dict[str, Any]] = None
    bias_detection_results: Optional[Dict[str, Any]] = None  # Bias checks
    comprehensive_robustness: Optional['RobustTestResults'] = None  # Full robustness results
    
    def __post_init__(self):
        """Validate consistency of test results."""
        # Ensure outcome matches p-value and alpha
        if self.p_value < self.alpha and self.outcome == HypothesisOutcome.FAIL_TO_REJECT_NULL:
            raise ValueError(
                f"Inconsistent results: p-value {self.p_value} < α {self.alpha} "
                f"but outcome is {self.outcome.value}"
            )
        if self.p_value >= self.alpha and self.outcome == HypothesisOutcome.NULL_REJECTED:
            raise ValueError(
                f"Inconsistent results: p-value {self.p_value} >= α {self.alpha} "
                f"but outcome is {self.outcome.value}"
            )
            
        # Validate confidence interval matches CI level
        if self.confidence_interval and self.ci_level:
            if not (0 < self.ci_level < 1):
                raise ValueError(f"CI level must be between 0 and 1, got {self.ci_level}")
                
        # Validate statistical power
        if self.statistical_power is not None:
            if not (0 <= self.statistical_power <= 1):
                raise ValueError(f"Statistical power must be between 0 and 1, got {self.statistical_power}")
                
        # Check for required fields when outcome is based on power
        if self.outcome == HypothesisOutcome.INSUFFICIENT_POWER:
            if self.statistical_power is None or self.statistical_power >= 0.80:
                raise ValueError(
                    "Outcome is INSUFFICIENT_POWER but power is not low or not provided"
                )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert results to dictionary format."""
        return {
            'hypothesis_id': self.hypothesis_id,
            'outcome': self.outcome.value,
            'test_statistic': self.test_statistic,
            'p_value': self.p_value,
            'alpha': self.alpha,
            'effect_size': self.effect_size,
            'confidence_interval': self.confidence_interval,
            'ci_level': self.ci_level,
            'statistical_power': self.statistical_power,
            'minimum_detectable_effect': self.minimum_detectable_effect,
            'adjusted_p_value': self.adjusted_p_value,
            'fdr_rejected': self.fdr_rejected,
            'n_observations': self.n_observations,
            'degrees_of_freedom': self.degrees_of_freedom,
            'diagnostic_tests': self.diagnostic_tests,
            'robustness_checks': self.robustness_checks,
            'detailed_results': self.detailed_results,
            'specification_curve_results': self.specification_curve_results,
            'bias_detection_results': self.bias_detection_results
        }


@dataclass
class PolicyInterpretation:
    """Neutral interpretation of test results for decision-making."""
    statistical_summary: str  # What the data shows
    findings: List[str]  # Key statistical findings
    considerations: List[str]  # Points to consider (not recommendations)
    uncertainty_statement: str  # Statement about uncertainty/limitations
    limitations: List[str]  # Study limitations and caveats
    data_quality_notes: List[str]  # Notes on data quality issues
    visualizations: Optional[Dict[str, Any]] = None


class HypothesisTest(ABC):
    """Base class for all hypothesis tests."""
    
    def __init__(self, hypothesis_id: str, description: str, 
                 enable_robustness: bool = True):
        """Initialize hypothesis test.
        
        Args:
            hypothesis_id: Unique identifier (e.g., 'H1', 'H2', etc.)
            description: Brief description of the hypothesis
            enable_robustness: Whether to enable comprehensive robustness testing
        """
        self.hypothesis_id = hypothesis_id
        self.description = description
        self.required_data = self._define_requirements()
        self.enable_robustness = enable_robustness and ROBUSTNESS_AVAILABLE
        
        # Initialize bias detector for this test
        from .bias_detection import BiasDetector
        self.bias_detector = BiasDetector(
            analysis_log_path=f".hypothesis_logs/{hypothesis_id}_log.json"
        )
        
        # Initialize robustness framework if enabled
        if self.enable_robustness:
            self.robustness_framework = ComprehensiveRobustnessFramework(
                project_name=f"Yemen Market Integration - {hypothesis_id}",
                output_dir=f"results/robustness/{hypothesis_id}"
            )
        
    @abstractmethod
    def _define_requirements(self) -> List[TestRequirement]:
        """Define data requirements for this hypothesis test."""
        pass
    
    @abstractmethod
    def prepare_data(self, raw_data: Dict[str, pd.DataFrame]) -> TestData:
        """Prepare raw data for hypothesis testing.
        
        Args:
            raw_data: Dictionary of raw dataframes
            
        Returns:
            TestData object ready for analysis
        """
        pass
    
    @abstractmethod
    def run_test(self, data: TestData) -> TestResults:
        """Execute the hypothesis test.
        
        Args:
            data: Prepared test data
            
        Returns:
            Test results including statistics and outcome
        """
        pass
    
    @abstractmethod
    def interpret_results(self, results: TestResults) -> PolicyInterpretation:
        """Interpret test results for policy makers.
        
        Args:
            results: Test results
            
        Returns:
            Policy-friendly interpretation
        """
        pass
    
    def validate_data(self, data: TestData) -> Tuple[bool, List[str]]:
        """Validate that data meets test requirements.
        
        Args:
            data: Test data to validate
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        # Check required data components
        if TestRequirement.PRICE_DATA in self.required_data:
            if data.panel_data is None or len(data.panel_data.observations) == 0:
                issues.append("Missing price data")
                
        if TestRequirement.EXCHANGE_RATES in self.required_data:
            if data.exchange_rates is None or data.exchange_rates.empty:
                issues.append("Missing exchange rate data")
                
        if TestRequirement.CONFLICT_DATA in self.required_data:
            if data.conflict_events is None or data.conflict_events.empty:
                issues.append("Missing conflict event data")
                
        if TestRequirement.AID_DATA in self.required_data:
            if data.aid_distribution is None or data.aid_distribution.empty:
                issues.append("Missing aid distribution data")
                
        # Add more validation as needed
        
        is_valid = len(issues) == 0
        return is_valid, issues
    
    def run_robustness_checks(self, data: TestData, results: TestResults) -> Dict[str, Any]:
        """Run standard robustness checks.
        
        Args:
            data: Test data
            results: Initial test results
            
        Returns:
            Dictionary of robustness check results
        """
        robustness = {}
        
        # Run comprehensive robustness framework if enabled
        if self.enable_robustness and hasattr(self, 'robustness_framework'):
            logger.info(f"Running comprehensive robustness tests for {self.hypothesis_id}")
            
            # Convert data to DataFrame format for robustness framework
            df = self._convert_to_dataframe(data)
            
            # Define model function that returns results in expected format
            def model_func(data_df):
                # Re-run the main test on provided data
                test_data = self._dataframe_to_test_data(data_df)
                test_results = self.run_test(test_data)
                
                return {
                    'coefficient': test_results.effect_size or test_results.test_statistic,
                    'se': self._estimate_standard_error(test_results),
                    'p_value': test_results.p_value,
                    'n_obs': test_results.n_observations,
                    'r_squared': test_results.detailed_results.get('r_squared') if test_results.detailed_results else None
                }
            
            # Run comprehensive tests
            robust_results = self.robustness_framework.run_comprehensive_test(
                data=df,
                main_model=model_func,
                hypothesis_name=self.hypothesis_id,
                cluster_var=self._get_cluster_var(),
                treatment_var=self._get_treatment_var(),
                outcome_var=self._get_outcome_var(),
                placebo_specs=self._get_placebo_specifications()
            )
            
            # Store full results
            results.comprehensive_robustness = robust_results
            
            # Extract key robustness metrics
            robustness['robustness_score'] = robust_results.robustness_score
            robustness['overall_assessment'] = robust_results.overall_assessment
            robustness['specification_curve'] = robust_results.specification_curve
            robustness['bootstrap_ci'] = (
                robust_results.bootstrap_results.get('ci_lower'),
                robust_results.bootstrap_results.get('ci_upper')
            )
            
        else:
            # Fall back to simple robustness checks
            # Placebo test with pre-treatment data
            if 'pre_treatment_test' in self._get_applicable_robustness_checks():
                robustness['placebo_test'] = self._run_placebo_test(data)
                
            # Bootstrap confidence intervals
            if 'bootstrap' in self._get_applicable_robustness_checks():
                robustness['bootstrap_ci'] = self._run_bootstrap(data, n_iterations=1000)
                
            # Sensitivity to outliers
            if 'outlier_sensitivity' in self._get_applicable_robustness_checks():
                robustness['outlier_robust'] = self._test_outlier_sensitivity(data)
            
        return robustness
    
    def _get_applicable_robustness_checks(self) -> List[str]:
        """Get list of applicable robustness checks for this hypothesis."""
        # Default set - can be overridden by specific hypotheses
        return ['pre_treatment_test', 'bootstrap', 'outlier_sensitivity']
    
    def _run_placebo_test(self, data: TestData) -> Dict[str, Any]:
        """Run placebo test using pre-treatment period."""
        # To be implemented by specific hypothesis tests
        return {'implemented': False}
    
    def _run_bootstrap(self, data: TestData, n_iterations: int) -> Dict[str, Any]:
        """Run bootstrap for confidence intervals."""
        # To be implemented by specific hypothesis tests
        return {'implemented': False}
    
    def _test_outlier_sensitivity(self, data: TestData) -> Dict[str, Any]:
        """Test sensitivity to outlier removal."""
        # To be implemented by specific hypothesis tests
        return {'implemented': False}
    
    def _convert_to_dataframe(self, data: TestData) -> pd.DataFrame:
        """Convert TestData to DataFrame for robustness framework."""
        # Default implementation - should be overridden by specific tests
        if data.panel_data:
            return data.panel_data.to_dataframe()
        return pd.DataFrame()
    
    def _dataframe_to_test_data(self, df: pd.DataFrame) -> TestData:
        """Convert DataFrame back to TestData."""
        # Default implementation - should be overridden by specific tests
        return TestData()
    
    def _estimate_standard_error(self, results: TestResults) -> float:
        """Estimate standard error from test results."""
        # Simple estimation based on confidence interval if available
        if results.confidence_interval:
            ci_width = results.confidence_interval[1] - results.confidence_interval[0]
            # For 95% CI, SE ≈ CI_width / (2 * 1.96)
            z_score = 1.96 if results.ci_level == 0.95 else stats.norm.ppf(1 - (1 - results.ci_level) / 2)
            return ci_width / (2 * z_score)
        # Default fallback
        return 0.1
    
    def _get_cluster_var(self) -> Optional[str]:
        """Get clustering variable for robustness tests."""
        # Default to market-level clustering
        return 'market_id'
    
    def _get_treatment_var(self) -> Optional[str]:
        """Get treatment variable for robustness tests."""
        # To be overridden by specific tests
        return None
    
    def _get_outcome_var(self) -> Optional[str]:
        """Get outcome variable for robustness tests."""
        # To be overridden by specific tests
        return None
    
    def _get_placebo_specifications(self) -> List[Dict[str, Any]]:
        """Get placebo test specifications."""
        # Default placebo tests - can be overridden
        return [
            {
                'name': 'Pre-treatment placebo',
                'type': 'fake_treatment_time',
                'fake_date': '2019-01-01'  # Before actual treatment
            },
            {
                'name': 'Random assignment placebo',
                'type': 'fake_treatment_location',
                'treatment_proportion': 0.5
            }
        ]
    
    def _calculate_statistical_power(self, data: TestData, 
                                   effect_size: Optional[float] = None,
                                   alpha: float = 0.05) -> float:
        """Calculate statistical power of the test.
        
        Args:
            data: Test data
            effect_size: Effect size to detect (uses observed if None)
            alpha: Significance level
            
        Returns:
            Statistical power (0-1)
        """
        # To be implemented by specific hypothesis tests
        # This is a placeholder that should be overridden
        return 0.80
    
    def _calculate_minimum_detectable_effect(self, data: TestData,
                                           power: float = 0.80,
                                           alpha: float = 0.05) -> float:
        """Calculate minimum detectable effect size at given power.
        
        Args:
            data: Test data
            power: Desired statistical power
            alpha: Significance level
            
        Returns:
            Minimum detectable effect size
        """
        # To be implemented by specific hypothesis tests
        # This is a placeholder that should be overridden
        return 0.10
    
    def generate_summary(self, results: TestResults, interpretation: PolicyInterpretation) -> str:
        """Generate a summary report of the hypothesis test.
        
        Args:
            results: Test results
            interpretation: Policy interpretation
            
        Returns:
            Formatted summary string
        """
        summary = f"""
Hypothesis {self.hypothesis_id}: {self.description}
{'=' * 60}

OUTCOME: {results.outcome.value.upper()}

Statistical Results:
- Test Statistic: {results.test_statistic:.4f}
- P-value: {results.p_value:.4f} {f'(adjusted: {results.adjusted_p_value:.4f})' if results.adjusted_p_value else ''}
- Significance Level (α): {results.alpha:.3f}
- Effect Size: {results.effect_size:.4f if results.effect_size else 'N/A'}
- Confidence Interval ({results.ci_level:.0%}): {results.confidence_interval if results.confidence_interval else 'N/A'}
- Statistical Power: {results.statistical_power:.2f if results.statistical_power else 'N/A'}
- Sample Size: {results.n_observations if results.n_observations else 'N/A'}

Statistical Summary:
{interpretation.statistical_summary}

Key Findings:
"""
        for i, finding in enumerate(interpretation.findings, 1):
            summary += f"{i}. {finding}\n"
            
        summary += "\nConsiderations:\n"
        for i, consideration in enumerate(interpretation.considerations, 1):
            summary += f"{i}. {consideration}\n"
            
        summary += f"\n{interpretation.uncertainty_statement}\n"
        
        if interpretation.limitations:
            summary += "\nLimitations:\n"
            for limitation in interpretation.limitations:
                summary += f"- {limitation}\n"
                
        if interpretation.data_quality_notes:
            summary += "\nData Quality Notes:\n"
            for note in interpretation.data_quality_notes:
                summary += f"- {note}\n"
                
        if results.bias_detection_results and results.bias_detection_results.get('p_hacking_risk'):
            summary += "\n⚠️ WARNING: Potential analytical bias detected"
        
        # Add robustness summary if available
        if results.comprehensive_robustness:
            summary += f"\n\nRobustness Analysis:\n"
            summary += f"Overall Score: {results.comprehensive_robustness.robustness_score:.0%}\n"
            summary += f"Assessment: {results.comprehensive_robustness.overall_assessment}\n"
            
            if results.comprehensive_robustness.specification_curve:
                sc = results.comprehensive_robustness.specification_curve
                summary += f"\nSpecification Curve: {sc.get('n_specifications', 0)} specifications tested\n"
                summary += f"- {sc.get('prop_significant', 0)*100:.0f}% significant at α=0.05\n"
                summary += f"- Median effect: {sc.get('median_coefficient', 0):.4f}\n"
                
        return summary


class HypothesisRegistry:
    """Registry for all hypothesis tests."""
    
    _tests: Dict[str, HypothesisTest] = {}
    
    @classmethod
    def register(cls, test: HypothesisTest) -> None:
        """Register a hypothesis test."""
        cls._tests[test.hypothesis_id] = test
        logger.info(f"Registered hypothesis test: {test.hypothesis_id}")
    
    @classmethod
    def get(cls, hypothesis_id: str) -> Optional[HypothesisTest]:
        """Get a hypothesis test by ID."""
        return cls._tests.get(hypothesis_id)
    
    @classmethod
    def list_all(cls) -> List[str]:
        """List all registered hypothesis IDs."""
        return list(cls._tests.keys())
    
    @classmethod
    def run_all(cls, data: Dict[str, pd.DataFrame]) -> Dict[str, Tuple[TestResults, PolicyInterpretation]]:
        """Run all registered hypothesis tests with multiple testing correction.
        
        Args:
            data: Raw data for all tests
            
        Returns:
            Dictionary mapping hypothesis IDs to results
        """
        from statsmodels.stats.multitest import multipletests
        
        results = {}
        all_p_values = []
        hypothesis_order = []
        
        # First pass: run all tests and collect p-values
        for hypothesis_id, test in cls._tests.items():
            try:
                logger.info(f"Running hypothesis test: {hypothesis_id}")
                
                # Prepare data
                test_data = test.prepare_data(data)
                
                # Validate data
                is_valid, issues = test.validate_data(test_data)
                if not is_valid:
                    logger.warning(f"Data validation failed for {hypothesis_id}: {issues}")
                    continue
                
                # Run test
                test_results = test.run_test(test_data)
                
                # Run robustness checks
                test_results.robustness_checks = test.run_robustness_checks(test_data, test_results)
                
                # Update outcome based on robustness if needed
                if test_results.comprehensive_robustness:
                    robustness_score = test_results.comprehensive_robustness.robustness_score
                    if robustness_score < 0.4 and test_results.outcome == HypothesisOutcome.NULL_REJECTED:
                        # Downgrade outcome if results are fragile
                        test_results.outcome = HypothesisOutcome.INCONCLUSIVE
                        logger.warning(f"{hypothesis_id}: Downgrading outcome due to low robustness ({robustness_score:.0%})")
                
                # Interpret results
                interpretation = test.interpret_results(test_results)
                
                results[hypothesis_id] = (test_results, interpretation)
                all_p_values.append(test_results.p_value)
                hypothesis_order.append(hypothesis_id)
                
                logger.info(f"Completed {hypothesis_id}: {test_results.outcome.value}")
                
            except Exception as e:
                logger.error(f"Error running hypothesis test {hypothesis_id}: {str(e)}")
        
        # Apply FDR correction if we have multiple tests
        if len(all_p_values) > 1:
            rejected, adjusted_p_values, _, _ = multipletests(
                all_p_values, 
                method='fdr_bh',  # Benjamini-Hochberg FDR
                alpha=0.05
            )
            
            # Update results with adjusted p-values
            for i, hypothesis_id in enumerate(hypothesis_order):
                if hypothesis_id in results:
                    test_results, interpretation = results[hypothesis_id]
                    test_results.adjusted_p_value = adjusted_p_values[i]
                    test_results.fdr_rejected = rejected[i]
                    
                    logger.info(
                        f"{hypothesis_id} - Original p-value: {test_results.p_value:.4f}, "
                        f"Adjusted p-value: {test_results.adjusted_p_value:.4f}, "
                        f"FDR rejected: {test_results.fdr_rejected}"
                    )
                
        return results


class CompoundHypothesisTest(HypothesisTest):
    """Base class for tests that combine multiple sub-hypotheses."""
    
    def __init__(self, hypothesis_id: str, description: str, sub_tests: List[HypothesisTest]):
        """Initialize compound hypothesis test.
        
        Args:
            hypothesis_id: Unique identifier
            description: Brief description
            sub_tests: List of component hypothesis tests
        """
        super().__init__(hypothesis_id, description)
        self.sub_tests = sub_tests
    
    def _define_requirements(self) -> List[TestRequirement]:
        """Combine requirements from all sub-tests."""
        requirements = set()
        for test in self.sub_tests:
            requirements.update(test.required_data)
        return list(requirements)
    
    def run_test(self, data: TestData) -> TestResults:
        """Run all sub-tests and combine results."""
        sub_results = []
        
        for test in self.sub_tests:
            try:
                result = test.run_test(data)
                sub_results.append(result)
            except Exception as e:
                logger.error(f"Error in sub-test {test.hypothesis_id}: {str(e)}")
                
        # Combine results (to be implemented by specific compound tests)
        return self._combine_results(sub_results)
    
    @abstractmethod
    def _combine_results(self, sub_results: List[TestResults]) -> TestResults:
        """Combine results from sub-tests."""
        pass