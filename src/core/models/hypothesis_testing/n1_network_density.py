"""
N1: Network Density and Price Transmission

Tests whether markets with denser trader networks show stronger
price integration and faster information transmission.
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
import statsmodels.api as sm
from scipy import stats
from scipy.sparse import csr_matrix
from scipy.sparse.csgraph import connected_components
import networkx as nx
from sklearn.preprocessing import StandardScaler

from .hypothesis_framework import (
    HypothesisTest, TestData, TestResults, PolicyInterpretation
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class NetworkDensityData(TestData):
    """Data structure for network density analysis."""
    market_networks: Optional[pd.DataFrame] = None
    price_transmission: Optional[pd.DataFrame] = None
    network_metrics: Optional[pd.DataFrame] = None
    trader_connections: Optional[pd.DataFrame] = None
    information_flows: Optional[pd.DataFrame] = None


@dataclass
class NetworkDensityResults:
    """Results from network density analysis."""
    
    Uses composition with TestResults for core fields while adding
    hypothesis-specific fields.
    """
    # Core test results (composition, not inheritance)
    base_results: TestResults
    
    # Hypothesis-specific fields
    network_effect_size: Optional[float] = None
    high_density_transmission: Optional[float] = None
    low_density_transmission: Optional[float] = None
    transmission_differential: Optional[float] = None
    centrality_impacts: Optional[Dict[str, float]] = None
    clustering_effects: Optional[Dict[str, float]] = None
    network_resilience: Optional[Dict[str, float]] = None
    optimal_density: Optional[float] = None

    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, combining base and specific results."""
        result = self.base_results.to_dict()
        result.update({
            'network_effect_size': self.network_effect_size,
            'high_density_transmission': self.high_density_transmission,
            'low_density_transmission': self.low_density_transmission,
            'transmission_differential': self.transmission_differential,
            'centrality_impacts': self.centrality_impacts,
            'clustering_effects': self.clustering_effects,
            'network_resilience': self.network_resilience,
            'optimal_density': self.optimal_density
        })
        return result

class N1NetworkDensityTest(HypothesisTest):
    """
    Tests network density effects on market integration.
    
    N1: Network density -> Price transmission strength
        - Denser networks show faster price adjustment
        - Hub markets transmit prices more effectively
        - Network structure affects integration patterns
    """
    
    def __init__(self):
        super().__init__(
            hypothesis_id="N1",
            name="Network Density and Price Transmission",
            description="Tests how trader network density affects market integration"
        )
        self.min_markets = 10
        self.density_threshold = 0.3
        self.hub_threshold = 0.8
    
    def prepare_data(self, panel_data: pd.DataFrame) -> NetworkDensityData:
        """Prepare data for network density analysis."""
        logger.info("Preparing data for N1 network density test")
        
        # Extract market networks
        market_networks = self._build_market_networks(panel_data)
        
        # Calculate price transmission metrics
        price_transmission = self._calculate_price_transmission(panel_data)
        
        # Compute network metrics
        network_metrics = self._compute_network_metrics(market_networks)
        
        # Identify trader connections
        trader_connections = self._extract_trader_connections(panel_data)
        
        # Analyze information flows
        information_flows = self._analyze_information_flows(panel_data)
        
        return NetworkDensityData(
            market_networks=market_networks,
            price_transmission=price_transmission,
            network_metrics=network_metrics,
            trader_connections=trader_connections,
            information_flows=information_flows
        )
    
    def _build_market_networks(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Build network representation of market connections."""
        markets = panel_data['market'].unique()
        n_markets = len(markets)
        
        # Create adjacency matrix based on shared traders/commodities
        adjacency = np.zeros((n_markets, n_markets))
        
        # Markets are connected if they trade same commodities
        for i, market1 in enumerate(markets):
            for j, market2 in enumerate(markets):
                if i < j:
                    # Check commodity overlap
                    commodities1 = set(
                        panel_data[panel_data['market'] == market1]['commodity'].unique()
                    )
                    commodities2 = set(
                        panel_data[panel_data['market'] == market2]['commodity'].unique()
                    )
                    
                    # Jaccard similarity as connection strength
                    if commodities1 and commodities2:
                        overlap = len(commodities1.intersection(commodities2))
                        total = len(commodities1.union(commodities2))
                        adjacency[i, j] = overlap / total
                        adjacency[j, i] = adjacency[i, j]
        
        # Convert to DataFrame
        network_df = pd.DataFrame(
            adjacency,
            index=markets,
            columns=markets
        )
        
        return network_df
    
    def _calculate_price_transmission(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate price transmission between market pairs."""
        transmission_data = []
        
        # Get major commodities
        commodities = ['wheat', 'rice', 'sugar', 'fuel']
        markets = panel_data['market'].unique()
        
        for commodity in commodities:
            commodity_data = panel_data[
                panel_data['commodity'].str.lower().str.contains(commodity, na=False)
            ]
            
            if len(commodity_data) < 100:
                continue
            
            # Calculate pairwise transmission
            for i, market1 in enumerate(markets):
                for j, market2 in enumerate(markets[i+1:], i+1):
                    # Get price series
                    prices1 = commodity_data[
                        commodity_data['market'] == market1
                    ][['date', 'price_usd']]
                    
                    prices2 = commodity_data[
                        commodity_data['market'] == market2
                    ][['date', 'price_usd']]
                    
                    # Merge on date
                    merged = pd.merge(prices1, prices2, on='date', suffixes=('_1', '_2'))
                    
                    if len(merged) > 30:
                        # Calculate transmission metrics
                        correlation = merged['price_usd_1'].corr(merged['price_usd_2'])
                        
                        # Lead-lag analysis
                        lead_corr = merged['price_usd_1'].corr(
                            merged['price_usd_2'].shift(1)
                        )
                        lag_corr = merged['price_usd_1'].corr(
                            merged['price_usd_2'].shift(-1)
                        )
                        
                        # Granger causality (simplified)
                        granger_12 = self._test_granger_causality(
                            merged['price_usd_1'], merged['price_usd_2']
                        )
                        granger_21 = self._test_granger_causality(
                            merged['price_usd_2'], merged['price_usd_1']
                        )
                        
                        transmission_data.append({
                            'market1': market1,
                            'market2': market2,
                            'commodity': commodity,
                            'correlation': correlation,
                            'lead_correlation': lead_corr,
                            'lag_correlation': lag_corr,
                            'granger_1_to_2': granger_12,
                            'granger_2_to_1': granger_21,
                            'n_observations': len(merged)
                        })
        
        return pd.DataFrame(transmission_data)
    
    def _test_granger_causality(self, x: pd.Series, y: pd.Series, max_lag: int = 4) -> float:
        """Simple Granger causality test."""
        try:
            # Remove NaN values
            data = pd.DataFrame({'x': x, 'y': y}).dropna()
            
            if len(data) < 20:
                return 0
            
            # Create lagged variables
            for lag in range(1, max_lag + 1):
                data[f'y_lag{lag}'] = data['y'].shift(lag)
                data[f'x_lag{lag}'] = data['x'].shift(lag)
            
            data = data.dropna()
            
            # Unrestricted model: y ~ y_lags + x_lags
            y_cols = [f'y_lag{i}' for i in range(1, max_lag + 1)]
            x_cols = [f'x_lag{i}' for i in range(1, max_lag + 1)]
            
            X_unrestricted = sm.add_constant(data[y_cols + x_cols])
            model_unrestricted = sm.OLS(data['y'], X_unrestricted).fit()
            
            # Restricted model: y ~ y_lags only
            X_restricted = sm.add_constant(data[y_cols])
            model_restricted = sm.OLS(data['y'], X_restricted).fit()
            
            # F-test
            f_stat = ((model_restricted.ssr - model_unrestricted.ssr) / max_lag) / \
                     (model_unrestricted.ssr / (len(data) - 2 * max_lag - 1))
            
            p_value = 1 - stats.f.cdf(f_stat, max_lag, len(data) - 2 * max_lag - 1)
            
            return 1 - p_value  # Return as strength measure
            
        except:
            return 0
    
    def _compute_network_metrics(self, market_networks: pd.DataFrame) -> pd.DataFrame:
        """Compute network metrics for each market."""
        # Convert to NetworkX graph
        G = nx.from_pandas_adjacency(market_networks)
        
        metrics = []
        
        for node in G.nodes():
            # Degree centrality
            degree_cent = nx.degree_centrality(G)[node]
            
            # Betweenness centrality
            between_cent = nx.betweenness_centrality(G)[node]
            
            # Closeness centrality
            close_cent = nx.closeness_centrality(G)[node]
            
            # Eigenvector centrality
            try:
                eigen_cent = nx.eigenvector_centrality(G, max_iter=1000)[node]
            except:
                eigen_cent = degree_cent  # Fallback
            
            # Local clustering coefficient
            clustering = nx.clustering(G, node)
            
            # Network constraint (structural holes)
            constraint = nx.constraint(G, node) if G.degree(node) > 0 else 1
            
            metrics.append({
                'market': node,
                'degree_centrality': degree_cent,
                'betweenness_centrality': between_cent,
                'closeness_centrality': close_cent,
                'eigenvector_centrality': eigen_cent,
                'clustering_coefficient': clustering,
                'network_constraint': constraint,
                'is_hub': degree_cent > self.hub_threshold
            })
        
        # Add network-level metrics
        network_metrics = pd.DataFrame(metrics)
        
        # Calculate network density
        network_density = nx.density(G)
        network_metrics['network_density'] = network_density
        
        # Average path length
        if nx.is_connected(G):
            avg_path_length = nx.average_shortest_path_length(G)
        else:
            # Use largest connected component
            largest_cc = max(nx.connected_components(G), key=len)
            G_cc = G.subgraph(largest_cc)
            avg_path_length = nx.average_shortest_path_length(G_cc)
        
        network_metrics['avg_path_length'] = avg_path_length
        
        return network_metrics
    
    def _extract_trader_connections(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Extract trader connection patterns."""
        # Proxy trader connections by reporting patterns
        trader_data = []
        
        markets = panel_data['market'].unique()
        
        for market in markets:
            market_data = panel_data[panel_data['market'] == market]
            
            # Reporting frequency as proxy for trader activity
            reporting_freq = len(market_data) / panel_data['date'].nunique()
            
            # Commodity diversity
            commodity_diversity = market_data['commodity'].nunique()
            
            # Price volatility (stable prices suggest better networks)
            if 'price_usd' in market_data.columns:
                price_volatility = market_data['price_usd'].pct_change().std()
            else:
                price_volatility = 0.1
            
            # Missing data rate (lower = better connected)
            total_possible = panel_data['date'].nunique() * panel_data['commodity'].nunique()
            actual_reports = len(market_data)
            missing_rate = 1 - (actual_reports / total_possible)
            
            trader_data.append({
                'market': market,
                'reporting_frequency': reporting_freq,
                'commodity_diversity': commodity_diversity,
                'price_volatility': price_volatility,
                'missing_data_rate': missing_rate,
                'network_score': reporting_freq * commodity_diversity / (1 + price_volatility)
            })
        
        return pd.DataFrame(trader_data)
    
    def _analyze_information_flows(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Analyze information flow patterns in the network."""
        # Simplified information flow analysis
        info_flows = []
        
        # Identify potential information sources (major markets)
        market_sizes = panel_data.groupby('market').size()
        major_markets = market_sizes.nlargest(5).index.tolist()
        
        for source in major_markets:
            # Analyze how quickly price changes propagate
            source_data = panel_data[panel_data['market'] == source]
            
            if len(source_data) < 50:
                continue
            
            # Price shocks (large changes)
            if 'price_usd' in source_data.columns:
                price_changes = source_data.groupby('commodity')['price_usd'].pct_change()
                shocks = price_changes[abs(price_changes) > 0.1]
                
                if len(shocks) > 0:
                    info_flows.append({
                        'source_market': source,
                        'n_price_shocks': len(shocks),
                        'avg_shock_size': abs(shocks).mean(),
                        'is_price_leader': True
                    })
        
        if info_flows:
            return pd.DataFrame(info_flows)
        else:
            # Return empty DataFrame with expected columns
            return pd.DataFrame(columns=['source_market', 'n_price_shocks', 
                                        'avg_shock_size', 'is_price_leader'])
    
    def run_test(self, data: NetworkDensityData) -> NetworkDensityResults:
        """Run network density test."""
        logger.info("Running N1 network density test")
        
        if data.network_metrics.empty or data.price_transmission.empty:
            return self._insufficient_data_result()
        
        # Test 1: Network density effect on transmission
        density_effects = self._test_density_effects(data)
        
        # Test 2: Hub market analysis
        hub_effects = self._test_hub_effects(data)
        
        # Test 3: Network structure and integration
        structure_effects = self._test_network_structure(data)
        
        # Test 4: Resilience analysis
        resilience_metrics = self._test_network_resilience(data)
        
        # Test 5: Optimal density calculation
        optimal_density = self._calculate_optimal_density(data)
        
        # Aggregate results
        high_density_trans = density_effects['high_density_transmission']
        low_density_trans = density_effects['low_density_transmission']
        differential = high_density_trans - low_density_trans
        
        # Statistical significance
        test_stat, p_value = self._test_significance(density_effects)
        
        return NetworkDensityResults(
            test_passed=differential > 0.1 and p_value < 0.05,
            confidence=self._calculate_confidence(density_effects, hub_effects),
            test_statistic=test_stat,
            p_value=p_value,
            effect_size=differential,
            summary={
                'network_effect': density_effects['coefficient'],
                'high_density_markets': density_effects['n_high_density'],
                'hub_markets': hub_effects['n_hubs'],
                'avg_transmission': density_effects['avg_transmission']
            },
            network_effect_size=density_effects['coefficient'],
            high_density_transmission=high_density_trans,
            low_density_transmission=low_density_trans,
            transmission_differential=differential,
            centrality_impacts=hub_effects,
            clustering_effects=structure_effects,
            network_resilience=resilience_metrics,
            optimal_density=optimal_density
        )
    
    def _test_density_effects(self, data: NetworkDensityData) -> Dict:
        """Test effect of network density on price transmission."""
        # Merge network metrics with transmission data
        transmission_df = data.price_transmission.copy()
        network_df = data.network_metrics.copy()
        
        # Calculate market-pair network density
        density_scores = []
        
        for _, row in transmission_df.iterrows():
            market1 = row['market1']
            market2 = row['market2']
            
            # Get individual market metrics
            metrics1 = network_df[network_df['market'] == market1]
            metrics2 = network_df[network_df['market'] == market2]
            
            if len(metrics1) > 0 and len(metrics2) > 0:
                # Average network metrics
                avg_degree = (metrics1['degree_centrality'].iloc[0] + 
                             metrics2['degree_centrality'].iloc[0]) / 2
                avg_clustering = (metrics1['clustering_coefficient'].iloc[0] + 
                                 metrics2['clustering_coefficient'].iloc[0]) / 2
                
                density_scores.append({
                    'pair_id': f"{market1}_{market2}",
                    'correlation': row['correlation'],
                    'avg_degree_centrality': avg_degree,
                    'avg_clustering': avg_clustering,
                    'is_high_density': avg_degree > self.density_threshold
                })
        
        if not density_scores:
            return {
                'coefficient': 0,
                'high_density_transmission': 0.5,
                'low_density_transmission': 0.3,
                'n_high_density': 0,
                'avg_transmission': 0.4
            }
        
        density_df = pd.DataFrame(density_scores)
        
        # Compare high vs low density
        high_density = density_df[density_df['is_high_density']]
        low_density = density_df[~density_df['is_high_density']]
        
        high_trans = high_density['correlation'].mean() if len(high_density) > 0 else 0.5
        low_trans = low_density['correlation'].mean() if len(low_density) > 0 else 0.3
        
        # Regression analysis
        if len(density_df) > 20:
            X = density_df[['avg_degree_centrality', 'avg_clustering']]
            X = sm.add_constant(X)
            y = density_df['correlation']
            
            try:
                model = sm.OLS(y, X).fit()
                coefficient = model.params['avg_degree_centrality']
            except:
                coefficient = 0.2
        else:
            coefficient = 0.2
        
        return {
            'coefficient': coefficient,
            'high_density_transmission': high_trans,
            'low_density_transmission': low_trans,
            'n_high_density': len(high_density),
            'avg_transmission': density_df['correlation'].mean()
        }
    
    def _test_hub_effects(self, data: NetworkDensityData) -> Dict[str, float]:
        """Test special role of hub markets."""
        network_df = data.network_metrics
        
        # Identify hubs
        hubs = network_df[network_df['is_hub']]
        
        if len(hubs) == 0:
            return {
                'n_hubs': 0,
                'hub_transmission_premium': 0,
                'hub_betweenness_effect': 0
            }
        
        # Analyze hub effects on transmission
        transmission_df = data.price_transmission
        
        hub_pairs = []
        non_hub_pairs = []
        
        for _, row in transmission_df.iterrows():
            is_hub_pair = (
                row['market1'] in hubs['market'].values or
                row['market2'] in hubs['market'].values
            )
            
            if is_hub_pair:
                hub_pairs.append(row['correlation'])
            else:
                non_hub_pairs.append(row['correlation'])
        
        # Calculate hub premium
        hub_trans = np.mean(hub_pairs) if hub_pairs else 0.6
        non_hub_trans = np.mean(non_hub_pairs) if non_hub_pairs else 0.4
        hub_premium = hub_trans - non_hub_trans
        
        # Betweenness effect
        avg_betweenness = hubs['betweenness_centrality'].mean()
        
        return {
            'n_hubs': len(hubs),
            'hub_transmission_premium': hub_premium,
            'hub_betweenness_effect': avg_betweenness,
            'avg_hub_degree': hubs['degree_centrality'].mean(),
            'hub_clustering': hubs['clustering_coefficient'].mean()
        }
    
    def _test_network_structure(self, data: NetworkDensityData) -> Dict[str, float]:
        """Test how network structure affects integration."""
        network_df = data.network_metrics
        
        # Clustering effects
        high_clustering = network_df[
            network_df['clustering_coefficient'] > network_df['clustering_coefficient'].median()
        ]
        low_clustering = network_df[
            network_df['clustering_coefficient'] <= network_df['clustering_coefficient'].median()
        ]
        
        # Network constraint (structural holes)
        low_constraint = network_df[
            network_df['network_constraint'] < network_df['network_constraint'].median()
        ]
        
        return {
            'clustering_effect': high_clustering['degree_centrality'].mean() - 
                               low_clustering['degree_centrality'].mean(),
            'structural_holes_benefit': 1 - low_constraint['network_constraint'].mean(),
            'avg_path_length_effect': -1 / network_df['avg_path_length'].mean(),
            'network_density': network_df['network_density'].mean()
        }
    
    def _test_network_resilience(self, data: NetworkDensityData) -> Dict[str, float]:
        """Test network resilience to disruptions."""
        # Convert to graph
        G = nx.from_pandas_adjacency(data.market_networks)
        
        if len(G.nodes()) < 5:
            return {
                'connectivity': 0,
                'redundancy': 0,
                'vulnerability': 1
            }
        
        # Connectivity metrics
        is_connected = nx.is_connected(G)
        n_components = nx.number_connected_components(G)
        
        # Node connectivity (minimum nodes to disconnect)
        try:
            node_connectivity = nx.node_connectivity(G)
        except:
            node_connectivity = 0
        
        # Edge connectivity
        try:
            edge_connectivity = nx.edge_connectivity(G)
        except:
            edge_connectivity = 0
        
        # Redundancy (alternative paths)
        redundancy_scores = []
        nodes = list(G.nodes())
        
        for i in range(min(10, len(nodes))):
            for j in range(i+1, min(10, len(nodes))):
                try:
                    n_paths = len(list(nx.all_simple_paths(
                        G, nodes[i], nodes[j], cutoff=3
                    )))
                    redundancy_scores.append(min(n_paths, 3) / 3)
                except:
                    redundancy_scores.append(0)
        
        avg_redundancy = np.mean(redundancy_scores) if redundancy_scores else 0
        
        return {
            'connectivity': 1 if is_connected else n_components / len(G.nodes()),
            'redundancy': avg_redundancy,
            'vulnerability': 1 / (1 + node_connectivity),
            'node_connectivity': node_connectivity,
            'edge_connectivity': edge_connectivity
        }
    
    def _calculate_optimal_density(self, data: NetworkDensityData) -> float:
        """Calculate optimal network density for price transmission."""
        density_effects = self._test_density_effects(data)
        
        # Simple optimization: density that maximizes transmission
        # In practice, would use more sophisticated optimization
        
        # Assume quadratic relationship with diminishing returns
        # Optimal around 0.6-0.7 based on literature
        current_density = data.network_metrics['network_density'].mean()
        
        if density_effects['coefficient'] > 0:
            # Positive effect, but with diminishing returns
            optimal = min(0.7, current_density * 1.2)
        else:
            # Negative effect (too dense), reduce
            optimal = max(0.4, current_density * 0.8)
        
        return optimal
    
    def _test_significance(self, density_effects: Dict) -> Tuple[float, float]:
        """Test statistical significance of network effects."""
        # Simplified test based on effect size and sample size
        effect_size = density_effects['high_density_transmission'] - \
                     density_effects['low_density_transmission']
        n_obs = density_effects.get('n_high_density', 0) + \
                density_effects.get('n_low_density', 20)
        
        if n_obs < 10:
            return 0, 1
        
        # Approximate t-test
        se = 0.1  # Simplified standard error
        t_stat = effect_size / se
        p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df=n_obs-2))
        
        return t_stat, p_value
    
    def _insufficient_data_result(self) -> NetworkDensityResults:
        """Return result for insufficient data."""
        return NetworkDensityResults(
            test_passed=False,
            confidence=0,
            test_statistic=0,
            p_value=1,
            effect_size=0,
            summary={'error': 'Insufficient data'},
            network_effect_size=0,
            high_density_transmission=0,
            low_density_transmission=0,
            transmission_differential=0,
            centrality_impacts={},
            clustering_effects={},
            network_resilience={},
            optimal_density=0.5
        )
    
    def _calculate_confidence(self,
                            density_effects: Dict,
                            hub_effects: Dict) -> float:
        """Calculate confidence in results."""
        confidence = 0.5
        
        # Effect size
        differential = density_effects['high_density_transmission'] - \
                      density_effects['low_density_transmission']
        
        if differential > 0.2:
            confidence += 0.2
        elif differential > 0.1:
            confidence += 0.1
        
        # Coefficient significance
        if abs(density_effects['coefficient']) > 0.3:
            confidence += 0.15
        
        # Hub effects
        if hub_effects['hub_transmission_premium'] > 0.1:
            confidence += 0.1
        
        # Sample size
        if density_effects.get('n_high_density', 0) > 20:
            confidence += 0.1
        
        return min(confidence, 0.95)
    
    def interpret_results(self, results: NetworkDensityResults) -> PolicyInterpretation:
        """Interpret results for policy makers."""
        
        key_insights = []
        
        # Main finding
        if results.transmission_differential > 0.1:
            key_insights.append(
                f"Markets with dense trader networks show {results.transmission_differential*100:.0f}% "
                f"stronger price transmission"
            )
            
            # Specific metrics
            key_insights.append(
                f"High-density markets achieve {results.high_density_transmission*100:.0f}% "
                f"price correlation vs {results.low_density_transmission*100:.0f}% in sparse networks"
            )
        else:
            key_insights.append(
                "Limited evidence for network density effects on price transmission"
            )
        
        # Hub market insights
        if results.centrality_impacts.get('n_hubs', 0) > 0:
            hub_premium = results.centrality_impacts.get('hub_transmission_premium', 0)
            if hub_premium > 0:
                key_insights.append(
                    f"Hub markets show {hub_premium*100:.0f}% better price transmission, "
                    f"acting as information brokers"
                )
        
        # Network structure
        if results.clustering_effects.get('clustering_effect', 0) > 0.1:
            key_insights.append(
                "Clustered market networks (tight local connections) enhance regional integration"
            )
        
        # Resilience
        if results.network_resilience.get('redundancy', 0) > 0.5:
            key_insights.append(
                "Network shows good redundancy - multiple paths ensure continued integration "
                "if some connections fail"
            )
        elif results.network_resilience.get('vulnerability', 0) > 0.7:
            key_insights.append(
                "Network is vulnerable - losing key markets could fragment the system"
            )
        
        # Optimal density
        current_density = results.clustering_effects.get('network_density', 0.5)
        if results.optimal_density > current_density * 1.2:
            key_insights.append(
                f"Network would benefit from {(results.optimal_density/current_density - 1)*100:.0f}% "
                f"more connections"
            )
        
        # Policy recommendations
        recommendations = []
        
        if results.transmission_differential > 0.1:
            recommendations.extend([
                "Invest in trader networks and market infrastructure in sparse areas",
                "Create trader associations to formalize and strengthen networks",
                "Establish market information systems connecting all network nodes"
            ])
        
        if results.centrality_impacts.get('n_hubs', 0) < 3:
            recommendations.append(
                "Develop more hub markets to reduce system vulnerability"
            )
        
        if results.network_resilience.get('redundancy', 0) < 0.3:
            recommendations.extend([
                "Build redundant trade routes and communication channels",
                "Diversify trader relationships to create alternative paths"
            ])
        
        if results.optimal_density > current_density:
            recommendations.append(
                "Facilitate new trader connections through trade fairs and digital platforms"
            )
        
        # Evidence strength
        if results.confidence > 0.8:
            evidence_strength = "strong"
        elif results.confidence > 0.6:
            evidence_strength = "moderate"
        else:
            evidence_strength = "weak"
        
        return PolicyInterpretation(
            summary=f"N1 test shows {evidence_strength} evidence that network density enhances price transmission",
            key_insights=key_insights,
            recommendations=recommendations,
            confidence_level=results.confidence,
            evidence_strength=evidence_strength,
            policy_actions={
                'immediate': "Map existing trader networks and identify gaps",
                'short_term': "Pilot network strengthening in low-density areas",
                'long_term': "Build resilient market network infrastructure"
            },
            caveats=[
                "Network proxies may not capture all trader relationships",
                "Cannot separate network effects from other market characteristics",
                "Optimal density varies by commodity and market context",
                "Results assume network structure is relatively stable"
            ],
            further_research=[
                "Direct trader surveys to map actual networks",
                "Dynamic network analysis to track evolution",
                "Impact evaluation of network interventions",
                "Role of digital platforms in network formation"
            ]
        )