"""Pydantic schemas for hypothesis testing API."""

from datetime import date, datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class HypothesisTestRequest(BaseModel):
    """Request model for running a hypothesis test."""
    start_date: date = Field(..., description="Start date for analysis period")
    end_date: date = Field(..., description="End date for analysis period")
    markets: Optional[List[str]] = Field(None, description="Specific markets to analyze (None = all)")
    commodities: Optional[List[str]] = Field(None, description="Specific commodities to analyze (None = all)")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Test-specific configuration")
    
    @validator("end_date")
    def validate_date_range(cls, v, values):
        """Ensure end date is after start date."""
        if "start_date" in values and v <= values["start_date"]:
            raise ValueError("End date must be after start date")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "start_date": "2020-01-01",
                "end_date": "2024-12-31",
                "markets": ["Sana'a", "Aden", "Taiz"],
                "commodities": ["wheat_flour", "rice", "sugar"],
                "config": {
                    "confidence_level": 0.95,
                    "include_diagnostics": True
                }
            }
        }


class BatchHypothesisRequest(BaseModel):
    """Request model for running multiple hypothesis tests."""
    hypothesis_ids: List[str] = Field(..., description="List of hypothesis IDs to test")
    start_date: date = Field(..., description="Start date for analysis period")
    end_date: date = Field(..., description="End date for analysis period")
    markets: Optional[List[str]] = Field(None, description="Specific markets to analyze")
    commodities: Optional[List[str]] = Field(None, description="Specific commodities to analyze")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Shared configuration for all tests")
    parallel: bool = Field(True, description="Run tests in parallel (True) or sequentially (False)")
    
    @validator("hypothesis_ids")
    def validate_hypothesis_ids(cls, v):
        """Ensure at least one hypothesis ID provided."""
        if not v:
            raise ValueError("At least one hypothesis ID must be provided")
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "hypothesis_ids": ["H1", "H2", "H5", "H9"],
                "start_date": "2020-01-01",
                "end_date": "2024-12-31",
                "parallel": True,
                "config": {
                    "confidence_level": 0.95
                }
            }
        }


class HypothesisTestResponse(BaseModel):
    """Response model for hypothesis test creation."""
    id: str = Field(..., description="Unique test ID")
    hypothesis_id: str = Field(..., description="Hypothesis being tested")
    status: str = Field(..., description="Current status (pending/running/completed/failed)")
    message: str = Field(..., description="Status message")
    estimated_duration_seconds: int = Field(..., description="Estimated time to completion")
    created_at: datetime = Field(..., description="Test creation timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "hypothesis_id": "H1",
                "status": "pending",
                "message": "Hypothesis test H1 queued for processing",
                "estimated_duration_seconds": 60,
                "created_at": "2024-01-01T00:00:00Z"
            }
        }


class BatchHypothesisResponse(BaseModel):
    """Response model for batch hypothesis test creation."""
    batch_id: str = Field(..., description="Unique batch ID")
    test_ids: List[Dict[str, str]] = Field(..., description="Individual test IDs and their hypotheses")
    status: str = Field(..., description="Batch status")
    message: str = Field(..., description="Status message")
    parallel: bool = Field(..., description="Whether tests run in parallel")
    estimated_duration_seconds: int = Field(..., description="Total estimated duration")
    created_at: datetime = Field(..., description="Batch creation timestamp")


class HypothesisOutcomeEnum(str, Enum):
    """Possible hypothesis test outcomes."""
    SUPPORTED = "supported"
    REJECTED = "rejected"
    PARTIAL = "partial"
    INCONCLUSIVE = "inconclusive"


class TestStatistics(BaseModel):
    """Statistical test results."""
    test_statistic: float = Field(..., description="Primary test statistic")
    p_value: float = Field(..., description="P-value of the test")
    confidence_level: float = Field(..., description="Confidence level used")
    effect_size: Optional[float] = Field(None, description="Effect size estimate")
    confidence_interval: Optional[List[float]] = Field(None, description="Confidence interval [lower, upper]")
    sample_size: Optional[int] = Field(None, description="Number of observations")


class DiagnosticTests(BaseModel):
    """Diagnostic test results."""
    normality: Optional[Dict[str, float]] = Field(None, description="Normality test results")
    heteroskedasticity: Optional[Dict[str, float]] = Field(None, description="Heteroskedasticity test results")
    autocorrelation: Optional[Dict[str, float]] = Field(None, description="Autocorrelation test results")
    multicollinearity: Optional[Dict[str, float]] = Field(None, description="Multicollinearity diagnostics")
    specification: Optional[Dict[str, float]] = Field(None, description="Specification test results")


class PolicyInterpretationSchema(BaseModel):
    """Policy-relevant interpretation of results."""
    summary: str = Field(..., description="Executive summary of findings")
    implications: List[str] = Field(..., description="Key policy implications")
    recommendations: List[str] = Field(..., description="Actionable recommendations")
    confidence_statement: str = Field(..., description="Confidence in the findings")
    caveats: List[str] = Field(..., description="Important limitations or caveats")
    visualizations: Optional[Dict[str, Any]] = Field(None, description="Visualization specifications")


class HypothesisResultsResponse(BaseModel):
    """Comprehensive results from a hypothesis test."""
    test_id: str = Field(..., description="Test ID")
    hypothesis_id: str = Field(..., description="Hypothesis tested")
    outcome: HypothesisOutcomeEnum = Field(..., description="Test outcome")
    statistics: TestStatistics = Field(..., description="Statistical test results")
    diagnostics: Optional[DiagnosticTests] = Field(None, description="Diagnostic test results")
    policy_interpretation: PolicyInterpretationSchema = Field(..., description="Policy interpretation")
    detailed_results: Dict[str, Any] = Field(..., description="Hypothesis-specific detailed results")
    metadata: Dict[str, Any] = Field(..., description="Test metadata (dates, markets, etc.)")
    completed_at: datetime = Field(..., description="Test completion timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "test_id": "550e8400-e29b-41d4-a716-446655440000",
                "hypothesis_id": "H1",
                "outcome": "supported",
                "statistics": {
                    "test_statistic": 3.45,
                    "p_value": 0.001,
                    "confidence_level": 0.95,
                    "effect_size": 2.74,
                    "confidence_interval": [2.50, 2.98],
                    "sample_size": 10000
                },
                "policy_interpretation": {
                    "summary": "Currency fragmentation fully explains the Yemen Paradox",
                    "implications": [
                        "Aid purchasing power overvalued by 274% in Houthi areas",
                        "Real prices 20-40% higher in conflict zones"
                    ],
                    "recommendations": [
                        "Adjust aid calculations using zone-specific exchange rates",
                        "Distribute aid based on real (USD) purchasing power"
                    ],
                    "confidence_statement": "95% confidence in findings",
                    "caveats": [
                        "Results assume accurate exchange rate data",
                        "Black market premiums may not be fully captured"
                    ]
                },
                "detailed_results": {
                    "yer_paradox_confirmed": True,
                    "usd_truth_revealed": True,
                    "exchange_effect_size": 2.74
                },
                "metadata": {
                    "start_date": "2020-01-01",
                    "end_date": "2024-12-31",
                    "n_markets": 50,
                    "n_observations": 10000
                },
                "completed_at": "2024-01-01T00:05:00Z"
            }
        }


class HypothesisInfo(BaseModel):
    """Information about a hypothesis."""
    id: str = Field(..., description="Hypothesis ID")
    name: str = Field(..., description="Hypothesis name")
    description: str = Field(..., description="Brief description")
    required_data: List[str] = Field(..., description="Required data types")
    category: str = Field(..., description="Hypothesis category")


class HypothesisListResponse(BaseModel):
    """Response containing list of available hypotheses."""
    count: int = Field(..., description="Number of hypotheses")
    hypotheses: List[HypothesisInfo] = Field(..., description="List of hypothesis information")
    
    class Config:
        schema_extra = {
            "example": {
                "count": 13,
                "hypotheses": [
                    {
                        "id": "H1",
                        "name": "Exchange Rate Mechanism",
                        "description": "Exchange rate differences explain price differentials",
                        "required_data": ["price_data", "exchange_rates"],
                        "category": "core"
                    },
                    {
                        "id": "H2",
                        "name": "Aid Distribution Channel Effects",
                        "description": "Tests how humanitarian aid affects local prices by modality",
                        "required_data": ["price_data", "aid_data"],
                        "category": "core"
                    }
                ]
            }
        }