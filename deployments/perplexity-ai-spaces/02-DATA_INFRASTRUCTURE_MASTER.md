# Data Infrastructure - Master Document

## Yemen Market Integration Research Methodology Package

### Quick Navigation

- **Overview**: Comprehensive data ecosystem for analyzing market integration under currency fragmentation
- **Key Components**: Data sources, collection protocols, quality assurance, missing data handling
- **Implementation**: From HDX access to panel construction with 88.4% coverage
- **Cross-References**: Links to methodology (Section 03), implementation guides (Section 06), quality standards

### Search Keywords

**Primary Terms**: data infrastructure, HDX platform, WFP prices, exchange rates, ACLED conflict, panel construction
**Technical Terms**: missing data imputation, spatial matching, 3D panel data, MNAR handling, buffer analysis
**Data Sources**: Humanitarian Data Exchange, World Food Programme, ACLED, OCHA, Central Bank Yemen
**Quality Terms**: validation protocols, completeness metrics, spatial accuracy, temporal consistency

---

## Executive Summary

### Data Infrastructure Status

- **Key Development**: Core datasets accessible through Humanitarian Data Exchange (HDX)
- **Research Status**: Improved from "data constrained" to "data adequate"
- **Coverage Achievement**: 88.4% balanced panel from fragmented sources
- **Methodological Challenge**: Handling 38% systematically missing data in conflict zones

### Infrastructure Capabilities

- **Multi-Source Integration**: WFP prices, exchange rates, conflict events, aid distribution
- **Spatial Precision**: Market-level geographic matching with conflict buffer zones
- **Temporal Granularity**: Daily exchange rates, weekly conflict, monthly prices
- **Quality Assurance**: Automated validation with manual verification protocols

---

## Data Source Inventory

### Primary Economic Data

#### WFP Food Prices for Yemen

- **Platform**: Humanitarian Data Exchange (HDX)
- **Coverage**: 3,000+ markets, 2015-present
- **Update Frequency**: Monthly
- **Key Variables**: Commodity prices, market locations, currency specification
- **Access**: Manual download or HDX Python API
- **Quality**: 6,400+ downloads indicate high usage/validation

#### Exchange Rate Data

- **Primary Source**: WFP Global Market Monitor (contains exchange rates)
- **Secondary**: WFP HungerMap macro-economic indicators
- **Coverage**: Zone-level rates (Houthi vs Government areas)
- **Critical Rates**:
  - Houthi zones: ~535 YER/USD (controlled)
  - Government zones: ~2,100 YER/USD (market)
- **Challenge**: Central Bank websites blocked; rely on humanitarian sources

### Conflict and Security Data

#### ACLED (Armed Conflict Location & Event Data)

- **Coverage**: All Yemen, 2015-present
- **Update Frequency**: Weekly
- **Event Types**: Battles, explosions, protests, strategic developments
- **Spatial Precision**: Exact coordinates or nearest town
- **Key Use**: Conflict intensity measures, event studies

#### ACAPS Access Constraints

- **Coverage**: Humanitarian access by district
- **Variables**: Access severity, constraint types, territorial control
- **Update Frequency**: Quarterly
- **Application**: Instrument for missing data patterns

### Humanitarian Operations Data

#### OCHA 3W (Who-What-Where)

- **Coverage**: All humanitarian activities
- **Variables**: Organization, activity type, beneficiaries, location
- **Critical for**: Aid distribution analysis (H2 hypothesis)
- **Modality Tracking**: Cash vs in-kind distributions

#### IOM Displacement Tracking Matrix

- **Coverage**: Population movements
- **Variables**: Origin, destination, numbers, reasons
- **Application**: Demand destruction proxy (H3 hypothesis)

### Supporting Datasets

#### Yemen Rainfall Indicators

- **Source**: Climate Hazards Group
- **Coverage**: District-level, 1981-present
- **Use**: Agricultural production controls
- **Format**: GeoTIFF requiring spatial processing

#### Infrastructure Data

- **Roads**: OpenStreetMap Yemen extracts
- **Ports**: Status and capacity from logistics cluster
- **Markets**: Geographic coordinates from WFP
- **Boundaries**: OCHA Common Operational Datasets

---

## Data Collection Protocols

### Access Strategy

#### Immediate Access (Manual)

```python
# HDX Manual Download Process
1. Navigate to data.humdata.org
2. Search "Yemen" + dataset name
3. Download latest version
4. Document version and date
5. Store in versioned directory structure
```

#### Automated Access (API)

```python
from hdx.hdx_configuration import Configuration
from hdx.data.dataset import Dataset

# Configure HDX access
Configuration.create(hdx_site='prod', user_agent='YemenAnalysis')

# Download WFP price data
dataset = Dataset.read_from_hdx('wfp-food-prices-for-yemen')
resources = dataset.get_resources()

for resource in resources:
    if 'csv' in resource['format'].lower():
        url = resource['download_url']
        resource.download(folder='data/raw/wfp/')
```

### Version Control Protocol

```
data/
├── raw/
│   ├── wfp/
│   │   ├── 2024-01-15_wfp_prices.csv
│   │   └── 2024-02-15_wfp_prices.csv
│   ├── acled/
│   │   └── 2024-02-01_acled_yemen.csv
│   └── exchange_rates/
│       └── 2024-02-15_exchange_rates.csv
├── processed/
│   └── panel_v2.1_2024-02-15.parquet
└── metadata/
    └── data_lineage.json
```

### Critical Data Requirements

#### Exchange Rate Granularity

- **Minimum**: Monthly averages by zone
- **Preferred**: Weekly or daily rates
- **Geographic**: Houthi vs Government zones minimum
- **Validation**: Cross-check multiple sources

#### Price Data Standards

- **Currency**: Must specify YER or USD
- **Units**: Standardize to per kg or per unit
- **Quality**: Flag suspicious values
- **Coverage**: Accept 60%+ market coverage

---

## Quality Assurance Framework

### Data Validation Pipeline

#### 1. Source Validation

```python
def validate_source_data(df, source_type):
    """Validate raw data meets minimum requirements"""

    checks = {
        'completeness': check_required_columns(df, source_type),
        'date_range': validate_temporal_coverage(df),
        'geographic': validate_spatial_coverage(df),
        'values': detect_outliers_contextual(df),
        'consistency': check_internal_consistency(df)
    }

    return ValidationReport(checks)
```

#### 2. Integration Validation

```python
def validate_integrated_panel(panel):
    """Ensure integrated panel meets analysis requirements"""

    metrics = {
        'coverage': calculate_coverage_metrics(panel),
        'balance': assess_panel_balance(panel),
        'missing_patterns': analyze_missing_data_patterns(panel),
        'spatial_consistency': check_spatial_relationships(panel),
        'temporal_consistency': validate_time_series_properties(panel)
    }

    return PanelQualityReport(metrics)
```

### Missing Data Methodology

#### Understanding Missingness in Conflict

**Types of Missing Data**:

1. **MCAR** (Missing Completely at Random): Rare in conflict
2. **MAR** (Missing at Random): Some technical failures
3. **MNAR** (Missing Not at Random): Dominant pattern - markets stop reporting due to conflict

#### Advanced Handling Approaches

**1. Multiple Imputation (MICE)**

```python
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer

# Include auxiliary variables
auxiliary_vars = ['lagged_price', 'neighbor_avg', 'conflict_intensity',
                  'season', 'aid_presence', 'accessibility']

imputer = IterativeImputer(
    max_iter=10,
    random_state=42,
    min_value=0  # Prices cannot be negative
)

imputed_data = imputer.fit_transform(panel_with_auxiliary)
```

**2. Selection Models (Heckman)**

```python
# Model probability of observing price
from statsmodels.base.model import GenericLikelihoodModel

class HeckmanSelection(GenericLikelihoodModel):
    def __init__(self, endog, exog, exog_select):
        # First stage: Model selection (observability)
        # Second stage: Model outcome (price) given observed
        pass
```

**3. Pattern Mixture Models**

```python
# Separate models by missing data pattern
patterns = identify_missing_patterns(panel)

results = {}
for pattern in patterns:
    subset = panel[panel.missing_pattern == pattern]
    results[pattern] = estimate_model(subset)
```

### Spatial Data Quality

#### Market-Conflict Matching Protocol

```python
def match_markets_to_conflict(markets_gdf, conflict_gdf, buffer_km=10):
    """Match markets to conflict events within buffer"""

    # Create buffer around markets
    markets_gdf['buffer'] = markets_gdf.geometry.buffer(
        buffer_km * 0.01  # Rough km to degree conversion
    )

    # Spatial join with conflict events
    matched = gpd.sjoin(
        markets_gdf,
        conflict_gdf,
        how='left',
        predicate='intersects'
    )

    # Aggregate conflict metrics
    conflict_metrics = matched.groupby('market_id').agg({
        'fatalities': 'sum',
        'events': 'count',
        'event_type': lambda x: x.mode()[0] if len(x) > 0 else None
    })

    return conflict_metrics
```

#### Travel Time Calculations

```python
def calculate_market_access(markets, infrastructure, terrain):
    """Calculate realistic travel times between markets"""

    # Account for:
    # - Road quality and checkpoints
    # - Terrain (mountainous Yemen)
    # - Security constraints
    # - Seasonal accessibility

    access_matrix = compute_access_matrix(
        origins=markets,
        road_network=infrastructure,
        friction_surface=terrain,
        barriers=security_constraints
    )

    return access_matrix
```

---

## Panel Construction Methodology

### 3D Panel Structure

```
Dimensions:
- Markets: 28 major markets
- Commodities: 23 food items
- Time: 72 months (2018-2023)
- Theoretical obs: 46,368
- Actual coverage: 88.4%
```

### Construction Pipeline

#### 1. Base Panel Creation

```python
def create_base_panel(markets, commodities, date_range):
    """Create complete 3D panel structure"""

    # Generate all combinations
    index = pd.MultiIndex.from_product(
        [markets, commodities, date_range],
        names=['market_id', 'commodity', 'date']
    )

    # Initialize empty panel
    panel = pd.DataFrame(index=index).reset_index()

    return panel
```

#### 2. Price Integration

```python
def integrate_prices(panel, wfp_data, exchange_rates):
    """Add price data with currency conversion"""

    # Merge WFP prices
    panel = panel.merge(
        wfp_data[['market_id', 'commodity', 'date', 'price', 'currency']],
        on=['market_id', 'commodity', 'date'],
        how='left'
    )

    # Add exchange rates by zone
    panel = add_exchange_rates_by_zone(panel, exchange_rates)

    # Calculate USD prices
    panel['price_usd'] = panel.apply(
        lambda x: x['price'] / x['exchange_rate'] if x['currency'] == 'YER' else x['price'],
        axis=1
    )

    return panel
```

#### 3. Conflict Integration

```python
def add_conflict_measures(panel, acled_data, buffer_km=10):
    """Add conflict intensity measures"""

    # Multiple buffer distances
    for buffer in [5, 10, 20]:
        conflict_metrics = calculate_conflict_intensity(
            panel[['market_id', 'date']].drop_duplicates(),
            acled_data,
            buffer_km=buffer
        )

        panel = panel.merge(
            conflict_metrics,
            on=['market_id', 'date'],
            how='left',
            suffixes=('', f'_{buffer}km')
        )

    return panel
```

#### 4. Feature Engineering

```python
def engineer_features(panel):
    """Create derived variables for analysis"""

    features = panel.copy()

    # Temporal features
    features['ramadan'] = is_ramadan(features['date'])
    features['harvest_season'] = is_harvest(features['date'], features['commodity'])

    # Spatial features
    features['distance_to_port'] = calculate_port_distance(features['market_id'])
    features['elevation'] = get_market_elevation(features['market_id'])

    # Market integration metrics
    features['price_correlation'] = calculate_rolling_correlation(
        features, window=3, min_periods=2
    )

    # Lagged variables
    for lag in [1, 2, 3]:
        features[f'price_lag{lag}'] = features.groupby(
            ['market_id', 'commodity']
        )['price_usd'].shift(lag)

    return features
```

### Data Quality Metrics

#### Coverage Statistics

```python
def calculate_coverage_metrics(panel):
    """Comprehensive coverage assessment"""

    metrics = {
        'overall_coverage': panel['price'].notna().mean(),
        'temporal_coverage': panel.groupby('date')['price'].apply(lambda x: x.notna().mean()),
        'spatial_coverage': panel.groupby('market_id')['price'].apply(lambda x: x.notna().mean()),
        'commodity_coverage': panel.groupby('commodity')['price'].apply(lambda x: x.notna().mean()),
        'balanced_panel_size': find_largest_balanced_subset(panel)
    }

    return CoverageReport(metrics)
```

---

## Transformation Procedures

### Currency Zone Assignment

```python
def assign_currency_zones(markets, territorial_control):
    """Assign markets to currency zones based on control"""

    # Time-varying territorial control
    zone_assignment = []

    for date in panel['date'].unique():
        control = territorial_control[
            territorial_control['date'] == date
        ]

        market_zones = spatial_join_markets_to_control(
            markets, control
        )

        market_zones['date'] = date
        zone_assignment.append(market_zones)

    return pd.concat(zone_assignment)
```

### Price Standardization

```python
def standardize_prices(prices_df):
    """Standardize prices across different units"""

    # Unit conversion factors
    conversions = {
        ('Rice', 'kg'): 1.0,
        ('Rice', '50kg bag'): 50.0,
        ('Wheat Flour', 'kg'): 1.0,
        ('Wheat Flour', '25kg bag'): 25.0
    }

    # Apply conversions
    prices_df['quantity_kg'] = prices_df.apply(
        lambda x: conversions.get((x['commodity'], x['unit']), 1.0),
        axis=1
    )

    prices_df['price_per_kg'] = prices_df['price'] / prices_df['quantity_kg']

    return prices_df
```

---

## Cross-References and Navigation

### Related Methodology

- **Econometric Methods**: Section 03 for analysis techniques
- **Implementation Guides**: Section 06 for practical applications
- **Quality Standards**: Section 10 for validation protocols
- **Results Templates**: Section 07 for output formats

### Technical Resources

- **HDX API Documentation**: For automated downloads
- **Spatial Processing**: GeoPandas workflows
- **Panel Methods**: Python panel data libraries
- **Missing Data**: Advanced imputation techniques

### Policy Applications

- **Data Requirements**: For different use cases
- **Real-time Systems**: Streaming data integration
- **Monitoring Dashboards**: Data pipeline design
- **Alert Systems**: Threshold-based triggers

This data infrastructure provides the foundation for rigorous econometric analysis of market integration under currency fragmentation, with robust protocols for handling the complexities of conflict-affected data collection and processing.
