version: '3.8'

services:
  # API Service
  api:
    build:
      context: .
      dockerfile: Dockerfile.production
    image: yemen-market-integration:api-latest
    container_name: yemen-api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://yemen_user:${DB_PASSWORD}@postgres:5432/yemen_market
      - REDIS_URL=redis://redis:6379/0
      - CACHE_TYPE=redis
      - CACHE_TTL=3600
      - EVENT_BUS_TYPE=redis
      - LOG_LEVEL=info
      - WORKERS=4
      - MAX_REQUESTS=1000
      - SENTRY_DSN=${SENTRY_DSN}
      - API_KEY_SECRET=${API_KEY_SECRET}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - yemen-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Worker Service
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker.production
    image: yemen-market-integration:worker-latest
    container_name: yemen-worker
    environment:
      - DATABASE_URL=postgresql://yemen_user:${DB_PASSWORD}@postgres:5432/yemen_market
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - LOG_LEVEL=info
      - HYPOTHESIS_RESULTS_PATH=/app/data/hypothesis_results
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - yemen-network
    restart: unless-stopped
    volumes:
      - hypothesis-results:/app/data/hypothesis_results
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 1G

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: yemen-postgres
    environment:
      - POSTGRES_USER=yemen_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=yemen_market
      - POSTGRES_INITDB_ARGS=--encoding=UTF8 --locale=en_US.utf8
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - yemen-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U yemen_user -d yemen_market"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G

  # Redis Cache & Message Broker
  redis:
    image: redis:7-alpine
    container_name: yemen-redis
    command: redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru
    networks:
      - yemen-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - redis-data:/data
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: yemen-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deployment/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./deployment/ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
    depends_on:
      - api
    networks:
      - yemen-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: yemen-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
    volumes:
      - ./deployment/monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./deployment/monitoring/prometheus/alerts.yml:/etc/prometheus/alerts.yml:ro
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - yemen-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: yemen-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - ./deployment/monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./deployment/monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
      - grafana-data:/var/lib/grafana
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - yemen-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Flower - Celery Monitoring
  flower:
    build:
      context: .
      dockerfile: Dockerfile.worker.production
    image: yemen-market-integration:worker-latest
    container_name: yemen-flower
    command: celery -A src.infrastructure.tasks.celery_app flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - FLOWER_BASIC_AUTH=${FLOWER_USER}:${FLOWER_PASSWORD}
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - yemen-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  yemen-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  hypothesis-results:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  nginx-cache:
    driver: local