# Hypothesis Testing Quick Start Guide

Welcome to the Yemen Market Integration hypothesis testing framework! This guide will help you get started with testing hypotheses about market integration and currency effects in conflict settings.

## 🚀 What You'll Learn

- Run your first hypothesis test (H1 - Exchange Rate Mechanism)
- Understand the importance of currency conversion in price analysis
- Interpret results for humanitarian programming
- Scale up to comprehensive analysis

## 📋 Prerequisites

- API key (get one at <<EMAIL>>)
- Python 3.8+ or any HTTP client
- Basic understanding of REST APIs

## 🎯 Currency Conversion Context

**The Challenge**: Different regions in Yemen use different exchange rates

**The Key Insight**: Price comparisons require proper currency conversion:

- Northern areas: ~535 YER/USD exchange rate
- Southern areas: ~2,000+ YER/USD exchange rate
- **Critical**: Must convert to USD before comparing prices across regions

## 🔬 Step 1: Test Your First Hypothesis

Let's test H1 (Exchange Rate Mechanism) to see this discovery in action.

### Using cURL

```bash
# 1. Set your API key
export API_KEY="your_api_key_here"

# 2. Run H1 test
curl -X POST https://api.yemen-market-integration.org/v2/hypothesis/H1/test \
  -H "X-API-Key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2023-01-01",
    "end_date": "2023-12-31"
  }'

# Response:
# {
#   "id": "550e8400-e29b-41d4-a716-446655440000",
#   "hypothesis_id": "H1",
#   "status": "pending",
#   "message": "Hypothesis test H1 queued for processing",
#   "estimated_duration_seconds": 60
# }
```

### Using Python

```python
import requests
import json
import time

# Configuration
API_KEY = "your_api_key_here"
BASE_URL = "https://api.yemen-market-integration.org/v2"

# Headers
headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

# Step 1: Start the test
test_data = {
    "start_date": "2023-01-01",
    "end_date": "2023-12-31"
}

response = requests.post(
    f"{BASE_URL}/hypothesis/H1/test",
    headers=headers,
    json=test_data
)

test = response.json()
print(f"Test started! ID: {test['id']}")
print(f"Status: {test['status']}")

# Step 2: Monitor progress (optional)
test_id = test['id']
while True:
    status_response = requests.get(
        f"{BASE_URL}/hypothesis/test/{test_id}/status",
        headers=headers
    )
    status = status_response.json()

    print(f"Progress: {status['progress']}%")

    if status['status'] in ['completed', 'failed']:
        break

    time.sleep(5)  # Check every 5 seconds

# Step 3: Get results
if status['status'] == 'completed':
    results_response = requests.get(
        f"{BASE_URL}/hypothesis/test/{test_id}/results",
        headers=headers
    )
    results = results_response.json()

    print("\n🎉 Test Complete!")
    print(f"Outcome: {results['outcome']}")
    print(f"Key Finding: {results['policy_interpretation']['summary']}")
```

## 📊 Step 2: Understanding Your Results

### Result Structure

```json
{
  "test_id": "550e8400-e29b-41d4-a716-446655440000",
  "hypothesis_id": "H1",
  "outcome": "supported",  // The hypothesis is confirmed!
  "statistics": {
    "test_statistic": 3.45,
    "p_value": 0.001,      // Highly significant
    "effect_size": 2.74,   // 274% overvaluation!
    "confidence_interval": [2.50, 2.98]
  },
  "policy_interpretation": {
    "summary": "Exchange rate differences significantly affect price comparisons",
    "implications": [
      "Different exchange rates across zones affect purchasing power assessments",
      "Price analysis requires proper currency conversion"
    ],
    "recommendations": [
      "Use zone-specific exchange rates in all price analyses",
      "Adjust humanitarian programming for currency differences"
    ]
  }
}
```

### What This Means

1. **Outcome: SUPPORTED** ✅
   - The hypothesis is confirmed with high confidence
   - Exchange rates significantly affect price comparisons

2. **Effect Size: 2.74** 📈
   - Large difference in exchange rates between zones
   - Critical to use appropriate rates for each region

3. **Policy Impact** 🎯
   - Must use zone-specific exchange rates in analysis
   - Currency-aware programming essential for accuracy

## 🔍 Step 3: Explore More Hypotheses

### Quick Reference Table

| Test | Focus | Key Question | Run Time |
|------|-------|--------------|----------|
| **H1** | Exchange Rates | Do currency zones explain price differences? | ~60s |
| **H2** | Aid Impact | How does aid affect local prices? | ~120s |
| **H3** | Demand | Is demand destruction larger than supply? | ~90s |
| **H4** | Zone Switching | What happens when control changes? | ~90s |
| **H5** | Arbitrage | Are markets still connected? | ~60s |

### Run Multiple Tests

```python
# Test how aid affects markets (H2)
aid_test = requests.post(
    f"{BASE_URL}/hypothesis/H2/test",
    headers=headers,
    json={
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
        "config": {
            "aid_modality": ["cash", "in_kind"],
            "spillover_radius_km": 50
        }
    }
).json()

# Test demand destruction (H3)
demand_test = requests.post(
    f"{BASE_URL}/hypothesis/H3/test",
    headers=headers,
    json={
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
        "markets": ["Sana'a", "Aden", "Taiz"]
    }
).json()

print(f"Aid test ID: {aid_test['id']}")
print(f"Demand test ID: {demand_test['id']}")
```

## ⚡ Step 4: Real-time Monitoring with SSE

For live progress updates, use Server-Sent Events:

```python
import sseclient  # pip install sseclient-py

# Start a test
test = requests.post(
    f"{BASE_URL}/hypothesis/H1/test",
    headers=headers,
    json={"start_date": "2023-01-01", "end_date": "2023-12-31"}
).json()

# Monitor with SSE
response = requests.get(
    f"{BASE_URL}/hypothesis/test/{test['id']}/stream",
    headers={**headers, "Accept": "text/event-stream"},
    stream=True
)

client = sseclient.SSEClient(response)
for event in client.events():
    data = json.loads(event.data)
    print(f"[{data.get('progress', 0)}%] {data.get('message', '')}")

    if data['event'] == 'complete':
        print(f"✅ Complete! Outcome: {data.get('outcome')}")
        break
```

## 🚄 Step 5: Batch Analysis

Run all core hypotheses at once:

```python
# Run H1-H5 in parallel
batch_request = {
    "hypothesis_ids": ["H1", "H2", "H3", "H4", "H5"],
    "start_date": "2023-01-01",
    "end_date": "2023-12-31",
    "parallel": True,  # Run simultaneously
    "config": {
        "confidence_level": 0.95,
        "include_diagnostics": True
    }
}

batch = requests.post(
    f"{BASE_URL}/hypothesis/batch",
    headers=headers,
    json=batch_request
).json()

print(f"Batch ID: {batch['batch_id']}")
print(f"Running {len(batch['test_ids'])} tests in parallel")
print(f"Estimated time: {batch['estimated_duration_seconds']}s")

# Monitor batch progress
for test_info in batch['test_ids']:
    print(f"- {test_info['hypothesis_id']}: {test_info['test_id']}")
```

## 📈 Step 6: Practical Applications

### For Humanitarian Organizations

```python
def calculate_aid_adjustment(h1_results):
    """Calculate how much to adjust aid budgets."""

    if h1_results['outcome'] == 'supported':
        overvaluation = h1_results['statistics']['effect_size']

        print("💡 Aid Adjustment Calculator")
        print(f"Exchange rate effect: {overvaluation:.1%}")

        # Example calculation
        original_budget = 1_000_000  # $1M USD
        houthi_area_share = 0.4      # 40% goes to Houthi areas

        # Calculate adjustment
        houthi_budget = original_budget * houthi_area_share
        adjustment_needed = houthi_budget * overvaluation

        print(f"\nOriginal budget: ${original_budget:,.0f}")
        print(f"Houthi area allocation: ${houthi_budget:,.0f}")
        print(f"Additional funding needed: ${adjustment_needed:,.0f}")
        print(f"Total adjusted budget: ${original_budget + adjustment_needed:,.0f}")

        return {
            'original': original_budget,
            'adjustment': adjustment_needed,
            'total': original_budget + adjustment_needed,
            'increase_percentage': (adjustment_needed / original_budget) * 100
        }

# Use with your H1 results
adjustment = calculate_aid_adjustment(h1_results)
print(f"\n✅ Increase budget by {adjustment['increase_percentage']:.1f}% for purchasing power parity")
```

### For Researchers

```python
def run_comprehensive_analysis(year):
    """Run full research protocol for a given year."""

    tests_to_run = {
        'H1': "Exchange rate mechanism",
        'H2': "Aid distribution effects",
        'H5': "Cross-border arbitrage",
        'H9': "Threshold effects",
        'S1': "Spatial boundaries"
    }

    results = {}

    for hypothesis_id, description in tests_to_run.items():
        print(f"\n🔬 Testing {hypothesis_id}: {description}")

        test = requests.post(
            f"{BASE_URL}/hypothesis/{hypothesis_id}/test",
            headers=headers,
            json={
                "start_date": f"{year}-01-01",
                "end_date": f"{year}-12-31",
                "config": {
                    "confidence_level": 0.99,  # Higher for research
                    "include_diagnostics": True
                }
            }
        ).json()

        # Wait for completion
        test_id = test['id']
        # ... (monitoring code) ...

        # Store results
        results[hypothesis_id] = test_id

    return results

# Run for 2023
research_results = run_comprehensive_analysis(2023)
```

## 🛠️ Advanced Configuration

### Customize Your Analysis

```python
# Advanced H1 test with custom configuration
advanced_config = {
    "start_date": "2020-01-01",
    "end_date": "2024-12-31",
    "markets": [
        "Sana'a", "Aden", "Taiz", "Hodeidah", "Ibb",
        "Dhamar", "Amran", "Sa'ada", "Hajjah", "Al Mahwit"
    ],
    "commodities": [
        "wheat_flour", "rice", "sugar", "cooking_oil", "beans"
    ],
    "config": {
        "confidence_level": 0.99,
        "include_diagnostics": True,
        "currency_zone_mapping": "auto",
        "outlier_detection": "robust",
        "missing_data_method": "interpolation",
        "bootstrap_iterations": 1000
    }
}

response = requests.post(
    f"{BASE_URL}/hypothesis/H1/test",
    headers=headers,
    json=advanced_config
)
```

### Export Results

```python
# Get results in different formats
formats = ['json', 'csv', 'excel', 'latex']

for fmt in formats:
    response = requests.get(
        f"{BASE_URL}/hypothesis/test/{test_id}/results?format={fmt}",
        headers=headers
    )

    if fmt == 'json':
        # Process JSON
        data = response.json()
    elif fmt == 'csv':
        # Save CSV
        with open(f'results_{test_id}.csv', 'wb') as f:
            f.write(response.content)
    elif fmt == 'excel':
        # Save Excel
        with open(f'results_{test_id}.xlsx', 'wb') as f:
            f.write(response.content)
    elif fmt == 'latex':
        # Save LaTeX
        with open(f'results_{test_id}.tex', 'w') as f:
            f.write(response.text)
```

## 📚 Next Steps

### 1. Explore All Hypotheses

- Review the [Complete Hypothesis List](../api/hypothesis-reference.md)
- Understand methodology for each test
- Plan your analysis strategy

### 2. Deep Dive into Results

- Read [Interpreting Results Guide](interpreting-results.md)
- Understand statistical outputs
- Learn about policy implications

### 3. Scale Your Analysis

- Use [Batch Processing](../api/integration-guide.md#batch-processing)
- Implement [Error Handling](../api/error-handling-guide.md)
- Set up [Monitoring](../monitoring/setup-guide.md)

### 4. Join the Community

- [Research Forum](https://forum.yemen-market-integration.org)
- [GitHub Discussions](https://github.com/yemen-market-integration/discussions)
- [Monthly Webinars](https://yemen-market-integration.org/events)

## 🆘 Getting Help

### Common Issues

**Authentication Error**

```python
# Check your API key
response = requests.get(
    f"{BASE_URL}/hypothesis",
    headers={"X-API-Key": API_KEY}
)
if response.status_code == 401:
    print("❌ Invalid API key. Check your credentials.")
```

**Insufficient Data**

```python
# Check data availability first
markets = requests.get(
    f"{BASE_URL}/markets?data_completeness_min=0.8",
    headers=headers
).json()

print(f"Markets with good data: {len(markets['markets'])}")
```

**Rate Limiting**

```python
# Handle rate limits gracefully
import time

def make_request_with_retry(url, **kwargs):
    while True:
        response = requests.request(url=url, **kwargs)

        if response.status_code == 429:
            retry_after = int(response.headers.get('X-RateLimit-Reset', 60))
            print(f"Rate limited. Waiting {retry_after}s...")
            time.sleep(retry_after)
            continue

        return response
```

### Support Channels

- 📧 **Email**: <<EMAIL>>
- 💬 **Slack**: [Join our workspace](https://yemen-market-integration.slack.com)
- 📖 **Docs**: <https://docs.yemen-market-integration.org>
- 🐛 **Issues**: <https://github.com/yemen-market-integration/issues>

## 🎓 Learn More

### Video Tutorials

1. [Getting Started (5 min)](https://youtube.com/watch?v=demo1)
2. [Understanding H1 Results (10 min)](https://youtube.com/watch?v=demo2)
3. [Policy Applications (15 min)](https://youtube.com/watch?v=demo3)

### Case Studies

- [WFP Yemen: 40% Budget Optimization](../case-studies/wfp-yemen.md)
- [UNICEF: Currency-Aware Cash Programming](../case-studies/unicef-cash.md)
- [World Bank: Market Recovery Analysis](../case-studies/worldbank-recovery.md)

### Academic Papers

- [Currency Fragmentation in Conflict Settings](https://papers.ssrn.com/abstract=123456)
- [Humanitarian Aid in Dual-Currency Economies](https://papers.ssrn.com/abstract=789012)

---

**🎉 Congratulations!** You've just run your first hypothesis test and learned about the importance of currency conversion in conflict market analysis. Proper currency handling is essential for accurate humanitarian programming.

**Ready to do more?** Check out our [Advanced Analysis Guide](advanced-analysis.md) or join our [Research Community](https://forum.yemen-market-integration.org).

*Last updated: January 2024 | Version 2.0*
