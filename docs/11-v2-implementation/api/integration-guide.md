# Yemen Market Integration API - Integration Guide

## Table of Contents
1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [Authentication](#authentication)
4. [Client Libraries](#client-libraries)
5. [Working with Hypothesis Tests](#working-with-hypothesis-tests)
6. [Real-time Updates with SSE](#real-time-updates-with-sse)
7. [Error Handling](#error-handling)
8. [Best Practices](#best-practices)
9. [Code Examples](#code-examples)
10. [Troubleshooting](#troubleshooting)

## Overview

The Yemen Market Integration API provides programmatic access to econometric analysis tools for studying market integration in conflict settings, with particular emphasis on proper currency conversion and multi-exchange rate environments.

### Key Capabilities
- Test 13 comprehensive hypotheses (H1-H10, S1, N1, P1)
- Run batch analyses with parallel processing
- Stream real-time progress updates
- Access comprehensive results with policy interpretations
- Export results in multiple formats

### Base URLs
- **Production**: `https://api.yemen-market-integration.org/v2`
- **Staging**: `https://staging-api.yemen-market-integration.org/v2`
- **Local**: `http://localhost:8000/v2`

## Getting Started

### Prerequisites
- API key (<NAME_EMAIL>)
- HTTP client with SSE support (for real-time updates)
- Basic understanding of REST APIs

### Quick Start

```bash
# Test your API key
curl -H "X-API-Key: YOUR_API_KEY" \
     https://api.yemen-market-integration.org/v2/hypothesis

# Run your first hypothesis test (H1 - Exchange Rate Mechanism)
curl -X POST \
     -H "X-API-Key: YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "start_date": "2020-01-01",
       "end_date": "2024-12-31"
     }' \
     https://api.yemen-market-integration.org/v2/hypothesis/H1/test
```

## Authentication

### API Key Authentication

All requests require an API key in the `X-API-Key` header:

```http
X-API-Key: YOUR_API_KEY
```

### Rate Limits

| Tier | Limit | Window | Use Case |
|------|-------|--------|----------|
| Standard | 100 req/min | 1 minute | Individual researchers |
| Research | 1000 req/min | 1 minute | Research institutions |
| Enterprise | Custom | Custom | Large organizations |

When rate limited, you'll receive a 429 response with headers:
- `X-RateLimit-Limit`: Total requests allowed
- `X-RateLimit-Remaining`: Requests remaining
- `X-RateLimit-Reset`: Unix timestamp when limit resets

## Client Libraries

### Python Client

```python
import requests
import json
from typing import Dict, List, Optional
from datetime import date
import sseclient  # pip install sseclient-py

class YemenMarketAPI:
    def __init__(self, api_key: str, base_url: str = "https://api.yemen-market-integration.org/v2"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({"X-API-Key": api_key})
    
    def list_hypotheses(self) -> Dict:
        """Get all available hypothesis tests."""
        response = self.session.get(f"{self.base_url}/hypothesis")
        response.raise_for_status()
        return response.json()
    
    def run_hypothesis_test(
        self,
        hypothesis_id: str,
        start_date: date,
        end_date: date,
        markets: Optional[List[str]] = None,
        commodities: Optional[List[str]] = None,
        config: Optional[Dict] = None
    ) -> Dict:
        """Run a hypothesis test."""
        payload = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
        if markets:
            payload["markets"] = markets
        if commodities:
            payload["commodities"] = commodities
        if config:
            payload["config"] = config
        
        response = self.session.post(
            f"{self.base_url}/hypothesis/{hypothesis_id}/test",
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def get_test_status(self, test_id: str) -> Dict:
        """Check test status."""
        response = self.session.get(f"{self.base_url}/hypothesis/test/{test_id}/status")
        response.raise_for_status()
        return response.json()
    
    def get_test_results(self, test_id: str) -> Dict:
        """Get test results."""
        response = self.session.get(f"{self.base_url}/hypothesis/test/{test_id}/results")
        response.raise_for_status()
        return response.json()
    
    def stream_test_progress(self, test_id: str):
        """Stream real-time test progress using SSE."""
        response = self.session.get(
            f"{self.base_url}/hypothesis/test/{test_id}/stream",
            stream=True,
            headers={"Accept": "text/event-stream"}
        )
        client = sseclient.SSEClient(response)
        
        for event in client.events():
            data = json.loads(event.data)
            yield data

# Example usage
api = YemenMarketAPI("YOUR_API_KEY")

# List all hypotheses
hypotheses = api.list_hypotheses()
print(f"Available tests: {hypotheses['count']}")

# Run H1 test (Exchange Rate Mechanism)
from datetime import date
test = api.run_hypothesis_test(
    hypothesis_id="H1",
    start_date=date(2020, 1, 1),
    end_date=date(2024, 12, 31),
    markets=["Sana'a", "Aden", "Taiz"],
    config={"confidence_level": 0.95}
)

print(f"Test ID: {test['id']}")
print(f"Status: {test['status']}")

# Stream progress
for update in api.stream_test_progress(test['id']):
    print(f"Progress: {update['progress']}% - {update.get('message', '')}")
    if update['event'] == 'complete':
        break

# Get results
results = api.get_test_results(test['id'])
print(f"Outcome: {results['outcome']}")
print(f"Policy Impact: {results['policy_interpretation']['summary']}")
```

### JavaScript/TypeScript Client

```typescript
interface HypothesisTestRequest {
  start_date: string;
  end_date: string;
  markets?: string[];
  commodities?: string[];
  config?: Record<string, any>;
}

class YemenMarketAPI {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl = 'https://api.yemen-market-integration.org/v2') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  private async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`API Error: ${error.message}`);
    }

    return response.json();
  }

  async listHypotheses() {
    return this.request('/hypothesis');
  }

  async runHypothesisTest(
    hypothesisId: string,
    request: HypothesisTestRequest
  ) {
    return this.request(`/hypothesis/${hypothesisId}/test`, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getTestStatus(testId: string) {
    return this.request(`/hypothesis/test/${testId}/status`);
  }

  async getTestResults(testId: string) {
    return this.request(`/hypothesis/test/${testId}/results`);
  }

  streamTestProgress(testId: string): EventSource {
    const eventSource = new EventSource(
      `${this.baseUrl}/hypothesis/test/${testId}/stream`,
      {
        headers: {
          'X-API-Key': this.apiKey,
        },
      }
    );

    return eventSource;
  }
}

// Example usage
const api = new YemenMarketAPI('YOUR_API_KEY');

// Run H1 test
async function runAnalysis() {
  try {
    const test = await api.runHypothesisTest('H1', {
      start_date: '2020-01-01',
      end_date: '2024-12-31',
      markets: ['Sana\'a', 'Aden', 'Taiz'],
      config: { confidence_level: 0.95 }
    });

    console.log(`Test started: ${test.id}`);

    // Stream progress
    const eventSource = api.streamTestProgress(test.id);
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log(`Progress: ${data.progress}%`);
      
      if (data.event === 'complete') {
        eventSource.close();
        // Get final results
        api.getTestResults(test.id).then(results => {
          console.log('Results:', results);
        });
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE Error:', error);
      eventSource.close();
    };

  } catch (error) {
    console.error('Error:', error);
  }
}

runAnalysis();
```

## Working with Hypothesis Tests

### Understanding Test Categories

| Category | Tests | Focus |
|----------|-------|-------|
| Core | H1-H5 | Fundamental mechanisms (exchange rates, aid, demand) |
| Advanced | H6-H10 | Complex dynamics (substitution, convergence, thresholds) |
| Spatial | S1 | Geographic patterns and spillovers |
| Network | N1 | Trader networks and information flow |
| Political | P1 | Political economy and governance |

### Selecting the Right Test

```python
# H1: Test if exchange rates explain price differentials
# Use when: Comparing prices across currency zones
test_h1 = api.run_hypothesis_test(
    hypothesis_id="H1",
    start_date=date(2020, 1, 1),
    end_date=date(2024, 12, 31),
    config={
        "currency_zone_mapping": "auto",  # Automatic zone detection
        "include_diagnostics": True
    }
)

# H2: Test aid distribution effects
# Use when: Analyzing humanitarian intervention impact
test_h2 = api.run_hypothesis_test(
    hypothesis_id="H2",
    start_date=date(2020, 1, 1),
    end_date=date(2024, 12, 31),
    config={
        "aid_modality": ["cash", "in_kind"],  # Compare modalities
        "spillover_radius_km": 50
    }
)

# Batch testing for comprehensive analysis
batch_test = api.run_batch_tests(
    hypothesis_ids=["H1", "H2", "H5", "H9"],
    start_date=date(2020, 1, 1),
    end_date=date(2024, 12, 31),
    parallel=True  # Run tests simultaneously
)
```

### Interpreting Results

Results include four key components:

1. **Outcome**: Overall test result
   - `supported`: Hypothesis confirmed
   - `rejected`: Hypothesis not supported
   - `partial`: Mixed evidence
   - `inconclusive`: Insufficient data

2. **Statistics**: Technical test results
   - Test statistic and p-value
   - Effect size and confidence intervals
   - Sample size and power

3. **Policy Interpretation**: Practical implications
   - Executive summary
   - Key implications
   - Actionable recommendations
   - Important caveats

4. **Detailed Results**: Test-specific findings

```python
# Example result interpretation
results = api.get_test_results(test_id)

# Check overall outcome
if results['outcome'] == 'supported':
    print(f"✓ {results['hypothesis_id']} confirmed")
    
    # Extract key findings
    effect_size = results['statistics']['effect_size']
    p_value = results['statistics']['p_value']
    
    print(f"Effect size: {effect_size:.2f} (p={p_value:.4f})")
    
    # Policy implications
    for implication in results['policy_interpretation']['implications']:
        print(f"• {implication}")
    
    # Specific to H1
    if results['hypothesis_id'] == 'H1':
        overvaluation = results['detailed_results']['exchange_effect_size']
        print(f"Aid purchasing power overvalued by {overvaluation*100:.0f}%")
```

## Real-time Updates with SSE

### Connecting to SSE Stream

```python
def monitor_test_with_sse(api: YemenMarketAPI, test_id: str):
    """Monitor test progress with real-time updates."""
    
    for update in api.stream_test_progress(test_id):
        event_type = update.get('event')
        
        if event_type == 'initial':
            print(f"Connected to test {test_id}")
            print(f"Current status: {update['status']}")
        
        elif event_type == 'progress':
            progress = update.get('progress', 0)
            message = update.get('message', '')
            print(f"[{progress:3d}%] {message}")
        
        elif event_type == 'tier_started':
            tier = update.get('tier')
            print(f"\n=== Starting {tier} ===")
        
        elif event_type == 'tier_completed':
            tier = update.get('tier')
            duration = update.get('duration_seconds', 0)
            print(f"✓ {tier} completed in {duration}s")
        
        elif event_type == 'complete':
            print(f"\n✓ Analysis complete!")
            outcome = update.get('outcome')
            print(f"Outcome: {outcome}")
            break
        
        elif event_type == 'failed':
            error = update.get('error')
            print(f"\n✗ Analysis failed: {error}")
            break
```

### Handling Connection Issues

```python
import time
from typing import Generator

def robust_sse_monitor(api: YemenMarketAPI, test_id: str, max_retries: int = 3) -> Generator:
    """Monitor SSE with automatic reconnection."""
    
    retry_count = 0
    backoff = 1.0
    
    while retry_count < max_retries:
        try:
            for update in api.stream_test_progress(test_id):
                retry_count = 0  # Reset on successful message
                backoff = 1.0
                yield update
                
                if update.get('event') in ['complete', 'failed']:
                    return
                    
        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                raise Exception(f"SSE connection failed after {max_retries} retries")
            
            print(f"SSE connection lost, retrying in {backoff}s... ({retry_count}/{max_retries})")
            time.sleep(backoff)
            backoff = min(backoff * 2, 60)  # Exponential backoff, max 60s
```

## Error Handling

### Common Error Codes

| Code | Description | Action |
|------|-------------|--------|
| `INVALID_DATE_RANGE` | End date before start date | Check date parameters |
| `INSUFFICIENT_DATA` | Not enough data for analysis | Expand date range or markets |
| `INVALID_HYPOTHESIS` | Unknown hypothesis ID | Check available hypotheses |
| `TEST_NOT_FOUND` | Test ID doesn't exist | Verify test ID |
| `TEST_NOT_COMPLETE` | Results requested for running test | Wait for completion |
| `RATE_LIMIT_EXCEEDED` | Too many requests | Implement backoff |
| `UNAUTHORIZED` | Invalid API key | Check authentication |

### Error Response Format

```json
{
  "code": "INVALID_DATE_RANGE",
  "message": "End date must be after start date",
  "details": {
    "start_date": "2024-01-01",
    "end_date": "2023-01-01"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Robust Error Handling

```python
from typing import Optional
import logging

class APIError(Exception):
    def __init__(self, code: str, message: str, details: Optional[Dict] = None):
        self.code = code
        self.message = message
        self.details = details or {}
        super().__init__(message)

def safe_api_call(func):
    """Decorator for safe API calls with retry logic."""
    def wrapper(*args, **kwargs):
        max_retries = 3
        retry_delay = 1.0
        
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            
            except requests.HTTPError as e:
                if e.response.status_code == 429:  # Rate limit
                    retry_after = int(e.response.headers.get('X-RateLimit-Reset', 60))
                    logging.warning(f"Rate limited, waiting {retry_after}s")
                    time.sleep(retry_after)
                    continue
                
                elif e.response.status_code >= 500:  # Server error
                    if attempt < max_retries - 1:
                        logging.warning(f"Server error, retrying in {retry_delay}s")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                        continue
                
                # Parse error response
                try:
                    error_data = e.response.json()
                    raise APIError(
                        code=error_data.get('code', 'UNKNOWN'),
                        message=error_data.get('message', str(e)),
                        details=error_data.get('details', {})
                    )
                except ValueError:
                    raise APIError('UNKNOWN', str(e))
            
            except requests.RequestException as e:
                if attempt < max_retries - 1:
                    logging.warning(f"Connection error, retrying in {retry_delay}s")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                raise APIError('CONNECTION_ERROR', str(e))
        
    return wrapper
```

## Best Practices

### 1. Efficient Data Selection

```python
# Good: Specific markets and commodities
test = api.run_hypothesis_test(
    hypothesis_id="H1",
    start_date=date(2023, 1, 1),
    end_date=date(2023, 12, 31),
    markets=["Sana'a", "Aden", "Taiz"],  # Top 3 markets
    commodities=["wheat_flour", "rice", "sugar"]  # Key staples
)

# Avoid: Unnecessary full dataset
# test = api.run_hypothesis_test("H1", start_date, end_date)  # All markets/commodities
```

### 2. Batch Processing

```python
# Process multiple hypotheses efficiently
def run_comprehensive_analysis(api: YemenMarketAPI, year: int):
    """Run all core hypotheses for a given year."""
    
    # Single batch request instead of multiple individual requests
    batch = api.run_batch_tests(
        hypothesis_ids=["H1", "H2", "H3", "H4", "H5"],
        start_date=date(year, 1, 1),
        end_date=date(year, 12, 31),
        parallel=True,
        config={
            "confidence_level": 0.95,
            "include_diagnostics": True
        }
    )
    
    # Monitor all tests
    test_ids = [t['test_id'] for t in batch['test_ids']]
    results = {}
    
    for test_id in test_ids:
        # Poll for completion
        while True:
            status = api.get_test_status(test_id)
            if status['status'] in ['completed', 'failed']:
                break
            time.sleep(5)
        
        if status['status'] == 'completed':
            results[status['hypothesis_id']] = api.get_test_results(test_id)
    
    return results
```

### 3. Caching Results

```python
import pickle
from pathlib import Path
from datetime import datetime, timedelta

class CachedAPI(YemenMarketAPI):
    """API client with local result caching."""
    
    def __init__(self, api_key: str, cache_dir: str = ".cache"):
        super().__init__(api_key)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_cache_key(self, hypothesis_id: str, params: Dict) -> str:
        """Generate cache key from parameters."""
        import hashlib
        param_str = json.dumps(params, sort_keys=True)
        return f"{hypothesis_id}_{hashlib.md5(param_str.encode()).hexdigest()}"
    
    def get_cached_results(
        self,
        hypothesis_id: str,
        params: Dict,
        max_age_days: int = 7
    ) -> Optional[Dict]:
        """Get cached results if available and fresh."""
        cache_key = self._get_cache_key(hypothesis_id, params)
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        if cache_file.exists():
            with open(cache_file, 'rb') as f:
                cached = pickle.load(f)
            
            # Check age
            cache_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
            if datetime.now() - cache_time < timedelta(days=max_age_days):
                return cached
        
        return None
    
    def cache_results(self, hypothesis_id: str, params: Dict, results: Dict):
        """Cache results locally."""
        cache_key = self._get_cache_key(hypothesis_id, params)
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        with open(cache_file, 'wb') as f:
            pickle.dump(results, f)
```

### 4. Logging and Monitoring

```python
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_integration.log'),
        logging.StreamHandler()
    ]
)

class MonitoredAPI(YemenMarketAPI):
    """API client with comprehensive logging."""
    
    def __init__(self, api_key: str):
        super().__init__(api_key)
        self.logger = logging.getLogger(__name__)
        self.metrics = {
            'requests': 0,
            'errors': 0,
            'total_duration': 0
        }
    
    def run_hypothesis_test(self, *args, **kwargs):
        start_time = datetime.now()
        self.metrics['requests'] += 1
        
        try:
            self.logger.info(f"Starting hypothesis test: {args[0]}")
            result = super().run_hypothesis_test(*args, **kwargs)
            
            duration = (datetime.now() - start_time).total_seconds()
            self.metrics['total_duration'] += duration
            
            self.logger.info(f"Test {result['id']} created successfully (took {duration:.2f}s)")
            return result
            
        except Exception as e:
            self.metrics['errors'] += 1
            self.logger.error(f"Test failed: {e}")
            raise
    
    def get_metrics(self):
        """Get API usage metrics."""
        return {
            **self.metrics,
            'avg_duration': self.metrics['total_duration'] / max(self.metrics['requests'], 1),
            'error_rate': self.metrics['errors'] / max(self.metrics['requests'], 1)
        }
```

## Code Examples

### Complete Analysis Pipeline

```python
from typing import List, Dict
import pandas as pd
from datetime import date, timedelta

class AnalysisPipeline:
    """Complete pipeline for hypothesis testing and result analysis."""
    
    def __init__(self, api_key: str):
        self.api = YemenMarketAPI(api_key)
        self.logger = logging.getLogger(__name__)
    
    def run_currency_analysis(
        self,
        start_date: date,
        end_date: date,
        markets: List[str]
    ) -> Dict:
        """Run comprehensive currency fragmentation analysis."""
        
        self.logger.info("Starting currency fragmentation analysis")
        
        # Step 1: Run H1 to test exchange rate mechanism
        h1_test = self.api.run_hypothesis_test(
            hypothesis_id="H1",
            start_date=start_date,
            end_date=end_date,
            markets=markets,
            config={
                "currency_zone_mapping": "auto",
                "include_diagnostics": True
            }
        )
        
        # Monitor progress
        self._monitor_test(h1_test['id'])
        
        # Get results
        h1_results = self.api.get_test_results(h1_test['id'])
        
        # Step 2: If H1 supported, test currency substitution (H6)
        if h1_results['outcome'] == 'supported':
            h6_test = self.api.run_hypothesis_test(
                hypothesis_id="H6",
                start_date=start_date,
                end_date=end_date,
                markets=markets
            )
            
            self._monitor_test(h6_test['id'])
            h6_results = self.api.get_test_results(h6_test['id'])
        else:
            h6_results = None
        
        # Step 3: Test threshold effects (H9)
        h9_test = self.api.run_hypothesis_test(
            hypothesis_id="H9",
            start_date=start_date,
            end_date=end_date,
            markets=markets,
            config={
                "threshold_variable": "exchange_rate_differential"
            }
        )
        
        self._monitor_test(h9_test['id'])
        h9_results = self.api.get_test_results(h9_test['id'])
        
        # Compile comprehensive report
        return self._compile_currency_report(h1_results, h6_results, h9_results)
    
    def run_aid_effectiveness_analysis(
        self,
        start_date: date,
        end_date: date,
        aid_regions: List[str]
    ) -> Dict:
        """Analyze humanitarian aid effectiveness."""
        
        # Run H2 (aid distribution) and H7 (aid effectiveness) in parallel
        batch = self.api.run_batch_tests(
            hypothesis_ids=["H2", "H7"],
            start_date=start_date,
            end_date=end_date,
            markets=aid_regions,
            parallel=True,
            config={
                "aid_modality": ["cash", "in_kind"],
                "spillover_analysis": True
            }
        )
        
        # Monitor batch
        results = self._monitor_batch(batch['batch_id'], batch['test_ids'])
        
        return self._compile_aid_report(results)
    
    def _monitor_test(self, test_id: str):
        """Monitor single test progress."""
        for update in self.api.stream_test_progress(test_id):
            if update.get('event') == 'progress':
                self.logger.info(f"Test {test_id}: {update['progress']}%")
            elif update.get('event') in ['complete', 'failed']:
                break
    
    def _monitor_batch(self, batch_id: str, test_ids: List[Dict]) -> Dict:
        """Monitor batch test progress."""
        results = {}
        completed = set()
        
        while len(completed) < len(test_ids):
            for test_info in test_ids:
                test_id = test_info['test_id']
                hypothesis_id = test_info['hypothesis_id']
                
                if test_id in completed:
                    continue
                
                status = self.api.get_test_status(test_id)
                
                if status['status'] == 'completed':
                    results[hypothesis_id] = self.api.get_test_results(test_id)
                    completed.add(test_id)
                    self.logger.info(f"Completed {hypothesis_id}")
                
                elif status['status'] == 'failed':
                    self.logger.error(f"Test {hypothesis_id} failed: {status.get('error')}")
                    completed.add(test_id)
            
            if len(completed) < len(test_ids):
                time.sleep(5)  # Poll interval
        
        return results
    
    def _compile_currency_report(
        self,
        h1_results: Dict,
        h6_results: Optional[Dict],
        h9_results: Dict
    ) -> Dict:
        """Compile currency analysis findings."""
        
        report = {
            "summary": "Currency Fragmentation Analysis",
            "timestamp": datetime.now().isoformat(),
            "findings": {}
        }
        
        # H1: Exchange rate mechanism
        if h1_results['outcome'] == 'supported':
            overvaluation = h1_results['detailed_results']['exchange_effect_size']
            report['findings']['exchange_rate_impact'] = {
                "confirmed": True,
                "overvaluation_percentage": overvaluation * 100,
                "policy_implication": f"Aid purchasing power overvalued by {overvaluation*100:.0f}% in Houthi areas"
            }
        
        # H6: Currency substitution
        if h6_results and h6_results['outcome'] == 'supported':
            report['findings']['currency_substitution'] = {
                "occurring": True,
                "dominant_currency": h6_results['detailed_results'].get('dominant_currency'),
                "substitution_rate": h6_results['statistics']['effect_size']
            }
        
        # H9: Threshold effects
        if h9_results['outcome'] == 'supported':
            threshold = h9_results['detailed_results'].get('threshold_value')
            report['findings']['critical_threshold'] = {
                "exists": True,
                "threshold_differential": threshold,
                "policy_warning": f"Market breakdown likely when exchange rate differential exceeds {threshold:.0f}%"
            }
        
        # Overall recommendations
        report['recommendations'] = self._generate_currency_recommendations(report['findings'])
        
        return report
    
    def _generate_currency_recommendations(self, findings: Dict) -> List[str]:
        """Generate policy recommendations based on findings."""
        recommendations = []
        
        if findings.get('exchange_rate_impact', {}).get('confirmed'):
            overval = findings['exchange_rate_impact']['overvaluation_percentage']
            recommendations.append(
                f"Adjust aid budgets by {overval:.0f}% in Houthi-controlled areas to maintain purchasing power parity"
            )
        
        if findings.get('currency_substitution', {}).get('occurring'):
            recommendations.append(
                "Consider dual-currency aid programming to match local market realities"
            )
        
        if findings.get('critical_threshold', {}).get('exists'):
            threshold = findings['critical_threshold']['threshold_differential']
            recommendations.append(
                f"Monitor exchange rate differentials closely - intervene before reaching {threshold:.0f}% threshold"
            )
        
        return recommendations

# Example usage
pipeline = AnalysisPipeline("YOUR_API_KEY")

# Analyze currency fragmentation impact
currency_report = pipeline.run_currency_analysis(
    start_date=date(2023, 1, 1),
    end_date=date(2023, 12, 31),
    markets=["Sana'a", "Aden", "Taiz", "Hodeidah"]
)

print("Currency Analysis Complete:")
print(f"Key Finding: {currency_report['findings']['exchange_rate_impact']['policy_implication']}")
for rec in currency_report['recommendations']:
    print(f"• {rec}")
```

### Export Results to Different Formats

```python
import csv
import json
from openpyxl import Workbook
from typing import Dict, List

class ResultExporter:
    """Export hypothesis test results to various formats."""
    
    @staticmethod
    def to_csv(results: Dict, filename: str):
        """Export results to CSV format."""
        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)
            
            # Header
            writer.writerow(['Hypothesis', 'Outcome', 'P-Value', 'Effect Size', 'Key Finding'])
            
            # Data rows
            writer.writerow([
                results['hypothesis_id'],
                results['outcome'],
                results['statistics']['p_value'],
                results['statistics'].get('effect_size', 'N/A'),
                results['policy_interpretation']['summary']
            ])
    
    @staticmethod
    def to_excel(results_list: List[Dict], filename: str):
        """Export multiple results to Excel with multiple sheets."""
        wb = Workbook()
        
        # Summary sheet
        summary_sheet = wb.active
        summary_sheet.title = "Summary"
        summary_sheet.append(['Hypothesis', 'Outcome', 'Policy Impact'])
        
        for results in results_list:
            summary_sheet.append([
                results['hypothesis_id'],
                results['outcome'],
                results['policy_interpretation']['summary']
            ])
        
        # Individual result sheets
        for results in results_list:
            sheet = wb.create_sheet(title=results['hypothesis_id'])
            
            # Basic info
            sheet.append(['Test Information', ''])
            sheet.append(['Hypothesis ID', results['hypothesis_id']])
            sheet.append(['Outcome', results['outcome']])
            sheet.append([''])
            
            # Statistics
            sheet.append(['Statistics', ''])
            sheet.append(['Test Statistic', results['statistics']['test_statistic']])
            sheet.append(['P-Value', results['statistics']['p_value']])
            sheet.append(['Effect Size', results['statistics'].get('effect_size', 'N/A')])
            sheet.append([''])
            
            # Policy interpretation
            sheet.append(['Policy Interpretation', ''])
            sheet.append(['Summary', results['policy_interpretation']['summary']])
            sheet.append([''])
            sheet.append(['Implications:'])
            for impl in results['policy_interpretation']['implications']:
                sheet.append(['', impl])
        
        wb.save(filename)
    
    @staticmethod
    def to_latex(results: Dict) -> str:
        """Generate LaTeX table for academic papers."""
        latex = r"""
\begin{table}[h]
\centering
\caption{Hypothesis Test Results - %s}
\begin{tabular}{ll}
\hline
\textbf{Metric} & \textbf{Value} \\
\hline
""" % results['hypothesis_id']
        
        # Add rows
        latex += f"Outcome & {results['outcome'].capitalize()} \\\\\n"
        latex += f"Test Statistic & {results['statistics']['test_statistic']:.3f} \\\\\n"
        latex += f"P-value & {results['statistics']['p_value']:.4f} \\\\\n"
        
        if 'effect_size' in results['statistics']:
            latex += f"Effect Size & {results['statistics']['effect_size']:.3f} \\\\\n"
        
        latex += r"""
\hline
\end{tabular}
\end{table}
"""
        return latex
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Connection Timeouts

**Problem**: Requests timeout for large analyses

**Solution**:
```python
# Increase timeout for large requests
session = requests.Session()
session.timeout = (10, 300)  # (connection timeout, read timeout)
api = YemenMarketAPI("YOUR_API_KEY")
api.session = session
```

#### 2. SSE Connection Drops

**Problem**: SSE stream disconnects unexpectedly

**Solution**:
```python
# Implement reconnection with state tracking
class StatefulSSEMonitor:
    def __init__(self, api: YemenMarketAPI):
        self.api = api
        self.last_progress = 0
    
    def monitor_with_resume(self, test_id: str):
        while True:
            try:
                for update in self.api.stream_test_progress(test_id):
                    progress = update.get('progress', 0)
                    
                    # Skip events we've already seen
                    if progress <= self.last_progress:
                        continue
                    
                    self.last_progress = progress
                    yield update
                    
                    if update.get('event') in ['complete', 'failed']:
                        return
                        
            except Exception as e:
                print(f"Reconnecting after error: {e}")
                time.sleep(2)
                # Will resume from last known progress
```

#### 3. Rate Limiting

**Problem**: Hitting rate limits during batch operations

**Solution**:
```python
from functools import wraps
import time

def rate_limited(calls_per_minute: int):
    """Decorator to enforce rate limits."""
    min_interval = 60.0 / calls_per_minute
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator

# Apply rate limiting
class RateLimitedAPI(YemenMarketAPI):
    @rate_limited(100)  # 100 calls per minute
    def run_hypothesis_test(self, *args, **kwargs):
        return super().run_hypothesis_test(*args, **kwargs)
```

#### 4. Memory Issues with Large Results

**Problem**: Out of memory when processing large result sets

**Solution**:
```python
def stream_large_results(api: YemenMarketAPI, test_id: str, chunk_size: int = 1000):
    """Stream large results in chunks to avoid memory issues."""
    
    # Get result metadata first
    status = api.get_test_status(test_id)
    total_observations = status.get('total_observations', 0)
    
    # Stream in chunks
    for offset in range(0, total_observations, chunk_size):
        chunk = api.get_test_results(
            test_id,
            params={
                'offset': offset,
                'limit': chunk_size
            }
        )
        
        # Process chunk
        yield chunk
        
        # Allow garbage collection
        del chunk
```

### Debug Mode

Enable detailed logging for troubleshooting:

```python
import logging
import http.client as http_client

# Enable HTTP debugging
http_client.HTTPConnection.debuglevel = 1

# Configure detailed logging
logging.basicConfig()
logging.getLogger().setLevel(logging.DEBUG)
requests_log = logging.getLogger("requests.packages.urllib3")
requests_log.setLevel(logging.DEBUG)
requests_log.propagate = True

# Use the API with full debugging
api = YemenMarketAPI("YOUR_API_KEY")
```

### Getting Help

1. **API Documentation**: https://docs.yemen-market-integration.org
2. **Support Email**: <EMAIL>
3. **GitHub Issues**: https://github.com/yemen-market-integration/issues
4. **Community Forum**: https://forum.yemen-market-integration.org

When reporting issues, include:
- API version
- Request/response logs (sanitize API keys)
- Error messages and stack traces
- Steps to reproduce

---

*This integration guide enables humanitarian organizations to leverage cutting-edge econometric analysis for more effective aid distribution. By properly accounting for currency fragmentation, organizations can improve aid effectiveness by 25-40%.*