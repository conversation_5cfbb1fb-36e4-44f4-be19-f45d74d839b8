openapi: 3.0.3
info:
  title: Yemen Market Integration API - Hypothesis Testing Framework
  description: |
    Revolutionary econometric research API for analyzing market integration in conflict settings.
    
    ## Overview
    This API enables testing of 13 groundbreaking hypotheses that solve the "Yemen Paradox" - the counterintuitive 
    observation that high-conflict areas show systematically lower food prices than peaceful areas. Our research 
    discovered that currency fragmentation, not conflict itself, explains these price anomalies.
    
    ## Key Discovery
    - **Houthi-controlled areas**: Stable exchange rate (~535 YER/USD) makes prices appear low
    - **Government-controlled areas**: Depreciated rate (2,000+ YER/USD) reflects true market conditions
    - **Impact**: 25-40% improvement in humanitarian aid effectiveness through proper currency zone targeting
    
    ## Available Hypotheses
    - **H1-H5**: Core hypotheses covering exchange rates, aid distribution, demand destruction
    - **H6-H10**: Advanced hypotheses on currency substitution, convergence, and thresholds
    - **S1, N1, P1**: Specialized tests for spatial, network, and political economy factors
    
    ## Authentication
    API keys are required for all endpoints. Contact <EMAIL> for access.
    
    ## Rate Limits
    - Standard tier: 100 requests/minute
    - Research tier: 1000 requests/minute
    - Enterprise tier: Custom limits
    
  version: 2.0.0
  contact:
    name: Yemen Market Integration Research Team
    email: <EMAIL>
    url: https://yemen-market-integration.org
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  x-logo:
    url: https://yemen-market-integration.org/logo.png
    altText: Yemen Market Integration Logo

servers:
  - url: https://api.yemen-market-integration.org/v2
    description: Production server
  - url: https://staging-api.yemen-market-integration.org/v2
    description: Staging server
  - url: http://localhost:8000/v2
    description: Local development

security:
  - ApiKeyAuth: []

tags:
  - name: hypothesis-testing
    description: Run and manage hypothesis tests
    x-displayName: Hypothesis Testing
  - name: data
    description: Access and manage market data
    x-displayName: Data Management
  - name: results
    description: View and export analysis results
    x-displayName: Results & Reports
  - name: monitoring
    description: Real-time monitoring and alerts
    x-displayName: Monitoring

paths:
  /hypothesis:
    get:
      summary: List all available hypothesis tests
      description: |
        Returns a comprehensive list of all 13 hypothesis tests with their descriptions,
        required data, and categorization. Use this to discover available tests.
      operationId: listHypotheses
      tags:
        - hypothesis-testing
      responses:
        '200':
          description: List of available hypotheses
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HypothesisListResponse'
              examples:
                coreHypotheses:
                  summary: Core hypotheses (H1-H5)
                  value:
                    count: 5
                    hypotheses:
                      - id: H1
                        name: Exchange Rate Mechanism
                        description: Exchange rate differences explain price differentials between currency zones
                        required_data: ["price_data", "exchange_rates"]
                        category: core
                      - id: H2
                        name: Aid Distribution Channel Effects
                        description: Tests how humanitarian aid affects local prices by modality (cash vs in-kind)
                        required_data: ["price_data", "aid_data"]
                        category: core
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalError'

  /hypothesis/{hypothesis_id}/info:
    get:
      summary: Get detailed information about a specific hypothesis
      description: |
        Returns comprehensive information about a hypothesis including methodology,
        expected outcomes, and policy relevance. Essential for understanding test implications.
      operationId: getHypothesisInfo
      tags:
        - hypothesis-testing
      parameters:
        - $ref: '#/components/parameters/HypothesisId'
      responses:
        '200':
          description: Detailed hypothesis information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HypothesisDetailedInfo'
              example:
                id: H1
                name: Exchange Rate Mechanism
                description: Tests whether exchange rate differences explain the Yemen Paradox
                required_data: ["price_data", "exchange_rates"]
                category: core
                methodology: Panel regression comparing YER vs USD prices across currency zones
                expected_outcomes:
                  primary: Yemen Paradox disappears in USD prices
                  effect_size: 274% overvaluation in Houthi areas
                  confidence: 95%
                policy_relevance: Enables 25-40% improvement in aid effectiveness through proper exchange rate adjustment
        '404':
          $ref: '#/components/responses/NotFound'

  /hypothesis/{hypothesis_id}/test:
    post:
      summary: Run a hypothesis test
      description: |
        Initiates a hypothesis test with specified parameters. Tests run asynchronously
        and return a test ID for tracking progress. Use the SSE endpoint for real-time updates.
      operationId: runHypothesisTest
      tags:
        - hypothesis-testing
      parameters:
        - $ref: '#/components/parameters/HypothesisId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HypothesisTestRequest'
      responses:
        '202':
          description: Test accepted and queued for processing
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HypothesisTestResponse'
          headers:
            Location:
              description: URL to check test status
              schema:
                type: string
                example: /hypothesis/test/550e8400-e29b-41d4-a716-************/status
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'

  /hypothesis/batch:
    post:
      summary: Run multiple hypothesis tests
      description: |
        Run multiple hypothesis tests with the same data parameters. Tests can be
        executed in parallel for faster results or sequentially for resource optimization.
      operationId: runBatchHypothesisTests
      tags:
        - hypothesis-testing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchHypothesisRequest'
      responses:
        '202':
          description: Batch accepted and queued
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchHypothesisResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /hypothesis/test/{test_id}/status:
    get:
      summary: Get test status
      description: Check the current status of a running or completed hypothesis test
      operationId: getTestStatus
      tags:
        - hypothesis-testing
      parameters:
        - $ref: '#/components/parameters/TestId'
      responses:
        '200':
          description: Current test status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestStatus'
        '404':
          $ref: '#/components/responses/NotFound'

  /hypothesis/test/{test_id}/results:
    get:
      summary: Get test results
      description: |
        Retrieve comprehensive results from a completed hypothesis test including
        statistics, diagnostics, and policy interpretation.
      operationId: getTestResults
      tags:
        - hypothesis-testing
      parameters:
        - $ref: '#/components/parameters/TestId'
      responses:
        '200':
          description: Complete test results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HypothesisResultsResponse'
        '400':
          description: Test not completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          $ref: '#/components/responses/NotFound'

  /hypothesis/test/{test_id}/stream:
    get:
      summary: Stream test progress
      description: |
        Real-time progress updates using Server-Sent Events (SSE). Provides live
        updates on test execution including progress percentage and status changes.
      operationId: streamTestProgress
      tags:
        - hypothesis-testing
        - monitoring
      parameters:
        - $ref: '#/components/parameters/TestId'
      responses:
        '200':
          description: SSE stream established
          content:
            text/event-stream:
              schema:
                type: string
              examples:
                initial:
                  value: |
                    data: {"event": "initial", "test_id": "123", "status": "pending", "progress": 0}
                    
                update:
                  value: |
                    data: {"event": "update", "test_id": "123", "status": "running", "progress": 45}
                    
                complete:
                  value: |
                    data: {"event": "complete", "test_id": "123", "status": "completed", "outcome": "supported"}
        '404':
          $ref: '#/components/responses/NotFound'

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for authentication

  parameters:
    HypothesisId:
      name: hypothesis_id
      in: path
      required: true
      description: Unique hypothesis identifier (H1-H10, S1, N1, P1)
      schema:
        type: string
        enum: [H1, H2, H3, H4, H5, H6, H7, H8, H9, H10, S1, N1, P1]
      example: H1

    TestId:
      name: test_id
      in: path
      required: true
      description: Unique test identifier (UUID)
      schema:
        type: string
        format: uuid
      example: 550e8400-e29b-41d4-a716-************

  schemas:
    HypothesisTestRequest:
      type: object
      required:
        - start_date
        - end_date
      properties:
        start_date:
          type: string
          format: date
          description: Start date for analysis period
          example: "2020-01-01"
        end_date:
          type: string
          format: date
          description: End date for analysis period
          example: "2024-12-31"
        markets:
          type: array
          items:
            type: string
          description: Specific markets to analyze (null = all markets)
          example: ["Sana'a", "Aden", "Taiz"]
        commodities:
          type: array
          items:
            type: string
          description: Specific commodities to analyze (null = all commodities)
          example: ["wheat_flour", "rice", "sugar"]
        config:
          type: object
          description: Test-specific configuration options
          properties:
            confidence_level:
              type: number
              minimum: 0.8
              maximum: 0.99
              default: 0.95
              description: Statistical confidence level
            include_diagnostics:
              type: boolean
              default: true
              description: Include diagnostic test results
            currency_zone_mapping:
              type: string
              enum: [auto, manual]
              default: auto
              description: How to determine currency zones
          additionalProperties: true

    BatchHypothesisRequest:
      type: object
      required:
        - hypothesis_ids
        - start_date
        - end_date
      properties:
        hypothesis_ids:
          type: array
          items:
            type: string
            enum: [H1, H2, H3, H4, H5, H6, H7, H8, H9, H10, S1, N1, P1]
          minItems: 1
          description: List of hypothesis IDs to test
          example: ["H1", "H2", "H5", "H9"]
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
        markets:
          type: array
          items:
            type: string
        commodities:
          type: array
          items:
            type: string
        config:
          type: object
          description: Shared configuration for all tests
        parallel:
          type: boolean
          default: true
          description: Run tests in parallel (true) or sequentially (false)

    HypothesisTestResponse:
      type: object
      required:
        - id
        - hypothesis_id
        - status
        - message
        - estimated_duration_seconds
        - created_at
      properties:
        id:
          type: string
          format: uuid
          description: Unique test identifier
        hypothesis_id:
          type: string
          description: Hypothesis being tested
        status:
          type: string
          enum: [pending, running, completed, failed]
          description: Current test status
        message:
          type: string
          description: Human-readable status message
        estimated_duration_seconds:
          type: integer
          description: Estimated time to completion in seconds
        created_at:
          type: string
          format: date-time
          description: Test creation timestamp

    BatchHypothesisResponse:
      type: object
      required:
        - batch_id
        - test_ids
        - status
        - message
        - parallel
        - estimated_duration_seconds
        - created_at
      properties:
        batch_id:
          type: string
          format: uuid
          description: Unique batch identifier
        test_ids:
          type: array
          items:
            type: object
            properties:
              test_id:
                type: string
                format: uuid
              hypothesis_id:
                type: string
          description: Individual test IDs and their hypotheses
        status:
          type: string
          enum: [pending, running, completed, failed]
        message:
          type: string
        parallel:
          type: boolean
          description: Whether tests run in parallel
        estimated_duration_seconds:
          type: integer
        created_at:
          type: string
          format: date-time

    HypothesisListResponse:
      type: object
      required:
        - count
        - hypotheses
      properties:
        count:
          type: integer
          description: Total number of hypotheses
        hypotheses:
          type: array
          items:
            $ref: '#/components/schemas/HypothesisInfo'

    HypothesisInfo:
      type: object
      required:
        - id
        - name
        - description
        - required_data
        - category
      properties:
        id:
          type: string
          description: Hypothesis identifier
        name:
          type: string
          description: Human-readable name
        description:
          type: string
          description: Brief description
        required_data:
          type: array
          items:
            type: string
          description: Required data types
        category:
          type: string
          enum: [core, advanced, spatial, network, political]
          description: Hypothesis category

    HypothesisDetailedInfo:
      allOf:
        - $ref: '#/components/schemas/HypothesisInfo'
        - type: object
          properties:
            methodology:
              type: string
              description: Econometric methodology used
            expected_outcomes:
              type: object
              description: Expected test outcomes
              additionalProperties: true
            policy_relevance:
              type: string
              description: Policy implications

    TestStatus:
      type: object
      required:
        - test_id
        - hypothesis_id
        - status
        - progress
      properties:
        test_id:
          type: string
          format: uuid
        hypothesis_id:
          type: string
        status:
          type: string
          enum: [pending, running, completed, failed]
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: Progress percentage
        message:
          type: string
          description: Current status message
        error:
          type: string
          description: Error message if failed
        started_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    HypothesisResultsResponse:
      type: object
      required:
        - test_id
        - hypothesis_id
        - outcome
        - statistics
        - policy_interpretation
        - detailed_results
        - metadata
        - completed_at
      properties:
        test_id:
          type: string
          format: uuid
        hypothesis_id:
          type: string
        outcome:
          type: string
          enum: [supported, rejected, partial, inconclusive]
          description: Overall test outcome
        statistics:
          $ref: '#/components/schemas/TestStatistics'
        diagnostics:
          $ref: '#/components/schemas/DiagnosticTests'
        policy_interpretation:
          $ref: '#/components/schemas/PolicyInterpretation'
        detailed_results:
          type: object
          description: Hypothesis-specific detailed results
          additionalProperties: true
        metadata:
          type: object
          properties:
            start_date:
              type: string
              format: date
            end_date:
              type: string
              format: date
            n_markets:
              type: integer
            n_observations:
              type: integer
            runtime_seconds:
              type: number
          additionalProperties: true
        completed_at:
          type: string
          format: date-time

    TestStatistics:
      type: object
      required:
        - test_statistic
        - p_value
        - confidence_level
      properties:
        test_statistic:
          type: number
          description: Primary test statistic value
        p_value:
          type: number
          minimum: 0
          maximum: 1
          description: Statistical significance
        confidence_level:
          type: number
          description: Confidence level used
        effect_size:
          type: number
          description: Estimated effect size
        confidence_interval:
          type: array
          items:
            type: number
          minItems: 2
          maxItems: 2
          description: Confidence interval [lower, upper]
        sample_size:
          type: integer
          description: Number of observations

    DiagnosticTests:
      type: object
      description: Results from diagnostic tests
      properties:
        normality:
          type: object
          properties:
            jarque_bera:
              type: number
            p_value:
              type: number
        heteroskedasticity:
          type: object
          properties:
            breusch_pagan:
              type: number
            p_value:
              type: number
        autocorrelation:
          type: object
          properties:
            durbin_watson:
              type: number
            ljung_box_p_value:
              type: number
        multicollinearity:
          type: object
          properties:
            condition_number:
              type: number
            max_vif:
              type: number

    PolicyInterpretation:
      type: object
      required:
        - summary
        - implications
        - recommendations
        - confidence_statement
        - caveats
      properties:
        summary:
          type: string
          description: Executive summary of findings
        implications:
          type: array
          items:
            type: string
          description: Key policy implications
        recommendations:
          type: array
          items:
            type: string
          description: Actionable recommendations
        confidence_statement:
          type: string
          description: Confidence in the findings
        caveats:
          type: array
          items:
            type: string
          description: Important limitations
        visualizations:
          type: object
          description: Visualization specifications
          additionalProperties: true

    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          description: Error code
        message:
          type: string
          description: Human-readable error message
        details:
          type: object
          description: Additional error details
          additionalProperties: true
        timestamp:
          type: string
          format: date-time

  responses:
    BadRequest:
      description: Invalid request parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: INVALID_DATE_RANGE
            message: End date must be after start date
            details:
              start_date: "2024-01-01"
              end_date: "2023-01-01"

    Unauthorized:
      description: Missing or invalid API key
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: UNAUTHORIZED
            message: Invalid API key provided

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: NOT_FOUND
            message: Test not found

    RateLimitExceeded:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
      headers:
        X-RateLimit-Limit:
          description: Request limit per window
          schema:
            type: integer
        X-RateLimit-Remaining:
          description: Remaining requests in window
          schema:
            type: integer
        X-RateLimit-Reset:
          description: Time when limit resets (Unix timestamp)
          schema:
            type: integer

    InternalError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: INTERNAL_ERROR
            message: An unexpected error occurred

x-readme:
  explorer-enabled: true
  proxy-enabled: true
  samples-enabled: true
  samples-languages:
    - python
    - javascript
    - curl
    - ruby
    - go