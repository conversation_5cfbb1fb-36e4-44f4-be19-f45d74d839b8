# Error Handling and Recovery Strategies Guide

## Table of Contents
1. [Overview](#overview)
2. [Error Classification](#error-classification)
3. [API Error Codes](#api-error-codes)
4. [Client-Side Error Handling](#client-side-error-handling)
5. [Recovery Strategies](#recovery-strategies)
6. [Monitoring and Alerting](#monitoring-and-alerting)
7. [Best Practices](#best-practices)
8. [Troubleshooting Workflows](#troubleshooting-workflows)

## Overview

This guide provides comprehensive error handling strategies for the Yemen Market Integration API, ensuring robust and resilient client implementations that can gracefully handle failures and recover from errors.

### Design Principles
- **Fail gracefully**: Never crash the client application
- **Provide context**: Include meaningful error messages
- **Enable recovery**: Implement automatic retry mechanisms
- **Track failures**: Log errors for debugging and monitoring
- **Maintain state**: Preserve progress during long-running operations

## Error Classification

### Error Categories

| Category | HTTP Status | Recoverable | Description |
|----------|-------------|-------------|-------------|
| Client Errors | 4xx | Sometimes | Invalid requests, authentication issues |
| Server Errors | 5xx | Yes | Temporary server issues |
| Network Errors | N/A | Yes | Connection problems |
| Rate Limiting | 429 | Yes | Too many requests |
| Validation Errors | 400 | No | Invalid input data |
| Business Logic Errors | 422 | Sometimes | Domain-specific constraints |

### Error Response Structure

```json
{
  "code": "ERROR_CODE",
  "message": "Human-readable error description",
  "details": {
    "field": "specific_field",
    "value": "invalid_value",
    "constraint": "validation_rule",
    "additional_info": {}
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_abc123def456",
  "documentation_url": "https://docs.yemen-market-integration.org/errors/ERROR_CODE"
}
```

## API Error Codes

### Authentication and Authorization (401-403)

| Code | HTTP Status | Description | Resolution |
|------|-------------|-------------|------------|
| `MISSING_API_KEY` | 401 | No API key provided | Include X-API-Key header |
| `INVALID_API_KEY` | 401 | API key is invalid or expired | Check key validity |
| `INSUFFICIENT_PERMISSIONS` | 403 | API key lacks required permissions | Request appropriate access level |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests | Implement backoff strategy |

### Validation Errors (400)

| Code | HTTP Status | Description | Resolution |
|------|-------------|-------------|------------|
| `INVALID_DATE_RANGE` | 400 | End date before start date | Correct date parameters |
| `INVALID_HYPOTHESIS_ID` | 400 | Unknown hypothesis identifier | Use valid hypothesis ID |
| `MISSING_REQUIRED_FIELD` | 400 | Required field not provided | Include all required fields |
| `INVALID_MARKET_NAME` | 400 | Market name not recognized | Use valid market identifier |
| `INVALID_COMMODITY_CODE` | 400 | Commodity code not found | Check commodity list |

### Data Availability Errors (404, 422)

| Code | HTTP Status | Description | Resolution |
|------|-------------|-------------|------------|
| `TEST_NOT_FOUND` | 404 | Test ID doesn't exist | Verify test ID |
| `INSUFFICIENT_DATA` | 422 | Not enough data for analysis | Expand date range or scope |
| `NO_PRICE_DATA` | 422 | No price data available | Check data availability |
| `CURRENCY_ZONE_UNAVAILABLE` | 422 | Cannot determine currency zones | Provide manual mapping |

### Processing Errors (409, 500-503)

| Code | HTTP Status | Description | Resolution |
|------|-------------|-------------|------------|
| `TEST_ALREADY_RUNNING` | 409 | Duplicate test request | Wait for completion |
| `ANALYSIS_FAILED` | 500 | Internal processing error | Retry with exponential backoff |
| `SERVICE_UNAVAILABLE` | 503 | Server temporarily unavailable | Retry after delay |
| `TIMEOUT_ERROR` | 504 | Request processing timeout | Use async endpoints |

## Client-Side Error Handling

### Basic Error Handler

```python
from typing import Dict, Optional, Callable
import requests
import time
import logging

class APIErrorHandler:
    """Comprehensive error handling for API clients."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.error_handlers = self._setup_error_handlers()
    
    def _setup_error_handlers(self) -> Dict[str, Callable]:
        """Map error codes to specific handlers."""
        return {
            'RATE_LIMIT_EXCEEDED': self._handle_rate_limit,
            'INVALID_API_KEY': self._handle_auth_error,
            'INSUFFICIENT_DATA': self._handle_data_error,
            'SERVICE_UNAVAILABLE': self._handle_service_error,
            'TIMEOUT_ERROR': self._handle_timeout,
        }
    
    def handle_error(self, error_response: Dict) -> Dict:
        """Main error handling dispatcher."""
        error_code = error_response.get('code', 'UNKNOWN')
        handler = self.error_handlers.get(error_code, self._handle_generic_error)
        
        self.logger.error(f"API Error: {error_code} - {error_response.get('message')}")
        
        return handler(error_response)
    
    def _handle_rate_limit(self, error: Dict) -> Dict:
        """Handle rate limiting with smart backoff."""
        retry_after = error.get('details', {}).get('retry_after', 60)
        
        return {
            'action': 'retry',
            'wait_seconds': retry_after,
            'message': f"Rate limited. Waiting {retry_after}s before retry."
        }
    
    def _handle_auth_error(self, error: Dict) -> Dict:
        """Handle authentication errors."""
        return {
            'action': 'fail',
            'message': "Authentication failed. Please check your API key.",
            'resolution': "Verify API key at https://yemen-market-integration.org/account"
        }
    
    def _handle_data_error(self, error: Dict) -> Dict:
        """Handle data availability errors."""
        suggestions = []
        
        if 'date_range' in str(error.get('details', {})):
            suggestions.append("Try expanding the date range")
        
        if 'markets' in str(error.get('details', {})):
            suggestions.append("Include more markets in the analysis")
        
        return {
            'action': 'modify_request',
            'suggestions': suggestions,
            'message': "Insufficient data for analysis"
        }
    
    def _handle_service_error(self, error: Dict) -> Dict:
        """Handle temporary service errors."""
        return {
            'action': 'retry',
            'wait_seconds': 30,
            'max_retries': 5,
            'message': "Service temporarily unavailable"
        }
    
    def _handle_timeout(self, error: Dict) -> Dict:
        """Handle timeout errors."""
        return {
            'action': 'use_async',
            'message': "Request timed out. Consider using async endpoints for large analyses."
        }
    
    def _handle_generic_error(self, error: Dict) -> Dict:
        """Fallback error handler."""
        return {
            'action': 'log_and_retry',
            'wait_seconds': 10,
            'message': f"Unexpected error: {error.get('message', 'Unknown error')}"
        }
```

### Resilient API Client

```python
from typing import Optional, Dict, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class ResilientAPIClient:
    """API client with built-in error handling and recovery."""
    
    def __init__(self, api_key: str, base_url: str):
        self.api_key = api_key
        self.base_url = base_url
        self.error_handler = APIErrorHandler()
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """Create session with retry strategy."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE"],
            raise_on_status=False
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set default headers
        session.headers.update({
            'X-API-Key': self.api_key,
            'Content-Type': 'application/json',
            'User-Agent': 'YemenMarketAPI-Python/2.0'
        })
        
        return session
    
    def request(
        self,
        method: str,
        endpoint: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Make resilient API request with error handling."""
        
        url = f"{self.base_url}{endpoint}"
        max_retries = kwargs.pop('max_retries', 3)
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                response = self.session.request(method, url, **kwargs)
                
                # Success
                if response.ok:
                    return response.json()
                
                # Handle HTTP errors
                if response.status_code == 429:
                    # Rate limiting
                    retry_after = int(response.headers.get('X-RateLimit-Reset', 60))
                    self._wait_with_jitter(retry_after)
                    retry_count += 1
                    continue
                
                # Parse error response
                try:
                    error_data = response.json()
                except ValueError:
                    error_data = {
                        'code': 'UNKNOWN',
                        'message': f"HTTP {response.status_code}: {response.text}"
                    }
                
                # Handle error
                error_action = self.error_handler.handle_error(error_data)
                
                if error_action['action'] == 'retry':
                    wait_time = error_action.get('wait_seconds', 10)
                    self._wait_with_jitter(wait_time)
                    retry_count += 1
                    continue
                
                elif error_action['action'] == 'fail':
                    raise APIError(error_data)
                
                elif error_action['action'] == 'modify_request':
                    raise DataError(error_data, suggestions=error_action.get('suggestions'))
                
                else:
                    # Log and retry for unknown actions
                    self._wait_with_jitter(10)
                    retry_count += 1
                    
            except requests.exceptions.ConnectionError as e:
                # Network error - always retry
                if retry_count < max_retries - 1:
                    wait_time = (2 ** retry_count) * 5  # Exponential backoff
                    logging.warning(f"Connection error, retrying in {wait_time}s: {e}")
                    self._wait_with_jitter(wait_time)
                    retry_count += 1
                else:
                    raise NetworkError(f"Connection failed after {max_retries} attempts")
            
            except requests.exceptions.Timeout as e:
                # Timeout - suggest async
                raise TimeoutError(
                    "Request timed out. Consider using async endpoints for large analyses."
                )
        
        # Max retries exceeded
        raise MaxRetriesError(f"Request failed after {max_retries} attempts")
    
    def _wait_with_jitter(self, seconds: int):
        """Wait with random jitter to prevent thundering herd."""
        import random
        jitter = random.uniform(0, 0.1 * seconds)
        time.sleep(seconds + jitter)

# Custom exception classes
class APIError(Exception):
    """Base API error."""
    def __init__(self, error_data: Dict):
        self.code = error_data.get('code')
        self.message = error_data.get('message')
        self.details = error_data.get('details', {})
        super().__init__(self.message)

class DataError(APIError):
    """Data-related errors."""
    def __init__(self, error_data: Dict, suggestions: list = None):
        super().__init__(error_data)
        self.suggestions = suggestions or []

class NetworkError(Exception):
    """Network-related errors."""
    pass

class TimeoutError(Exception):
    """Timeout errors."""
    pass

class MaxRetriesError(Exception):
    """Max retries exceeded."""
    pass
```

## Recovery Strategies

### 1. Automatic Retry with Exponential Backoff

```python
import functools
import random
import time
from typing import Callable, TypeVar, Optional

T = TypeVar('T')

def exponential_backoff_retry(
    max_attempts: int = 5,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    jitter: bool = True,
    exceptions: tuple = (Exception,)
) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    Decorator for automatic retry with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries
        exponential_base: Base for exponential backoff calculation
        jitter: Add random jitter to prevent thundering herd
        exceptions: Tuple of exceptions to catch and retry
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            attempt = 0
            
            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                
                except exceptions as e:
                    attempt += 1
                    
                    if attempt >= max_attempts:
                        logging.error(f"Max retries ({max_attempts}) exceeded for {func.__name__}")
                        raise
                    
                    # Calculate delay
                    delay = min(base_delay * (exponential_base ** (attempt - 1)), max_delay)
                    
                    # Add jitter
                    if jitter:
                        delay *= (0.5 + random.random())
                    
                    logging.warning(
                        f"Attempt {attempt}/{max_attempts} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f}s..."
                    )
                    
                    time.sleep(delay)
            
            return None  # Should never reach here
        
        return wrapper
    return decorator

# Usage example
@exponential_backoff_retry(
    max_attempts=5,
    base_delay=2.0,
    exceptions=(requests.RequestException, APIError)
)
def run_hypothesis_test_with_retry(api_client, hypothesis_id, **params):
    """Run hypothesis test with automatic retry."""
    return api_client.request(
        'POST',
        f'/hypothesis/{hypothesis_id}/test',
        json=params
    )
```

### 2. Circuit Breaker Pattern

```python
from enum import Enum
from datetime import datetime, timedelta
from threading import Lock

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class CircuitBreaker:
    """
    Circuit breaker pattern implementation for API calls.
    
    Prevents cascading failures by temporarily blocking calls to a failing service.
    """
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self._failure_count = 0
        self._last_failure_time = None
        self._state = CircuitState.CLOSED
        self._lock = Lock()
    
    @property
    def state(self) -> CircuitState:
        with self._lock:
            if self._state == CircuitState.OPEN:
                if self._last_failure_time and \
                   datetime.now() - self._last_failure_time > timedelta(seconds=self.recovery_timeout):
                    self._state = CircuitState.HALF_OPEN
            return self._state
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        if self.state == CircuitState.OPEN:
            raise CircuitOpenError("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        
        except self.expected_exception as e:
            self._on_failure()
            raise
    
    def _on_success(self):
        """Reset circuit breaker on successful call."""
        with self._lock:
            self._failure_count = 0
            self._state = CircuitState.CLOSED
    
    def _on_failure(self):
        """Handle failure and potentially open circuit."""
        with self._lock:
            self._failure_count += 1
            self._last_failure_time = datetime.now()
            
            if self._failure_count >= self.failure_threshold:
                self._state = CircuitState.OPEN
                logging.error(
                    f"Circuit breaker opened after {self._failure_count} failures"
                )

class CircuitOpenError(Exception):
    """Raised when circuit breaker is open."""
    pass

# Usage with API client
class ProtectedAPIClient(ResilientAPIClient):
    """API client with circuit breaker protection."""
    
    def __init__(self, api_key: str, base_url: str):
        super().__init__(api_key, base_url)
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=60,
            expected_exception=(APIError, NetworkError)
        )
    
    def request(self, method: str, endpoint: str, **kwargs):
        """Make request with circuit breaker protection."""
        return self.circuit_breaker.call(
            super().request,
            method,
            endpoint,
            **kwargs
        )
```

### 3. Request Hedging

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Callable, Any

class RequestHedger:
    """
    Hedge requests by sending multiple parallel requests and using the first to succeed.
    
    Useful for critical requests where latency is more important than efficiency.
    """
    
    def __init__(self, max_hedged_requests: int = 3, delay_between_hedges: float = 1.0):
        self.max_hedged_requests = max_hedged_requests
        self.delay_between_hedges = delay_between_hedges
        self.executor = ThreadPoolExecutor(max_workers=max_hedged_requests)
    
    def hedge_request(
        self,
        request_func: Callable[..., Any],
        *args,
        **kwargs
    ) -> Any:
        """
        Execute hedged requests and return the first successful result.
        """
        futures = []
        
        # Submit initial request
        futures.append(self.executor.submit(request_func, *args, **kwargs))
        
        # Submit hedged requests with delays
        for i in range(1, self.max_hedged_requests):
            time.sleep(self.delay_between_hedges)
            futures.append(self.executor.submit(request_func, *args, **kwargs))
        
        # Return first successful result
        for future in as_completed(futures):
            try:
                result = future.result()
                # Cancel remaining futures
                for f in futures:
                    if not f.done():
                        f.cancel()
                return result
            except Exception as e:
                logging.warning(f"Hedged request failed: {e}")
                continue
        
        # All requests failed
        raise Exception("All hedged requests failed")

# Usage example
hedger = RequestHedger(max_hedged_requests=3, delay_between_hedges=0.5)

try:
    # Critical request with hedging
    result = hedger.hedge_request(
        api_client.get_test_results,
        test_id="critical_test_123"
    )
except Exception as e:
    logging.error(f"All hedged requests failed: {e}")
```

### 4. Graceful Degradation

```python
from typing import Optional, Dict, List
from datetime import datetime, timedelta

class GracefulDegradationHandler:
    """
    Implement graceful degradation strategies when primary services fail.
    """
    
    def __init__(self, cache_ttl_minutes: int = 60):
        self.cache = {}
        self.cache_ttl = timedelta(minutes=cache_ttl_minutes)
    
    def get_hypothesis_results_with_fallback(
        self,
        api_client: ResilientAPIClient,
        hypothesis_id: str,
        params: Dict
    ) -> Dict:
        """
        Get hypothesis results with multiple fallback strategies.
        """
        
        # Strategy 1: Try primary API
        try:
            results = api_client.run_hypothesis_test(hypothesis_id, **params)
            self._cache_results(hypothesis_id, params, results)
            return results
        
        except (APIError, NetworkError) as e:
            logging.warning(f"Primary API failed: {e}")
        
        # Strategy 2: Check cache
        cached = self._get_cached_results(hypothesis_id, params)
        if cached:
            logging.info("Using cached results")
            return {
                **cached,
                'metadata': {
                    **cached.get('metadata', {}),
                    'source': 'cache',
                    'cached_at': cached['_cached_at']
                }
            }
        
        # Strategy 3: Use simplified analysis
        try:
            logging.info("Falling back to simplified analysis")
            simplified_results = self._run_simplified_analysis(hypothesis_id, params)
            return {
                **simplified_results,
                'metadata': {
                    'source': 'simplified',
                    'warning': 'Results based on simplified analysis due to service issues'
                }
            }
        
        except Exception as e:
            logging.error(f"Simplified analysis also failed: {e}")
        
        # Strategy 4: Return error with partial data
        return self._generate_partial_results(hypothesis_id, params)
    
    def _cache_results(self, hypothesis_id: str, params: Dict, results: Dict):
        """Cache successful results."""
        cache_key = f"{hypothesis_id}_{hash(str(params))}"
        self.cache[cache_key] = {
            **results,
            '_cached_at': datetime.now().isoformat()
        }
    
    def _get_cached_results(
        self,
        hypothesis_id: str,
        params: Dict
    ) -> Optional[Dict]:
        """Retrieve cached results if available and not expired."""
        cache_key = f"{hypothesis_id}_{hash(str(params))}"
        
        if cache_key in self.cache:
            cached = self.cache[cache_key]
            cached_time = datetime.fromisoformat(cached['_cached_at'])
            
            if datetime.now() - cached_time < self.cache_ttl:
                return cached
        
        return None
    
    def _run_simplified_analysis(
        self,
        hypothesis_id: str,
        params: Dict
    ) -> Dict:
        """Run simplified local analysis as fallback."""
        # Implement simplified versions of each hypothesis test
        simplified_tests = {
            'H1': self._simplified_h1_test,
            'H2': self._simplified_h2_test,
            # Add more as needed
        }
        
        test_func = simplified_tests.get(hypothesis_id)
        if test_func:
            return test_func(params)
        
        raise NotImplementedError(f"No simplified test for {hypothesis_id}")
    
    def _simplified_h1_test(self, params: Dict) -> Dict:
        """Simplified H1 test using basic heuristics."""
        # This would contain actual simplified logic
        return {
            'hypothesis_id': 'H1',
            'outcome': 'partial',
            'statistics': {
                'confidence_level': 0.8,  # Lower confidence
                'method': 'simplified'
            },
            'policy_interpretation': {
                'summary': 'Preliminary evidence suggests currency effects',
                'confidence_statement': 'Low confidence - based on simplified analysis'
            }
        }
    
    def _generate_partial_results(
        self,
        hypothesis_id: str,
        params: Dict
    ) -> Dict:
        """Generate partial results with error information."""
        return {
            'hypothesis_id': hypothesis_id,
            'outcome': 'error',
            'error': {
                'message': 'Analysis service unavailable',
                'suggestions': [
                    'Try again later',
                    'Use cached results if available',
                    'Contact support for manual analysis'
                ]
            },
            'partial_data': {
                'request_params': params,
                'timestamp': datetime.now().isoformat()
            }
        }
```

## Monitoring and Alerting

### Error Tracking System

```python
from dataclasses import dataclass
from typing import Dict, List
from collections import defaultdict
import json

@dataclass
class ErrorMetric:
    error_code: str
    count: int
    first_seen: datetime
    last_seen: datetime
    affected_endpoints: List[str]

class ErrorMonitor:
    """
    Monitor and track API errors for alerting and analysis.
    """
    
    def __init__(self, alert_threshold: int = 10, time_window_minutes: int = 5):
        self.alert_threshold = alert_threshold
        self.time_window = timedelta(minutes=time_window_minutes)
        self.error_counts = defaultdict(lambda: defaultdict(int))
        self.error_metrics = {}
        self._lock = Lock()
    
    def record_error(
        self,
        error_code: str,
        endpoint: str,
        details: Optional[Dict] = None
    ):
        """Record an error occurrence."""
        with self._lock:
            now = datetime.now()
            
            # Update counts
            self.error_counts[error_code][endpoint] += 1
            
            # Update metrics
            if error_code not in self.error_metrics:
                self.error_metrics[error_code] = ErrorMetric(
                    error_code=error_code,
                    count=0,
                    first_seen=now,
                    last_seen=now,
                    affected_endpoints=[]
                )
            
            metric = self.error_metrics[error_code]
            metric.count += 1
            metric.last_seen = now
            
            if endpoint not in metric.affected_endpoints:
                metric.affected_endpoints.append(endpoint)
            
            # Check for alerts
            self._check_alerts(error_code)
    
    def _check_alerts(self, error_code: str):
        """Check if error rate exceeds threshold."""
        metric = self.error_metrics[error_code]
        
        # Count recent errors
        recent_count = self._count_recent_errors(error_code)
        
        if recent_count >= self.alert_threshold:
            self._trigger_alert(error_code, recent_count)
    
    def _count_recent_errors(self, error_code: str) -> int:
        """Count errors within time window."""
        # Simplified - in production, use time-series data
        metric = self.error_metrics[error_code]
        if datetime.now() - metric.last_seen < self.time_window:
            return metric.count
        return 0
    
    def _trigger_alert(self, error_code: str, count: int):
        """Trigger alert for high error rate."""
        alert = {
            'severity': 'high',
            'error_code': error_code,
            'count': count,
            'time_window': str(self.time_window),
            'message': f"Error rate exceeded: {count} {error_code} errors in {self.time_window}",
            'timestamp': datetime.now().isoformat()
        }
        
        # Send to monitoring system
        self._send_alert(alert)
    
    def _send_alert(self, alert: Dict):
        """Send alert to monitoring system."""
        # Implement integration with monitoring service
        # e.g., PagerDuty, Slack, email, etc.
        logging.error(f"ALERT: {json.dumps(alert, indent=2)}")
    
    def get_error_summary(self) -> Dict:
        """Get summary of all errors."""
        with self._lock:
            return {
                'total_errors': sum(m.count for m in self.error_metrics.values()),
                'unique_error_codes': len(self.error_metrics),
                'most_common': sorted(
                    [
                        {
                            'code': code,
                            'count': metric.count,
                            'last_seen': metric.last_seen.isoformat()
                        }
                        for code, metric in self.error_metrics.items()
                    ],
                    key=lambda x: x['count'],
                    reverse=True
                )[:10]
            }

# Global error monitor instance
error_monitor = ErrorMonitor(alert_threshold=10, time_window_minutes=5)

# Integration with API client
class MonitoredAPIClient(ResilientAPIClient):
    """API client with integrated error monitoring."""
    
    def request(self, method: str, endpoint: str, **kwargs):
        try:
            return super().request(method, endpoint, **kwargs)
        except APIError as e:
            error_monitor.record_error(e.code, endpoint, e.details)
            raise
```

## Best Practices

### 1. Structured Logging

```python
import structlog
from typing import Any

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

class StructuredErrorLogger:
    """Enhanced logging with structured error context."""
    
    def __init__(self, service_name: str = "yemen-market-api"):
        self.logger = structlog.get_logger(service_name)
    
    def log_api_error(
        self,
        error: APIError,
        request_context: Dict[str, Any]
    ):
        """Log API error with full context."""
        self.logger.error(
            "api_error",
            error_code=error.code,
            error_message=error.message,
            error_details=error.details,
            request_method=request_context.get('method'),
            request_endpoint=request_context.get('endpoint'),
            request_params=request_context.get('params'),
            user_id=request_context.get('user_id'),
            correlation_id=request_context.get('correlation_id')
        )
    
    def log_recovery_attempt(
        self,
        strategy: str,
        attempt: int,
        max_attempts: int
    ):
        """Log recovery attempt."""
        self.logger.info(
            "recovery_attempt",
            strategy=strategy,
            attempt=attempt,
            max_attempts=max_attempts,
            remaining_attempts=max_attempts - attempt
        )
    
    def log_recovery_success(
        self,
        strategy: str,
        duration_seconds: float
    ):
        """Log successful recovery."""
        self.logger.info(
            "recovery_success",
            strategy=strategy,
            duration_seconds=duration_seconds
        )
    
    def log_circuit_breaker_state_change(
        self,
        old_state: str,
        new_state: str,
        failure_count: int
    ):
        """Log circuit breaker state changes."""
        self.logger.warning(
            "circuit_breaker_state_change",
            old_state=old_state,
            new_state=new_state,
            failure_count=failure_count
        )
```

### 2. Error Context Preservation

```python
from contextvars import ContextVar
import uuid

# Context variables for request tracking
request_id_var: ContextVar[str] = ContextVar('request_id')
user_id_var: ContextVar[str] = ContextVar('user_id')

class ContextAwareAPIClient(MonitoredAPIClient):
    """API client that preserves context across error boundaries."""
    
    def request(self, method: str, endpoint: str, **kwargs):
        # Generate request ID if not present
        request_id = request_id_var.get(str(uuid.uuid4()))
        request_id_var.set(request_id)
        
        # Add to headers
        headers = kwargs.get('headers', {})
        headers['X-Request-ID'] = request_id
        kwargs['headers'] = headers
        
        try:
            return super().request(method, endpoint, **kwargs)
        
        except Exception as e:
            # Preserve context in error
            if hasattr(e, 'context'):
                e.context = {}
            
            e.context = {
                'request_id': request_id,
                'user_id': user_id_var.get(None),
                'method': method,
                'endpoint': endpoint,
                'timestamp': datetime.now().isoformat()
            }
            
            raise
```

### 3. Idempotency and Deduplication

```python
from functools import lru_cache
import hashlib

class IdempotentAPIClient(ContextAwareAPIClient):
    """API client with idempotency support."""
    
    def __init__(self, api_key: str, base_url: str):
        super().__init__(api_key, base_url)
        self.pending_requests = {}
        self._lock = Lock()
    
    def _generate_idempotency_key(
        self,
        method: str,
        endpoint: str,
        params: Dict
    ) -> str:
        """Generate unique key for request deduplication."""
        key_data = f"{method}:{endpoint}:{json.dumps(params, sort_keys=True)}"
        return hashlib.sha256(key_data.encode()).hexdigest()
    
    def request_with_idempotency(
        self,
        method: str,
        endpoint: str,
        idempotency_key: Optional[str] = None,
        **kwargs
    ):
        """Make idempotent request with deduplication."""
        
        # Generate key if not provided
        if not idempotency_key:
            idempotency_key = self._generate_idempotency_key(
                method, endpoint, kwargs
            )
        
        # Check for pending request
        with self._lock:
            if idempotency_key in self.pending_requests:
                logging.info(f"Duplicate request detected: {idempotency_key}")
                return self.pending_requests[idempotency_key]
        
        # Add idempotency key to headers
        headers = kwargs.get('headers', {})
        headers['Idempotency-Key'] = idempotency_key
        kwargs['headers'] = headers
        
        try:
            # Store as pending
            with self._lock:
                self.pending_requests[idempotency_key] = None
            
            # Make request
            result = self.request(method, endpoint, **kwargs)
            
            # Cache result
            with self._lock:
                self.pending_requests[idempotency_key] = result
            
            return result
            
        finally:
            # Clean up after delay
            def cleanup():
                time.sleep(300)  # 5 minutes
                with self._lock:
                    self.pending_requests.pop(idempotency_key, None)
            
            threading.Thread(target=cleanup, daemon=True).start()
```

## Troubleshooting Workflows

### Diagnostic Flow Chart

```python
class TroubleshootingGuide:
    """Interactive troubleshooting guide for common issues."""
    
    def diagnose_error(self, error: APIError) -> List[str]:
        """Provide diagnostic steps based on error type."""
        
        diagnostics = {
            'INVALID_API_KEY': [
                "1. Verify API key is correctly copied (no extra spaces)",
                "2. Check key hasn't expired in dashboard",
                "3. Ensure key has required permissions",
                "4. Try regenerating key if issues persist"
            ],
            'INSUFFICIENT_DATA': [
                "1. Check data availability for selected date range",
                "2. Verify markets have price data: GET /markets/{id}",
                "3. Try expanding date range by 3-6 months",
                "4. Consider using different markets with better coverage",
                "5. Check /data/availability endpoint for coverage map"
            ],
            'RATE_LIMIT_EXCEEDED': [
                "1. Check current rate limit status in response headers",
                "2. Implement exponential backoff (see code examples)",
                "3. Consider upgrading to higher tier",
                "4. Batch requests where possible",
                "5. Cache results to reduce API calls"
            ],
            'TEST_NOT_FOUND': [
                "1. Verify test ID format (should be UUID)",
                "2. Check test wasn't deleted",
                "3. Ensure using correct environment (prod/staging)",
                "4. Verify test creation was successful",
                "5. Check if test expired (30-day retention)"
            ],
            'TIMEOUT_ERROR': [
                "1. Check if request is too large (reduce scope)",
                "2. Use async endpoints for long-running tests",
                "3. Consider batch processing with smaller chunks",
                "4. Monitor with SSE for progress updates",
                "5. Verify network connectivity and latency"
            ]
        }
        
        return diagnostics.get(
            error.code,
            [
                "1. Check error details for specific information",
                "2. Verify request parameters match API documentation",
                "3. Try with minimal parameters first",
                "4. Check system status page for outages",
                "5. Contact support with error details and request ID"
            ]
        )
    
    def suggest_recovery_strategy(self, error: APIError) -> Dict[str, Any]:
        """Suggest appropriate recovery strategy."""
        
        strategies = {
            'RATE_LIMIT_EXCEEDED': {
                'strategy': 'exponential_backoff',
                'initial_delay': 60,
                'max_retries': 5,
                'code_example': 'See exponential_backoff_retry decorator'
            },
            'SERVICE_UNAVAILABLE': {
                'strategy': 'circuit_breaker',
                'failure_threshold': 5,
                'recovery_timeout': 300,
                'fallback': 'Use cached results or degraded mode'
            },
            'TIMEOUT_ERROR': {
                'strategy': 'async_processing',
                'alternative': 'Break request into smaller chunks',
                'monitoring': 'Use SSE for progress tracking'
            },
            'INSUFFICIENT_DATA': {
                'strategy': 'expand_parameters',
                'suggestions': [
                    'Increase date range',
                    'Include more markets',
                    'Use data availability endpoint first'
                ]
            }
        }
        
        return strategies.get(error.code, {
            'strategy': 'generic_retry',
            'suggestion': 'Implement exponential backoff with 3-5 retries'
        })

# Usage example
troubleshooter = TroubleshootingGuide()

try:
    result = api_client.run_hypothesis_test("H1", **params)
except APIError as e:
    # Get diagnostic steps
    steps = troubleshooter.diagnose_error(e)
    print(f"Troubleshooting steps for {e.code}:")
    for step in steps:
        print(step)
    
    # Get recovery strategy
    strategy = troubleshooter.suggest_recovery_strategy(e)
    print(f"\nRecommended recovery: {strategy['strategy']}")
```

### Health Check Implementation

```python
class APIHealthChecker:
    """Comprehensive health checking for API integration."""
    
    def __init__(self, api_client: ResilientAPIClient):
        self.api_client = api_client
        self.health_status = {
            'api_reachable': False,
            'authentication_valid': False,
            'data_available': False,
            'hypothesis_tests_working': False,
            'sse_functional': False
        }
    
    async def run_health_checks(self) -> Dict[str, Any]:
        """Run comprehensive health checks."""
        
        # Check 1: API Reachability
        try:
            self.api_client.request('GET', '/health')
            self.health_status['api_reachable'] = True
        except Exception as e:
            logging.error(f"API not reachable: {e}")
        
        # Check 2: Authentication
        try:
            self.api_client.request('GET', '/hypothesis')
            self.health_status['authentication_valid'] = True
        except APIError as e:
            if e.code == 'INVALID_API_KEY':
                logging.error("Authentication failed")
        
        # Check 3: Data Availability
        try:
            markets = self.api_client.request('GET', '/markets?limit=1')
            if markets and markets.get('markets'):
                self.health_status['data_available'] = True
        except Exception as e:
            logging.error(f"Data check failed: {e}")
        
        # Check 4: Hypothesis Test Creation
        try:
            # Create minimal test
            test = self.api_client.request(
                'POST',
                '/hypothesis/H1/test',
                json={
                    'start_date': '2023-01-01',
                    'end_date': '2023-01-31',
                    'markets': ['Sana\'a']
                }
            )
            if test and test.get('id'):
                self.health_status['hypothesis_tests_working'] = True
                
                # Clean up
                self.api_client.request('DELETE', f"/hypothesis/test/{test['id']}")
        except Exception as e:
            logging.error(f"Hypothesis test check failed: {e}")
        
        # Check 5: SSE Functionality
        # This would be more complex in practice
        self.health_status['sse_functional'] = self.health_status['api_reachable']
        
        # Generate report
        return {
            'timestamp': datetime.now().isoformat(),
            'overall_health': all(self.health_status.values()),
            'checks': self.health_status,
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on health check results."""
        recommendations = []
        
        if not self.health_status['api_reachable']:
            recommendations.append("Check network connectivity and API endpoint URL")
        
        if not self.health_status['authentication_valid']:
            recommendations.append("Verify API key and regenerate if necessary")
        
        if not self.health_status['data_available']:
            recommendations.append("Contact support - data service may be down")
        
        return recommendations
```

---

*This comprehensive error handling guide ensures robust integration with the Yemen Market Integration API, enabling reliable analysis even in challenging network conditions and service disruptions.*