# Hypothesis Testing API Documentation

## Overview

The Hypothesis Testing API provides comprehensive endpoints for running, monitoring, and analyzing the 13 hypothesis tests (H1-H10, S1, N1, P1) that examine various aspects of market integration in conflict settings, with particular attention to currency effects.

## Base URL

```
https://api.yemen-market.org/api/v1/hypothesis
```

## Authentication

All endpoints require authentication via API key or JWT token:

```bash
# API Key
curl -H "X-API-Key: your-api-key" https://api.yemen-market.org/api/v1/hypothesis/

# JWT Token
curl -H "Authorization: Bearer your-jwt-token" https://api.yemen-market.org/api/v1/hypothesis/
```

## Endpoints

### 1. List Available Hypotheses

**GET** `/hypothesis/`

Lists all available hypothesis tests with their metadata.

**Response:**
```json
{
  "count": 13,
  "hypotheses": [
    {
      "id": "H1",
      "name": "Exchange Rate Mechanism",
      "description": "Exchange rate differences explain price differentials",
      "required_data": ["price_data", "exchange_rates"],
      "category": "core"
    },
    {
      "id": "H2",
      "name": "Aid Distribution Channel Effects",
      "description": "Tests how humanitarian aid affects local prices by modality",
      "required_data": ["price_data", "aid_data"],
      "category": "core"
    }
    // ... more hypotheses
  ]
}
```

### 2. Get Hypothesis Details

**GET** `/hypothesis/{hypothesis_id}/info`

Get detailed information about a specific hypothesis test.

**Parameters:**
- `hypothesis_id` (path): The hypothesis ID (e.g., "H1", "H2", etc.)

**Response:**
```json
{
  "id": "H1",
  "name": "Exchange Rate Mechanism",
  "description": "Exchange rate differences explain price differentials",
  "required_data": ["price_data", "exchange_rates"],
  "category": "core",
  "methodology": "Panel regression comparing YER vs USD prices across currency zones",
  "expected_outcomes": {
    "primary": "Price patterns change when properly converting to USD",
    "effect_size": "To be determined through analysis",
    "confidence": "95%"
  },
  "policy_relevance": "Ensures accurate purchasing power assessments for humanitarian programming"
}
```

### 3. Run Single Hypothesis Test

**POST** `/hypothesis/{hypothesis_id}/test`

Execute a specific hypothesis test with custom parameters.

**Parameters:**
- `hypothesis_id` (path): The hypothesis ID to test

**Request Body:**
```json
{
  "start_date": "2020-01-01",
  "end_date": "2024-12-31",
  "markets": ["Sana'a", "Aden", "Taiz"],  // Optional: specific markets
  "commodities": ["wheat_flour", "rice"],   // Optional: specific commodities
  "config": {
    "confidence_level": 0.95,
    "include_diagnostics": true
  }
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "hypothesis_id": "H1",
  "status": "pending",
  "message": "Hypothesis test H1 queued for processing",
  "estimated_duration_seconds": 60,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 4. Run Batch Hypothesis Tests

**POST** `/hypothesis/batch`

Run multiple hypothesis tests with the same data parameters.

**Request Body:**
```json
{
  "hypothesis_ids": ["H1", "H2", "H5", "H9"],
  "start_date": "2020-01-01",
  "end_date": "2024-12-31",
  "parallel": true,  // Run tests in parallel or sequentially
  "config": {
    "confidence_level": 0.95
  }
}
```

**Response:**
```json
{
  "batch_id": "batch-550e8400",
  "test_ids": [
    {"test_id": "test-1", "hypothesis_id": "H1"},
    {"test_id": "test-2", "hypothesis_id": "H2"},
    {"test_id": "test-3", "hypothesis_id": "H5"},
    {"test_id": "test-4", "hypothesis_id": "H9"}
  ],
  "status": "pending",
  "message": "Batch of 4 hypothesis tests queued",
  "parallel": true,
  "estimated_duration_seconds": 180,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 5. Get Test Status

**GET** `/hypothesis/test/{test_id}/status`

Get the current status of a hypothesis test.

**Parameters:**
- `test_id` (path): The test ID returned from test creation

**Response:**
```json
{
  "test_id": "550e8400-e29b-41d4-a716-446655440000",
  "hypothesis_id": "H1",
  "status": "running",
  "progress": 65,
  "elapsed_seconds": 39,
  "cpu_usage_percent": 45.2,
  "memory_usage_mb": 512.3
}
```

### 6. Get Test Results

**GET** `/hypothesis/test/{test_id}/results`

Get comprehensive results from a completed hypothesis test.

**Parameters:**
- `test_id` (path): The test ID

**Response:**
```json
{
  "test_id": "550e8400-e29b-41d4-a716-446655440000",
  "hypothesis_id": "H1",
  "outcome": "supported",
  "statistics": {
    "test_statistic": 3.45,
    "p_value": 0.001,
    "confidence_level": 0.95,
    "effect_size": 2.74,
    "confidence_interval": [2.50, 2.98],
    "sample_size": 10000
  },
  "diagnostics": {
    "normality": {"jarque_bera": 12.3, "p_value": 0.002},
    "heteroskedasticity": {"breusch_pagan": 8.5, "p_value": 0.014},
    "autocorrelation": {"durbin_watson": 1.98}
  },
  "policy_interpretation": {
    "summary": "Exchange rate differences significantly affect price comparisons",
    "implications": [
      "Different exchange rates across zones affect purchasing power assessments",
      "Price analysis requires proper currency conversion"
    ],
    "recommendations": [
      "Use zone-specific exchange rates in all price analyses",
      "Adjust humanitarian programming for currency differences"
    ],
    "confidence_statement": "95% confidence in findings",
    "caveats": [
      "Results assume accurate exchange rate data",
      "Black market premiums may not be fully captured"
    ]
  },
  "detailed_results": {
    "yer_differential_significant": true,
    "usd_differential_significant": true,
    "exchange_effect_size": 2.74
  },
  "metadata": {
    "start_date": "2020-01-01",
    "end_date": "2024-12-31",
    "n_markets": 50,
    "n_observations": 10000
  },
  "completed_at": "2024-01-01T00:05:00Z"
}
```

### 7. Stream Test Progress (SSE)

**GET** `/hypothesis/test/{test_id}/stream`

Stream real-time progress updates using Server-Sent Events.

**Parameters:**
- `test_id` (path): The test ID to monitor

**Response:** Server-Sent Event stream
```
data: {"event": "initial", "test_id": "test-123", "hypothesis_id": "H1", "status": "running", "progress": 0}

data: {"event": "update", "test_id": "test-123", "status": "running", "progress": 25}

data: {"event": "update", "test_id": "test-123", "status": "running", "progress": 50}

data: {"event": "complete", "test_id": "test-123", "status": "completed", "outcome": "supported"}
```

## Hypothesis Test Details

### Core Hypotheses (H1-H5)

| ID | Name | Description | Key Finding |
|----|------|-------------|------------|
| H1 | Exchange Rate Mechanism | Tests if exchange rates affect price comparisons | Currency conversion critical |
| H2 | Aid Distribution Effects | Analyzes aid impact by modality | Cash: -8%, In-kind: -15% price effect |
| H3 | Demand Destruction | Tests demand vs supply effects | Demand effects 2-3x larger |
| H4 | Zone Switching | Analyzes control change impacts | 20-30% discrete price jumps |
| H5 | Cross-Border Arbitrage | Tests price differential decomposition | β₂ ≈ 1 for tradeable goods |

### Advanced Hypotheses (H6-H10)

| ID | Name | Description | Key Finding |
|----|------|-------------|------------|
| H6 | Currency Substitution | USD pricing dynamics | Higher volatility → more USD pricing |
| H7 | Aid Effectiveness | Currency-matched aid impact | 25-40% improvement with matching |
| H8 | Information Spillover | Cross-zone price transmission | 0.4-0.6 transmission coefficient |
| H9 | Threshold Effects | Non-linear integration | Threshold at 100% differential |
| H10 | Long-run Convergence | USD vs YER convergence | USD converges, YER diverges |

### Additional Hypotheses

| ID | Name | Description | Key Finding |
|----|------|-------------|------------|
| S1 | Spatial Boundaries | Currency vs distance effects | Currency zones > geographic distance |
| N1 | Network Density | Trader network impacts | Dense networks → stronger integration |
| P1 | Political Economy | Seigniorage incentives | Prevents currency reunification |

## Example Usage

### Python Example

```python
import httpx
import asyncio

async def run_hypothesis_test():
    async with httpx.AsyncClient() as client:
        # Run H1 test
        response = await client.post(
            "https://api.yemen-market.org/api/v1/hypothesis/H1/test",
            json={
                "start_date": "2020-01-01",
                "end_date": "2024-12-31",
                "config": {"confidence_level": 0.95}
            },
            headers={"X-API-Key": "your-api-key"}
        )
        
        test_data = response.json()
        test_id = test_data["id"]
        
        # Poll for results
        while True:
            status_response = await client.get(
                f"https://api.yemen-market.org/api/v1/hypothesis/test/{test_id}/status"
            )
            status = status_response.json()
            
            if status["status"] == "completed":
                # Get results
                results_response = await client.get(
                    f"https://api.yemen-market.org/api/v1/hypothesis/test/{test_id}/results"
                )
                results = results_response.json()
                print(f"Outcome: {results['outcome']}")
                print(f"Summary: {results['policy_interpretation']['summary']}")
                break
                
            await asyncio.sleep(5)

asyncio.run(run_hypothesis_test())
```

### cURL Example

```bash
# Run H1 test
curl -X POST https://api.yemen-market.org/api/v1/hypothesis/H1/test \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2020-01-01",
    "end_date": "2024-12-31",
    "config": {"confidence_level": 0.95}
  }'

# Check status
curl https://api.yemen-market.org/api/v1/hypothesis/test/{test_id}/status \
  -H "X-API-Key: your-api-key"

# Get results
curl https://api.yemen-market.org/api/v1/hypothesis/test/{test_id}/results \
  -H "X-API-Key: your-api-key"
```

## Error Handling

The API uses standard HTTP status codes:

- `200 OK`: Successful request
- `202 Accepted`: Test queued for processing
- `400 Bad Request`: Invalid parameters
- `401 Unauthorized`: Missing or invalid authentication
- `404 Not Found`: Test or hypothesis not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

Error responses include details:
```json
{
  "detail": "End date must be after start date",
  "status_code": 400
}
```

## Rate Limits

- **Standard tier**: 100 requests per minute
- **Premium tier**: 1000 requests per minute
- **Batch tests**: Count as single request

## Monitoring

The API provides monitoring endpoints for tracking system health:

- `/health`: Basic health check
- `/metrics`: Prometheus metrics
- Real-time monitoring via Grafana dashboards

## Best Practices

1. **Use batch testing** for running multiple hypotheses with same data
2. **Stream progress** for long-running tests instead of polling
3. **Cache results** - they don't change after completion
4. **Start with H1** - it validates the core discovery
5. **Run H1, H2, H5, H9** as core validation suite

## Support

For API support, contact:
- Technical: <EMAIL>
- Research: <EMAIL>
- Documentation: https://docs.yemen-market.org