# Production Deployment Guide

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Infrastructure Requirements](#infrastructure-requirements)
4. [Pre-Deployment Checklist](#pre-deployment-checklist)
5. [Deployment Steps](#deployment-steps)
6. [Configuration Management](#configuration-management)
7. [Database Setup](#database-setup)
8. [Security Hardening](#security-hardening)
9. [Performance Optimization](#performance-optimization)
10. [Monitoring Setup](#monitoring-setup)
11. [Backup and Recovery](#backup-and-recovery)
12. [Post-Deployment Validation](#post-deployment-validation)
13. [Rollback Procedures](#rollback-procedures)
14. [Operational Runbooks](#operational-runbooks)

## Overview

This guide provides comprehensive instructions for deploying the Yemen Market Integration API to production, ensuring high availability, security, and performance for critical humanitarian analysis workloads.

### System Components

- **API Service**: FastAPI application serving hypothesis testing endpoints
- **Worker Service**: Celery workers for async hypothesis test execution
- **Database**: PostgreSQL for persistent storage
- **Cache**: Redis for caching and job queuing
- **Load Balancer**: NGINX for traffic distribution
- **Monitoring**: Prometheus + Grafana for observability

### Deployment Targets

- **Kubernetes**: Primary production environment
- **Docker Swarm**: Alternative for smaller deployments
- **AWS ECS**: Cloud-native option

## Architecture

### High-Level Architecture

```
                           ┌─────────────────┐
                           │  Load Balancer  │
                           │    (NGINX)      │
                           └────────┬────────┘
                                   │
                        ┌──────────┴──────────┐
                        │                     │
                   ┌────▼─────┐         ┌────▼─────┐
                   │  API-1    │         │  API-2    │
                   │ (FastAPI) │         │ (FastAPI) │
                   └─────┬─────┘         └─────┬─────┘
                         │                     │
              ┌──────────┴─────────────────────┴──────────┐
              │                                           │
         ┌────▼─────┐                               ┌────▼─────┐
         │  Redis   │                               │PostgreSQL│
         │ (Cache)  │                               │(Database)│
         └────┬─────┘                               └──────────┘
              │
     ┌────────┴────────┐
     │                 │
┌────▼─────┐     ┌────▼─────┐
│ Worker-1 │     │ Worker-2 │
│ (Celery) │     │ (Celery) │
└──────────┘     └──────────┘
```

### Component Specifications

| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| API | FastAPI | 0.104+ | REST API endpoints |
| Workers | Celery | 5.3+ | Async job processing |
| Database | PostgreSQL | 15+ | Data persistence |
| Cache | Redis | 7+ | Caching & job queue |
| Load Balancer | NGINX | 1.24+ | Traffic distribution |
| Container | Docker | 24+ | Containerization |
| Orchestration | Kubernetes | 1.28+ | Container orchestration |

## Infrastructure Requirements

### Minimum Production Requirements

```yaml
# API Servers (2 instances minimum)
api_servers:
  cpu: 4 cores
  memory: 8 GB
  disk: 50 GB SSD
  network: 1 Gbps

# Worker Nodes (2 instances minimum)
worker_nodes:
  cpu: 8 cores
  memory: 16 GB
  disk: 100 GB SSD
  network: 1 Gbps

# Database Server
database:
  cpu: 8 cores
  memory: 32 GB
  disk: 500 GB SSD (RAID 10)
  network: 10 Gbps
  backup: Daily snapshots

# Redis Cache
redis:
  cpu: 4 cores
  memory: 16 GB
  disk: 50 GB SSD
  persistence: AOF enabled

# Load Balancer
load_balancer:
  cpu: 2 cores
  memory: 4 GB
  disk: 20 GB
  network: 10 Gbps
```

### Network Requirements

- **Internal Network**: 10 Gbps between components
- **External Bandwidth**: 1 Gbps minimum
- **SSL Certificates**: Valid certificates for HTTPS
- **DNS**: Configured A/AAAA records
- **Firewall Rules**: Documented below

## Pre-Deployment Checklist

### 1. Infrastructure Validation

```bash
#!/bin/bash
# infrastructure_check.sh

echo "=== Infrastructure Validation ==="

# Check Docker version
docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+')
echo "Docker version: $docker_version"
if (( $(echo "$docker_version < 24.0" | bc -l) )); then
    echo "❌ Docker version too old. Required: 24+"
    exit 1
fi

# Check Kubernetes access
kubectl version --short
if [ $? -ne 0 ]; then
    echo "❌ Cannot connect to Kubernetes cluster"
    exit 1
fi

# Verify namespaces
kubectl get namespace yemen-market-prod
if [ $? -ne 0 ]; then
    echo "Creating production namespace..."
    kubectl create namespace yemen-market-prod
fi

# Check storage classes
kubectl get storageclass
echo "✅ Infrastructure validation complete"
```

### 2. Security Checklist

- [ ] SSL certificates obtained and validated
- [ ] API keys generated and stored in secrets manager
- [ ] Database passwords rotated
- [ ] Network policies configured
- [ ] RBAC policies defined
- [ ] Security scanning completed
- [ ] Penetration testing performed

### 3. Configuration Preparation

```yaml
# config/production.yaml
environment: production

api:
  host: 0.0.0.0
  port: 8000
  workers: 4
  debug: false
  cors_origins:
    - https://yemen-market-integration.org
    - https://app.yemen-market-integration.org

database:
  host: ${DB_HOST}
  port: 5432
  name: yemen_market_prod
  user: ${DB_USER}
  password: ${DB_PASSWORD}
  pool_size: 20
  max_overflow: 10
  pool_timeout: 30
  pool_recycle: 3600

redis:
  host: ${REDIS_HOST}
  port: 6379
  password: ${REDIS_PASSWORD}
  db: 0
  decode_responses: true
  socket_timeout: 5
  socket_connect_timeout: 5

celery:
  broker_url: redis://:${REDIS_PASSWORD}@${REDIS_HOST}:6379/0
  result_backend: redis://:${REDIS_PASSWORD}@${REDIS_HOST}:6379/1
  task_serializer: json
  result_serializer: json
  accept_content: ["json"]
  timezone: UTC
  enable_utc: true
  task_soft_time_limit: 3600
  task_time_limit: 7200

monitoring:
  sentry_dsn: ${SENTRY_DSN}
  prometheus_port: 9090
  enable_tracing: true
  trace_sample_rate: 0.1

security:
  jwt_secret: ${JWT_SECRET}
  jwt_algorithm: HS256
  jwt_expire_minutes: 1440
  api_key_header: X-API-Key
  rate_limit_per_minute: 100
  rate_limit_burst: 20
```

## Deployment Steps

### 1. Kubernetes Deployment

#### Create Secrets

```bash
# Create secrets for sensitive data
kubectl create secret generic yemen-api-secrets \
  --from-literal=db-password="${DB_PASSWORD}" \
  --from-literal=redis-password="${REDIS_PASSWORD}" \
  --from-literal=jwt-secret="${JWT_SECRET}" \
  --from-literal=sentry-dsn="${SENTRY_DSN}" \
  -n yemen-market-prod

# Create API key secret
kubectl create secret generic api-keys \
  --from-file=api-keys.json \
  -n yemen-market-prod
```

#### Deploy Database

```yaml
# kubernetes/postgres.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: yemen-market-prod
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 500Gi
  storageClassName: fast-ssd

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: yemen-market-prod
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: yemen_market_prod
        - name: POSTGRES_USER
          value: yemen_api
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: db-password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            cpu: 4
            memory: 16Gi
          limits:
            cpu: 8
            memory: 32Gi
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - yemen_api
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - yemen_api
          initialDelaySeconds: 5
          periodSeconds: 5
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 500Gi
      storageClassName: fast-ssd

---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: yemen-market-prod
spec:
  ports:
  - port: 5432
    targetPort: 5432
  selector:
    app: postgres
  type: ClusterIP
```

#### Deploy Redis

```yaml
# kubernetes/redis.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: yemen-market-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        - --appendonly
        - "yes"
        - --appendfsync
        - everysec
        ports:
        - containerPort: 6379
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: redis-password
        resources:
          requests:
            cpu: 2
            memory: 8Gi
          limits:
            cpu: 4
            memory: 16Gi
        volumeMounts:
        - name: redis-data
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: yemen-market-prod
spec:
  ports:
  - port: 6379
    targetPort: 6379
  selector:
    app: redis
  type: ClusterIP
```

#### Deploy API Service

```yaml
# kubernetes/api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yemen-api
  namespace: yemen-market-prod
  labels:
    app: yemen-api
    version: v2.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: yemen-api
  template:
    metadata:
      labels:
        app: yemen-api
        version: v2.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - yemen-api
            topologyKey: kubernetes.io/hostname
      containers:
      - name: api
        image: yemen-market-integration/api:v2.0.0
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: ENVIRONMENT
          value: production
        - name: DB_HOST
          value: postgres
        - name: DB_USER
          value: yemen_api
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: db-password
        - name: REDIS_HOST
          value: redis
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: redis-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: jwt-secret
        - name: SENTRY_DSN
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: sentry-dsn
        resources:
          requests:
            cpu: 2
            memory: 4Gi
          limits:
            cpu: 4
            memory: 8Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]
      initContainers:
      - name: db-migration
        image: yemen-market-integration/api:v2.0.0
        command: ["alembic", "upgrade", "head"]
        env:
        - name: DB_HOST
          value: postgres
        - name: DB_USER
          value: yemen_api
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: db-password

---
apiVersion: v1
kind: Service
metadata:
  name: yemen-api
  namespace: yemen-market-prod
  labels:
    app: yemen-api
spec:
  ports:
  - port: 80
    targetPort: 8000
    name: http
  - port: 9090
    targetPort: 9090
    name: metrics
  selector:
    app: yemen-api
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: yemen-api-hpa
  namespace: yemen-market-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: yemen-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
```

#### Deploy Workers

```yaml
# kubernetes/worker-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yemen-worker
  namespace: yemen-market-prod
spec:
  replicas: 4
  selector:
    matchLabels:
      app: yemen-worker
  template:
    metadata:
      labels:
        app: yemen-worker
    spec:
      containers:
      - name: worker
        image: yemen-market-integration/worker:v2.0.0
        command: ["celery", "-A", "src.infrastructure.workers", "worker", "--loglevel=info", "--concurrency=4"]
        env:
        - name: ENVIRONMENT
          value: production
        - name: DB_HOST
          value: postgres
        - name: DB_USER
          value: yemen_api
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: db-password
        - name: REDIS_HOST
          value: redis
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: yemen-api-secrets
              key: redis-password
        resources:
          requests:
            cpu: 4
            memory: 8Gi
          limits:
            cpu: 8
            memory: 16Gi
        livenessProbe:
          exec:
            command:
            - celery
            - -A
            - src.infrastructure.workers
            - inspect
            - ping
          initialDelaySeconds: 30
          periodSeconds: 60
          timeoutSeconds: 10

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: yemen-worker-hpa
  namespace: yemen-market-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: yemen-worker
  minReplicas: 4
  maxReplicas: 20
  metrics:
  - type: External
    external:
      metric:
        name: celery_queue_length
        selector:
          matchLabels:
            queue: hypothesis_tests
      target:
        type: AverageValue
        averageValue: "10"
```

#### Configure Ingress

```yaml
# kubernetes/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: yemen-api-ingress
  namespace: yemen-market-prod
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.yemen-market-integration.org
    secretName: yemen-api-tls
  rules:
  - host: api.yemen-market-integration.org
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: yemen-api
            port:
              number: 80
```

### 2. Database Migration

```bash
#!/bin/bash
# migrate_database.sh

echo "=== Database Migration ==="

# Port forward to database
kubectl port-forward -n yemen-market-prod svc/postgres 5432:5432 &
PF_PID=$!
sleep 5

# Run migrations
export DATABASE_URL="postgresql://yemen_api:${DB_PASSWORD}@localhost:5432/yemen_market_prod"

# Backup current schema
pg_dump $DATABASE_URL --schema-only > backup/schema_$(date +%Y%m%d_%H%M%S).sql

# Run Alembic migrations
alembic upgrade head

# Verify migration
alembic current

# Seed initial data
python scripts/seed_production_data.py

# Kill port forward
kill $PF_PID

echo "✅ Database migration complete"
```

### 3. SSL Certificate Setup

```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Create ClusterIssuer
cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
```

## Configuration Management

### Environment Variables

```bash
# .env.production
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_DEBUG=false

# Database
DB_HOST=postgres.yemen-market-prod.svc.cluster.local
DB_PORT=5432
DB_NAME=yemen_market_prod
DB_USER=yemen_api
DB_PASSWORD=${SECURE_DB_PASSWORD}

# Redis
REDIS_HOST=redis.yemen-market-prod.svc.cluster.local
REDIS_PORT=6379
REDIS_PASSWORD=${SECURE_REDIS_PASSWORD}

# Security
JWT_SECRET=${SECURE_JWT_SECRET}
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# Monitoring
SENTRY_DSN=${SENTRY_DSN}
PROMETHEUS_PORT=9090
ENABLE_TRACING=true
TRACE_SAMPLE_RATE=0.1

# External Services
ACLED_API_KEY=${ACLED_API_KEY}
HDX_API_KEY=${HDX_API_KEY}
WFP_API_KEY=${WFP_API_KEY}
```

### ConfigMap for Non-Sensitive Config

```yaml
# kubernetes/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: yemen-api-config
  namespace: yemen-market-prod
data:
  api_config.yaml: |
    environment: production
    
    cors:
      origins:
        - https://yemen-market-integration.org
        - https://app.yemen-market-integration.org
      allow_credentials: true
      allow_methods: ["*"]
      allow_headers: ["*"]
    
    rate_limiting:
      standard_tier:
        requests_per_minute: 100
        burst: 20
      research_tier:
        requests_per_minute: 1000
        burst: 100
      enterprise_tier:
        requests_per_minute: 10000
        burst: 500
    
    hypothesis_tests:
      timeout_seconds: 3600
      max_retries: 3
      retry_delay: 60
    
    data_sources:
      wfp:
        base_url: https://dataviz.vam.wfp.org/api
        timeout: 30
      acled:
        base_url: https://api.acleddata.com
        timeout: 30
      hdx:
        base_url: https://data.humdata.org/api/3
        timeout: 30
```

## Security Hardening

### 1. Network Policies

```yaml
# kubernetes/network-policies.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-network-policy
  namespace: yemen-market-prod
spec:
  podSelector:
    matchLabels:
      app: yemen-api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - podSelector:
        matchLabels:
          app: yemen-worker
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - namespaceSelector: {}
      podSelector:
        matchLabels:
          k8s-app: kube-dns
    ports:
    - protocol: UDP
      port: 53
  - to:
    - ipBlock:
        cidr: 0.0.0.0/0
        except:
        - ***************/32  # Block AWS metadata
    ports:
    - protocol: TCP
      port: 443  # HTTPS only for external
```

### 2. Pod Security Policies

```yaml
# kubernetes/pod-security-policy.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: yemen-api-psp
  namespace: yemen-market-prod
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true
```

### 3. RBAC Configuration

```yaml
# kubernetes/rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: yemen-api-sa
  namespace: yemen-market-prod

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: yemen-api-role
  namespace: yemen-market-prod
rules:
- apiGroups: [""]
  resources: ["secrets"]
  resourceNames: ["yemen-api-secrets", "api-keys"]
  verbs: ["get"]
- apiGroups: [""]
  resources: ["configmaps"]
  resourceNames: ["yemen-api-config"]
  verbs: ["get", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: yemen-api-rolebinding
  namespace: yemen-market-prod
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: yemen-api-role
subjects:
- kind: ServiceAccount
  name: yemen-api-sa
  namespace: yemen-market-prod
```

## Performance Optimization

### 1. API Performance Tuning

```python
# src/infrastructure/performance/optimization.py
from fastapi import FastAPI, Request
from fastapi.responses import ORJSONResponse
import orjson
from starlette.middleware.gzip import GZipMiddleware
from starlette.middleware.trustedhost import TrustedHostMiddleware
import uvloop

# Use uvloop for better async performance
uvloop.install()

def configure_performance(app: FastAPI):
    """Configure performance optimizations."""
    
    # Use ORJSON for faster JSON serialization
    app.default_response_class = ORJSONResponse
    
    # Enable response compression
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Security middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["api.yemen-market-integration.org", "*.yemen-market-integration.org"]
    )
    
    # Connection pooling configuration
    @app.on_event("startup")
    async def configure_db_pool():
        from sqlalchemy.ext.asyncio import create_async_engine
        from sqlalchemy.pool import NullPool
        
        engine = create_async_engine(
            DATABASE_URL,
            pool_size=20,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False
        )
        
        app.state.db_engine = engine
    
    # Redis connection pooling
    @app.on_event("startup")
    async def configure_redis_pool():
        import redis.asyncio as redis
        
        pool = redis.ConnectionPool(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            db=0,
            max_connections=50,
            decode_responses=True
        )
        
        app.state.redis_pool = pool
```

### 2. Database Optimization

```sql
-- Performance indexes
CREATE INDEX CONCURRENTLY idx_prices_market_commodity_date 
ON prices(market_id, commodity_id, date_recorded DESC);

CREATE INDEX CONCURRENTLY idx_prices_date_recorded 
ON prices(date_recorded DESC);

CREATE INDEX CONCURRENTLY idx_hypothesis_tests_status 
ON hypothesis_tests(status) WHERE status IN ('pending', 'running');

CREATE INDEX CONCURRENTLY idx_hypothesis_tests_created_at 
ON hypothesis_tests(created_at DESC);

-- Partitioning for large tables
CREATE TABLE prices_2024 PARTITION OF prices
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- Vacuum and analyze
VACUUM ANALYZE prices;
VACUUM ANALYZE hypothesis_tests;

-- Connection pooling configuration
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '8GB';
ALTER SYSTEM SET effective_cache_size = '24GB';
ALTER SYSTEM SET maintenance_work_mem = '2GB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
```

### 3. Caching Strategy

```python
# src/infrastructure/caching/strategy.py
from typing import Optional, Any
import hashlib
import json
from datetime import timedelta

class CacheStrategy:
    """Intelligent caching strategy for API responses."""
    
    def __init__(self, redis_pool):
        self.redis = redis_pool
    
    def get_cache_key(self, endpoint: str, params: dict) -> str:
        """Generate cache key from endpoint and parameters."""
        param_str = json.dumps(params, sort_keys=True)
        key_data = f"{endpoint}:{param_str}"
        return f"api:cache:{hashlib.md5(key_data.encode()).hexdigest()}"
    
    async def get_cached(self, key: str) -> Optional[Any]:
        """Get cached response."""
        async with self.redis.client() as conn:
            data = await conn.get(key)
            if data:
                return orjson.loads(data)
        return None
    
    async def set_cached(
        self, 
        key: str, 
        data: Any, 
        ttl: timedelta
    ):
        """Cache response with TTL."""
        async with self.redis.client() as conn:
            await conn.setex(
                key,
                int(ttl.total_seconds()),
                orjson.dumps(data)
            )
    
    def get_ttl(self, endpoint: str) -> timedelta:
        """Determine TTL based on endpoint."""
        ttl_map = {
            "/hypothesis": timedelta(hours=24),  # Rarely changes
            "/hypothesis/*/info": timedelta(hours=24),
            "/markets": timedelta(hours=6),
            "/commodities": timedelta(hours=12),
            "/prices": timedelta(minutes=15),  # More dynamic
            "/hypothesis/test/*/results": timedelta(days=7)  # Immutable
        }
        
        for pattern, ttl in ttl_map.items():
            if self._matches_pattern(endpoint, pattern):
                return ttl
        
        return timedelta(minutes=5)  # Default
```

## Monitoring Setup

### 1. Prometheus Configuration

```yaml
# kubernetes/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: yemen-market-prod
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    scrape_configs:
      - job_name: 'yemen-api'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - yemen-market-prod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets: ['alertmanager:9093']
    
    rule_files:
      - '/etc/prometheus/alerts/*.yml'
```

### 2. Alert Rules

```yaml
# kubernetes/prometheus-alerts.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-alerts
  namespace: yemen-market-prod
data:
  api_alerts.yml: |
    groups:
      - name: api_alerts
        interval: 30s
        rules:
          - alert: APIHighErrorRate
            expr: |
              (
                sum(rate(http_requests_total{status=~"5.."}[5m])) BY (job)
                /
                sum(rate(http_requests_total[5m])) BY (job)
              ) > 0.05
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "High error rate on {{ $labels.job }}"
              description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }}"
          
          - alert: APIHighLatency
            expr: |
              histogram_quantile(0.95, 
                sum(rate(http_request_duration_seconds_bucket[5m])) BY (le, job)
              ) > 2.0
            for: 10m
            labels:
              severity: warning
            annotations:
              summary: "High API latency on {{ $labels.job }}"
              description: "95th percentile latency is {{ $value }}s for {{ $labels.job }}"
          
          - alert: HypothesisTestBacklog
            expr: |
              celery_queue_length{queue="hypothesis_tests"} > 100
            for: 15m
            labels:
              severity: warning
            annotations:
              summary: "Large hypothesis test backlog"
              description: "{{ $value }} tests queued"
          
          - alert: DatabaseConnectionPoolExhausted
            expr: |
              (
                sqlalchemy_pool_size - sqlalchemy_pool_checked_in_connections
              ) / sqlalchemy_pool_size < 0.1
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Database connection pool nearly exhausted"
              description: "Only {{ $value | humanizePercentage }} connections available"
```

### 3. Grafana Dashboards

```json
// grafana/api-dashboard.json
{
  "dashboard": {
    "title": "Yemen Market Integration API",
    "panels": [
      {
        "title": "Request Rate",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total[5m])) by (method, endpoint)"
          }
        ]
      },
      {
        "title": "Error Rate",
        "targets": [
          {
            "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) / sum(rate(http_requests_total[5m]))"
          }
        ]
      },
      {
        "title": "Response Time (p50, p95, p99)",
        "targets": [
          {
            "expr": "histogram_quantile(0.5, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
            "legendFormat": "p50"
          },
          {
            "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
            "legendFormat": "p95"
          },
          {
            "expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
            "legendFormat": "p99"
          }
        ]
      },
      {
        "title": "Active Hypothesis Tests",
        "targets": [
          {
            "expr": "sum(hypothesis_tests_active) by (hypothesis_id)"
          }
        ]
      },
      {
        "title": "Database Connections",
        "targets": [
          {
            "expr": "sqlalchemy_pool_checked_out_connections",
            "legendFormat": "Active"
          },
          {
            "expr": "sqlalchemy_pool_size",
            "legendFormat": "Pool Size"
          }
        ]
      },
      {
        "title": "Redis Memory Usage",
        "targets": [
          {
            "expr": "redis_memory_used_bytes / (1024 * 1024 * 1024)"
          }
        ]
      }
    ]
  }
}
```

## Backup and Recovery

### 1. Automated Backup Configuration

```yaml
# kubernetes/backup-cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: yemen-market-prod
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-api-secrets
                  key: db-password
            command:
            - /bin/sh
            - -c
            - |
              DATE=$(date +%Y%m%d_%H%M%S)
              pg_dump -h postgres -U yemen_api -d yemen_market_prod | gzip > /backup/yemen_market_prod_${DATE}.sql.gz
              
              # Upload to S3
              aws s3 cp /backup/yemen_market_prod_${DATE}.sql.gz s3://yemen-market-backups/postgres/
              
              # Keep only last 30 days of backups
              find /backup -name "*.sql.gz" -mtime +30 -delete
            volumeMounts:
            - name: backup
              mountPath: /backup
          volumes:
          - name: backup
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
```

### 2. Recovery Procedures

```bash
#!/bin/bash
# restore_database.sh

echo "=== Database Recovery ==="

# List available backups
aws s3 ls s3://yemen-market-backups/postgres/ | tail -10

read -p "Enter backup filename to restore: " BACKUP_FILE

# Download backup
aws s3 cp s3://yemen-market-backups/postgres/${BACKUP_FILE} /tmp/

# Stop API pods to prevent writes
kubectl scale deployment yemen-api -n yemen-market-prod --replicas=0
kubectl scale deployment yemen-worker -n yemen-market-prod --replicas=0

# Restore database
kubectl exec -it postgres-0 -n yemen-market-prod -- bash -c "
  dropdb yemen_market_prod
  createdb yemen_market_prod
  gunzip -c /tmp/${BACKUP_FILE} | psql yemen_market_prod
"

# Restart services
kubectl scale deployment yemen-api -n yemen-market-prod --replicas=3
kubectl scale deployment yemen-worker -n yemen-market-prod --replicas=4

echo "✅ Database recovery complete"
```

## Post-Deployment Validation

### 1. Health Check Script

```python
#!/usr/bin/env python3
# validate_deployment.py

import requests
import time
import sys
from typing import Dict, List, Tuple

class DeploymentValidator:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {"X-API-Key": api_key}
        self.checks_passed = 0
        self.checks_failed = 0
    
    def run_all_checks(self) -> bool:
        """Run all validation checks."""
        checks = [
            ("API Health", self.check_api_health),
            ("Database Connection", self.check_database),
            ("Redis Connection", self.check_redis),
            ("Authentication", self.check_auth),
            ("Hypothesis List", self.check_hypothesis_list),
            ("Create Test", self.check_create_test),
            ("SSE Stream", self.check_sse),
            ("Metrics Endpoint", self.check_metrics),
            ("Rate Limiting", self.check_rate_limiting),
            ("Error Handling", self.check_error_handling)
        ]
        
        print("=== Deployment Validation ===\n")
        
        for name, check_func in checks:
            try:
                result, message = check_func()
                if result:
                    print(f"✅ {name}: {message}")
                    self.checks_passed += 1
                else:
                    print(f"❌ {name}: {message}")
                    self.checks_failed += 1
            except Exception as e:
                print(f"❌ {name}: Exception - {str(e)}")
                self.checks_failed += 1
        
        print(f"\n=== Summary ===")
        print(f"Passed: {self.checks_passed}")
        print(f"Failed: {self.checks_failed}")
        
        return self.checks_failed == 0
    
    def check_api_health(self) -> Tuple[bool, str]:
        """Check API health endpoint."""
        response = requests.get(f"{self.base_url}/health")
        return response.status_code == 200, f"Status: {response.status_code}"
    
    def check_database(self) -> Tuple[bool, str]:
        """Check database connectivity."""
        response = requests.get(f"{self.base_url}/health/db", headers=self.headers)
        return response.status_code == 200, response.json().get("status", "unknown")
    
    def check_redis(self) -> Tuple[bool, str]:
        """Check Redis connectivity."""
        response = requests.get(f"{self.base_url}/health/redis", headers=self.headers)
        return response.status_code == 200, response.json().get("status", "unknown")
    
    def check_auth(self) -> Tuple[bool, str]:
        """Check authentication."""
        # Without auth
        response = requests.get(f"{self.base_url}/hypothesis")
        unauthorized = response.status_code == 401
        
        # With auth
        response = requests.get(f"{self.base_url}/hypothesis", headers=self.headers)
        authorized = response.status_code == 200
        
        return unauthorized and authorized, "Auth working correctly"
    
    def check_hypothesis_list(self) -> Tuple[bool, str]:
        """Check hypothesis listing."""
        response = requests.get(f"{self.base_url}/hypothesis", headers=self.headers)
        if response.status_code == 200:
            data = response.json()
            count = data.get("count", 0)
            return count == 13, f"Found {count} hypotheses"
        return False, f"Status: {response.status_code}"
    
    def check_create_test(self) -> Tuple[bool, str]:
        """Check test creation."""
        test_data = {
            "start_date": "2023-01-01",
            "end_date": "2023-01-31"
        }
        
        response = requests.post(
            f"{self.base_url}/hypothesis/H1/test",
            json=test_data,
            headers=self.headers
        )
        
        if response.status_code == 202:
            test_id = response.json().get("id")
            # Clean up
            requests.delete(
                f"{self.base_url}/hypothesis/test/{test_id}",
                headers=self.headers
            )
            return True, f"Test created: {test_id}"
        
        return False, f"Status: {response.status_code}"
    
    def check_sse(self) -> Tuple[bool, str]:
        """Check SSE streaming."""
        # Create a test first
        test_data = {
            "start_date": "2023-01-01",
            "end_date": "2023-01-31"
        }
        
        response = requests.post(
            f"{self.base_url}/hypothesis/H1/test",
            json=test_data,
            headers=self.headers
        )
        
        if response.status_code == 202:
            test_id = response.json()["id"]
            
            # Try SSE connection
            sse_url = f"{self.base_url}/hypothesis/test/{test_id}/stream"
            with requests.get(sse_url, headers=self.headers, stream=True, timeout=5) as r:
                if r.status_code == 200:
                    # Read first event
                    for line in r.iter_lines():
                        if line and line.startswith(b'data:'):
                            # Clean up
                            requests.delete(
                                f"{self.base_url}/hypothesis/test/{test_id}",
                                headers=self.headers
                            )
                            return True, "SSE streaming working"
                    
        return False, "SSE connection failed"
    
    def check_metrics(self) -> Tuple[bool, str]:
        """Check Prometheus metrics."""
        response = requests.get(f"{self.base_url}/metrics")
        if response.status_code == 200:
            has_metrics = "http_requests_total" in response.text
            return has_metrics, "Metrics exposed"
        return False, f"Status: {response.status_code}"
    
    def check_rate_limiting(self) -> Tuple[bool, str]:
        """Check rate limiting."""
        # Make rapid requests
        for _ in range(10):
            requests.get(f"{self.base_url}/hypothesis", headers=self.headers)
        
        # This should be rate limited
        response = requests.get(f"{self.base_url}/hypothesis", headers=self.headers)
        
        if response.status_code == 429:
            reset_time = response.headers.get("X-RateLimit-Reset")
            return True, f"Rate limiting active, reset: {reset_time}"
        
        return True, "Rate limiting may not be configured"
    
    def check_error_handling(self) -> Tuple[bool, str]:
        """Check error handling."""
        # Invalid hypothesis ID
        response = requests.get(
            f"{self.base_url}/hypothesis/INVALID/info",
            headers=self.headers
        )
        
        if response.status_code == 404:
            error_data = response.json()
            has_code = "code" in error_data
            has_message = "message" in error_data
            return has_code and has_message, "Proper error format"
        
        return False, "Error handling not working"

if __name__ == "__main__":
    # Configuration
    BASE_URL = "https://api.yemen-market-integration.org/v2"
    API_KEY = "test_api_key_123"
    
    validator = DeploymentValidator(BASE_URL, API_KEY)
    
    if validator.run_all_checks():
        print("\n✅ Deployment validation PASSED")
        sys.exit(0)
    else:
        print("\n❌ Deployment validation FAILED")
        sys.exit(1)
```

### 2. Load Testing

```python
# load_test.py
import asyncio
import aiohttp
import time
from typing import List, Dict
import statistics

async def make_request(session: aiohttp.ClientSession, url: str) -> Dict:
    """Make a single request and return timing info."""
    start = time.time()
    try:
        async with session.get(url) as response:
            await response.text()
            return {
                "status": response.status,
                "duration": time.time() - start,
                "error": None
            }
    except Exception as e:
        return {
            "status": 0,
            "duration": time.time() - start,
            "error": str(e)
        }

async def load_test(base_url: str, api_key: str, concurrent: int = 10, total: int = 100):
    """Run load test."""
    headers = {"X-API-Key": api_key}
    url = f"{base_url}/hypothesis"
    
    async with aiohttp.ClientSession(headers=headers) as session:
        # Warmup
        await make_request(session, url)
        
        # Run test
        start_time = time.time()
        tasks = []
        
        for i in range(total):
            if len(tasks) >= concurrent:
                # Wait for some to complete
                done, tasks = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
                tasks = list(tasks)
            
            task = asyncio.create_task(make_request(session, url))
            tasks.append(task)
        
        # Wait for all to complete
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful = [r for r in results if r["status"] == 200]
        durations = [r["duration"] for r in successful]
        
        print(f"\n=== Load Test Results ===")
        print(f"Total requests: {total}")
        print(f"Successful: {len(successful)}")
        print(f"Failed: {total - len(successful)}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Requests/sec: {total / total_time:.2f}")
        
        if durations:
            print(f"\nResponse times (successful requests):")
            print(f"Min: {min(durations)*1000:.2f}ms")
            print(f"Max: {max(durations)*1000:.2f}ms")
            print(f"Mean: {statistics.mean(durations)*1000:.2f}ms")
            print(f"Median: {statistics.median(durations)*1000:.2f}ms")
            print(f"P95: {statistics.quantiles(durations, n=20)[18]*1000:.2f}ms")
            print(f"P99: {statistics.quantiles(durations, n=100)[98]*1000:.2f}ms")

# Run load test
asyncio.run(load_test(
    "https://api.yemen-market-integration.org/v2",
    "test_api_key_123",
    concurrent=50,
    total=1000
))
```

## Rollback Procedures

### 1. Quick Rollback

```bash
#!/bin/bash
# rollback_deployment.sh

echo "=== Quick Rollback ==="

# Get current and previous deployment
CURRENT=$(kubectl get deployment yemen-api -n yemen-market-prod -o jsonpath='{.spec.template.spec.containers[0].image}')
echo "Current version: $CURRENT"

# Rollback to previous version
kubectl rollout undo deployment/yemen-api -n yemen-market-prod
kubectl rollout undo deployment/yemen-worker -n yemen-market-prod

# Wait for rollout
kubectl rollout status deployment/yemen-api -n yemen-market-prod
kubectl rollout status deployment/yemen-worker -n yemen-market-prod

# Verify
NEW=$(kubectl get deployment yemen-api -n yemen-market-prod -o jsonpath='{.spec.template.spec.containers[0].image}')
echo "Rolled back to: $NEW"

# Run validation
python validate_deployment.py
```

### 2. Database Rollback

```bash
#!/bin/bash
# rollback_database.sh

echo "=== Database Rollback ==="

# Stop services
kubectl scale deployment yemen-api -n yemen-market-prod --replicas=0
kubectl scale deployment yemen-worker -n yemen-market-prod --replicas=0

# Get migration history
kubectl exec -it postgres-0 -n yemen-market-prod -- \
  psql -U yemen_api -d yemen_market_prod -c "SELECT * FROM alembic_version;"

read -p "Enter target revision: " TARGET_REVISION

# Rollback migrations
kubectl exec -it postgres-0 -n yemen-market-prod -- \
  alembic downgrade ${TARGET_REVISION}

# Restart services
kubectl scale deployment yemen-api -n yemen-market-prod --replicas=3
kubectl scale deployment yemen-worker -n yemen-market-prod --replicas=4

echo "✅ Database rollback complete"
```

## Operational Runbooks

### 1. High CPU Usage

```markdown
## Runbook: High CPU Usage

### Symptoms
- CPU usage > 80% for more than 5 minutes
- API response times increasing
- Pod restarts

### Investigation
1. Check top CPU consumers:
   ```bash
   kubectl top pods -n yemen-market-prod --sort-by=cpu
   ```

2. Check for expensive queries:
   ```bash
   kubectl exec -it postgres-0 -n yemen-market-prod -- \
     psql -U yemen_api -d yemen_market_prod -c \
     "SELECT query, calls, mean_exec_time FROM pg_stat_statements 
      ORDER BY mean_exec_time DESC LIMIT 10;"
   ```

3. Check for runaway hypothesis tests:
   ```bash
   kubectl logs -n yemen-market-prod -l app=yemen-worker --tail=100 | grep ERROR
   ```

### Resolution
1. Scale up if legitimate load:
   ```bash
   kubectl scale deployment yemen-api -n yemen-market-prod --replicas=5
   ```

2. Kill expensive queries:
   ```bash
   kubectl exec -it postgres-0 -n yemen-market-prod -- \
     psql -U yemen_api -d yemen_market_prod -c \
     "SELECT pg_terminate_backend(pid) FROM pg_stat_activity 
      WHERE state = 'active' AND query_start < now() - interval '10 minutes';"
   ```

3. Restart problematic pods:
   ```bash
   kubectl delete pod <pod-name> -n yemen-market-prod
   ```
```

### 2. Database Connection Exhaustion

```markdown
## Runbook: Database Connection Pool Exhausted

### Symptoms
- "connection pool exhausted" errors
- API returning 500 errors
- Slow response times

### Investigation
1. Check current connections:
   ```bash
   kubectl exec -it postgres-0 -n yemen-market-prod -- \
     psql -U yemen_api -d yemen_market_prod -c \
     "SELECT count(*) FROM pg_stat_activity;"
   ```

2. Check connection sources:
   ```bash
   kubectl exec -it postgres-0 -n yemen-market-prod -- \
     psql -U yemen_api -d yemen_market_prod -c \
     "SELECT application_name, client_addr, count(*) 
      FROM pg_stat_activity 
      GROUP BY application_name, client_addr 
      ORDER BY count DESC;"
   ```

### Resolution
1. Kill idle connections:
   ```bash
   kubectl exec -it postgres-0 -n yemen-market-prod -- \
     psql -U yemen_api -d yemen_market_prod -c \
     "SELECT pg_terminate_backend(pid) 
      FROM pg_stat_activity 
      WHERE state = 'idle' 
      AND state_change < now() - interval '5 minutes';"
   ```

2. Restart API pods to reset pools:
   ```bash
   kubectl rollout restart deployment/yemen-api -n yemen-market-prod
   ```

3. Increase connection limit (temporary):
   ```sql
   ALTER SYSTEM SET max_connections = 300;
   SELECT pg_reload_conf();
   ```
```

### 3. Hypothesis Test Failures

```markdown
## Runbook: Hypothesis Test Failures

### Symptoms
- Tests stuck in "running" state
- High failure rate
- Timeout errors

### Investigation
1. Check failed tests:
   ```bash
   kubectl exec -it postgres-0 -n yemen-market-prod -- \
     psql -U yemen_api -d yemen_market_prod -c \
     "SELECT hypothesis_id, status, error_message, created_at 
      FROM hypothesis_tests 
      WHERE status = 'failed' 
      ORDER BY created_at DESC 
      LIMIT 10;"
   ```

2. Check worker logs:
   ```bash
   kubectl logs -n yemen-market-prod -l app=yemen-worker --tail=200 | grep ERROR
   ```

3. Check Celery queue:
   ```bash
   kubectl exec -it redis-0 -n yemen-market-prod -- redis-cli LLEN hypothesis_tests
   ```

### Resolution
1. Retry failed tests:
   ```sql
   UPDATE hypothesis_tests 
   SET status = 'pending', error_message = NULL 
   WHERE status = 'failed' 
   AND created_at > now() - interval '1 hour';
   ```

2. Clear stuck tasks:
   ```bash
   kubectl exec -it <worker-pod> -n yemen-market-prod -- \
     celery -A src.infrastructure.workers purge -f
   ```

3. Scale workers if backlog:
   ```bash
   kubectl scale deployment yemen-worker -n yemen-market-prod --replicas=8
   ```
```

---

*This production deployment guide ensures reliable, secure, and performant operation of the Yemen Market Integration API in production environments. Regular review and updates of these procedures are essential for maintaining system health.*