# Cointegration Test Implementations

**Target Audience**: Econometricians, Time Series Analysts  
**Module**: `yemen_market.models.three_tier.tier2_commodity`

## Overview

This document provides detailed implementation of cointegration testing procedures used in the Yemen Market Integration analysis. Cointegration tests identify long-run equilibrium relationships between non-stationary price series, crucial for understanding market integration.

## Theoretical Foundation

### Cointegration Definition

Two or more I(1) series are cointegrated if there exists a linear combination that is I(0):

$$y_t \sim I(1), x_t \sim I(1)$$
$$z_t = y_t - \beta x_t \sim I(0)$$

Where $\beta$ is the cointegrating vector.

**⚠️ CRITICAL REQUIREMENT**: Series integration order must be verified before cointegration testing.

### Unit Root Testing Protocol (MANDATORY)

**Step 1: Visual Inspection**
- Plot price series to identify trends, breaks, seasonality
- Check for obvious non-stationarity patterns

**Step 2: Augmented Dickey-Fuller Tests**
```python
from statsmodels.tsa.stattools import adfuller

def comprehensive_unit_root_tests(series: pd.Series, 
                                 max_lags: int = 12) -> dict:
    """
    Comprehensive unit root testing protocol.
    """
    results = {}
    
    # ADF test with different specifications
    for spec in ['c', 'ct', 'ctt', 'nc']:
        adf_result = adfuller(series, maxlag=max_lags, regression=spec)
        results[f'adf_{spec}'] = {
            'statistic': adf_result[0],
            'p_value': adf_result[1],
            'critical_values': adf_result[4],
            'reject_unit_root': adf_result[1] < 0.05
        }
    
    return results
```

**Step 3: Phillips-Perron Tests** (Robust to heteroskedasticity)
**Step 4: KPSS Tests** (Null of stationarity)
**Step 5: Structural Break Tests** (Zivot-Andrews for conflict settings)

**INTEGRATION ORDER DECISION RULE**:
- If ADF/PP reject unit root AND KPSS accepts stationarity → I(0)
- If ADF/PP accept unit root AND KPSS rejects stationarity → I(1)  
- If tests disagree → Use first differences and re-test
- **DO NOT PROCEED** with cointegration if series not I(1)

### Economic Interpretation

In market integration context:
- **Cointegration exists**: Markets are integrated; prices move together long-term
- **No cointegration**: Markets are segmented; prices diverge permanently
- **Cointegrating vector**: Long-run price relationship

## Engle-Granger Two-Step Method

### Implementation

```python
import numpy as np
import pandas as pd
from statsmodels.regression.linear_model import OLS
from statsmodels.tsa.stattools import adfuller
import statsmodels.api as sm

class EngleGrangerTest:
    """
    Engle-Granger two-step cointegration test.
    
    Suitable for bivariate relationships and when one variable
    is weakly exogenous (e.g., global price → local price).
    """
    
    def __init__(self, critical_values: dict = None):
        """Initialize with MacKinnon critical values."""
        if critical_values is None:
            # MacKinnon (2010) critical values for cointegration
            self.critical_values = {
                1: {  # No constant
                    50: {0.01: -3.96, 0.05: -3.37, 0.10: -3.03},
                    100: {0.01: -3.98, 0.05: -3.37, 0.10: -3.03},
                    250: {0.01: -3.97, 0.05: -3.37, 0.10: -3.02}
                },
                2: {  # With constant
                    50: {0.01: -4.32, 0.05: -3.77, 0.10: -3.45},
                    100: {0.01: -4.07, 0.05: -3.57, 0.10: -3.28},
                    250: {0.01: -3.96, 0.05: -3.49, 0.10: -3.21}
                }
            }
    
    def test(
        self,
        y: np.ndarray,
        x: np.ndarray,
        trend: str = 'c',
        maxlag: int = None
    ) -> dict:
        """
        Perform Engle-Granger test.
        
        Parameters
        ----------
        y : array-like
            Dependent variable (e.g., local price)
        x : array-like  
            Independent variable (e.g., global price)
        trend : str
            'n' for no constant, 'c' for constant, 'ct' for constant + trend
        maxlag : int
            Maximum lags for ADF test on residuals
        """
        # Step 1: Check both series are I(1)
        y_adf = adfuller(y, regression=trend, maxlag=maxlag)
        x_adf = adfuller(x, regression=trend, maxlag=maxlag)
        
        if y_adf[1] < 0.05 or x_adf[1] < 0.05:
            import warnings
            warnings.warn(
                "One or both series may be stationary. "
                "Cointegration test may not be appropriate."
            )
        
        # Step 2: Estimate cointegrating regression
        if trend == 'n':
            X = x.reshape(-1, 1)
        elif trend == 'c':
            X = sm.add_constant(x)
        elif trend == 'ct':
            X = sm.add_constant(x)
            time_trend = np.arange(len(x))
            X = np.column_stack([X, time_trend])
        
        model = OLS(y, X)
        results = model.fit()
        
        # Extract parameters
        if trend == 'n':
            beta = results.params[0]
            alpha = 0
        else:
            alpha = results.params[0]
            beta = results.params[1]
        
        residuals = results.resid
        
        # Step 3: Test residuals for stationarity
        # No constant in ADF for residuals
        adf_resid = adfuller(residuals, regression='n', maxlag=maxlag)
        
        # Get appropriate critical values
        n = len(y)
        trend_type = 1 if trend == 'n' else 2
        
        # Find closest sample size
        sample_sizes = [50, 100, 250]
        closest_n = min(sample_sizes, key=lambda x: abs(x - n))
        
        crit_vals = self.critical_values[trend_type][closest_n]
        
        # Interpolate for exact sample size (MacKinnon approximation)
        if n < 50:
            adjustment = (50 - n) * 0.01
            crit_vals = {k: v - adjustment for k, v in crit_vals.items()}
        
        return {
            'test_stat': adf_resid[0],
            'p_value': adf_resid[1],  # Note: Standard p-values not valid
            'critical_values': crit_vals,
            'cointegrated': adf_resid[0] < crit_vals[0.05],
            'cointegrating_vector': [1, -beta],  # y - β*x
            'alpha': alpha,
            'beta': beta,
            'residuals': residuals,
            'residual_variance': np.var(residuals),
            'r_squared': results.rsquared,
            'durbin_watson': sm.stats.stattools.durbin_watson(residuals)
        }
    
    def error_correction_model(
        self,
        y: np.ndarray,
        x: np.ndarray,
        residuals: np.ndarray,
        lags: int = 1
    ) -> dict:
        """
        Estimate Error Correction Model (ECM) after cointegration.
        
        Δy_t = γ + ρ*ECT_{t-1} + Σα_i*Δy_{t-i} + Σβ_i*Δx_{t-i} + ε_t
        """
        # Create differences
        dy = np.diff(y)
        dx = np.diff(x)
        
        # Error correction term (lagged residuals)
        ect = residuals[:-1]
        
        # Build design matrix
        X_ecm = [ect]
        
        # Add lagged differences
        for lag in range(1, lags + 1):
            if lag < len(dy):
                X_ecm.append(dy[:-lag] if lag > 0 else dy)
                X_ecm.append(dx[:-lag] if lag > 0 else dx)
        
        # Align all series
        min_len = min(len(series) for series in X_ecm)
        X_ecm = [series[-min_len:] for series in X_ecm]
        dy_aligned = dy[-min_len:]
        
        # Stack and add constant
        X_ecm = np.column_stack(X_ecm)
        X_ecm = sm.add_constant(X_ecm)
        
        # Estimate ECM
        ecm_model = OLS(dy_aligned, X_ecm)
        ecm_results = ecm_model.fit()
        
        # Extract adjustment speed
        adjustment_speed = ecm_results.params[1]  # Coefficient on ECT
        
        # Half-life calculation
        if -1 < adjustment_speed < 0:
            half_life = np.log(0.5) / np.log(1 + adjustment_speed)
        else:
            half_life = np.nan
        
        return {
            'adjustment_speed': adjustment_speed,
            'half_life': half_life,
            'ecm_results': ecm_results,
            'significant_adjustment': ecm_results.pvalues[1] < 0.05
        }
```

### Phillips-Ouliaris Test

Alternative to Engle-Granger with better small sample properties:

```python
def phillips_ouliaris_test(
    y: np.ndarray,
    x: np.ndarray,
    trend: str = 'c',
    kernel: str = 'bartlett',
    bandwidth: int = None
) -> dict:
    """
    Phillips-Ouliaris cointegration test.
    
    Uses different test statistics than Engle-Granger.
    """
    # Cointegrating regression
    if trend == 'c':
        X = sm.add_constant(x)
    else:
        X = x.reshape(-1, 1)
    
    model = OLS(y, X)
    results = model.fit()
    residuals = results.resid
    
    # Calculate test statistics
    n = len(y)
    
    # Variance ratio statistic
    from statsmodels.tsa.stattools import acovf
    
    if bandwidth is None:
        bandwidth = int(n**(1/3))
    
    # Long-run variance estimation
    acov = acovf(residuals, fft=False, nlag=bandwidth)
    
    if kernel == 'bartlett':
        weights = 1 - np.arange(bandwidth + 1) / (bandwidth + 1)
    else:  # Quadratic spectral
        weights = np.ones(bandwidth + 1)
    
    long_run_var = acov[0] + 2 * np.sum(weights[1:] * acov[1:])
    
    # Short-run variance
    short_run_var = np.var(residuals)
    
    # Test statistic
    z_alpha = n * (1 - short_run_var / long_run_var)
    
    # Critical values (Phillips-Ouliaris 1990)
    critical_values = {
        'c': {0.01: 29.5, 0.05: 20.5, 0.10: 16.3},
        'n': {0.01: 35.7, 0.05: 25.8, 0.10: 20.5}
    }
    
    crit_vals = critical_values.get(trend, critical_values['c'])
    
    return {
        'test_stat': z_alpha,
        'critical_values': crit_vals,
        'cointegrated': z_alpha > crit_vals[0.05],
        'long_run_variance': long_run_var,
        'short_run_variance': short_run_var,
        'bandwidth': bandwidth
    }
```

## Johansen Cointegration Test

### Full Implementation

```python
class JohansenTest:
    """
    Johansen cointegration test for multivariate systems.
    
    Tests for the number of cointegrating relationships
    in a system of variables.
    """
    
    def __init__(self):
        """Initialize with critical values."""
        # Critical values from Johansen (1995)
        # Format: {deterministic_trend: {significance_level: values}}
        self.trace_critical_values = self._load_trace_critical_values()
        self.max_eig_critical_values = self._load_max_eig_critical_values()
    
    def test(
        self,
        data: pd.DataFrame,
        det_order: int = 0,
        k_ar_diff: int = 1
    ) -> dict:
        """
        Perform Johansen cointegration test.
        
        Parameters
        ----------
        data : DataFrame
            Multivariate time series data
        det_order : int
            -1: No deterministic terms
             0: Constant term restricted to cointegration space
             1: Unrestricted constant term
        k_ar_diff : int
            Number of lags in the VAR (differences)
        """
        from statsmodels.tsa.vector_ar.vecm import coint_johansen
        
        # Ensure data is properly formatted
        y = data.values if isinstance(data, pd.DataFrame) else data
        n_vars = y.shape[1]
        
        # Run Johansen test
        result = coint_johansen(y, det_order, k_ar_diff)
        
        # Trace test results
        trace_stats = result.lr1
        trace_crit = result.cvt
        
        # Max eigenvalue test results  
        max_eig_stats = result.lr2
        max_eig_crit = result.cvm
        
        # Determine cointegration rank
        rank_trace = self._determine_rank(trace_stats, trace_crit[:, 1])  # 5% level
        rank_max_eig = self._determine_rank(max_eig_stats, max_eig_crit[:, 1])
        
        # Extract normalized eigenvectors (cointegrating vectors)
        eigenvectors = result.evec
        eigenvalues = result.eig
        
        # Normalize by first variable
        cointegrating_vectors = eigenvectors[:, :rank_trace].copy()
        for i in range(rank_trace):
            if cointegrating_vectors[0, i] != 0:
                cointegrating_vectors[:, i] /= cointegrating_vectors[0, i]
        
        # Calculate adjustment parameters (alpha)
        # α = S01 * β * (β' * S11 * β)^(-1)
        # Where S01 and S11 are from the Johansen procedure
        
        return {
            'rank_trace': rank_trace,
            'rank_max_eig': rank_max_eig,
            'trace_stats': trace_stats,
            'trace_critical_values': trace_crit,
            'max_eig_stats': max_eig_stats,
            'max_eig_critical_values': max_eig_crit,
            'eigenvalues': eigenvalues,
            'eigenvectors': eigenvectors,
            'cointegrating_vectors': cointegrating_vectors,
            'test_summary': self._create_summary(
                trace_stats, trace_crit, max_eig_stats, max_eig_crit
            )
        }
    
    def _determine_rank(self, test_stats: np.ndarray, critical_values: np.ndarray) -> int:
        """Determine cointegration rank from test statistics."""
        rank = 0
        for i in range(len(test_stats)):
            if test_stats[i] > critical_values[i]:
                rank = i + 1
            else:
                break
        return rank
    
    def _create_summary(
        self,
        trace_stats: np.ndarray,
        trace_crit: np.ndarray,
        max_eig_stats: np.ndarray,
        max_eig_crit: np.ndarray
    ) -> pd.DataFrame:
        """Create summary table of test results."""
        n_vars = len(trace_stats)
        
        summary_data = []
        for i in range(n_vars):
            summary_data.append({
                'H0: rank': i,
                'H1: rank': f'>{i}',
                'Trace Stat': trace_stats[i],
                'Trace 5%': trace_crit[i, 1],
                'Trace Result': 'Reject' if trace_stats[i] > trace_crit[i, 1] else 'Fail to Reject',
                'Max-Eig Stat': max_eig_stats[i],
                'Max-Eig 5%': max_eig_crit[i, 1],
                'Max-Eig Result': 'Reject' if max_eig_stats[i] > max_eig_crit[i, 1] else 'Fail to Reject'
            })
        
        return pd.DataFrame(summary_data)
    
    def identify_structural_vectors(
        self,
        eigenvectors: np.ndarray,
        rank: int,
        restrictions: dict = None
    ) -> np.ndarray:
        """
        Identify structural cointegrating vectors with restrictions.
        
        Example restrictions for price analysis:
        - Homogeneity: β1 + β2 = 1 (prices sum to unity)
        - Exclusion: β3 = 0 (variable doesn't enter)
        """
        beta = eigenvectors[:, :rank].copy()
        
        if restrictions is None:
            return beta
        
        # Apply restrictions (simplified example)
        if 'homogeneity' in restrictions:
            # Normalize so coefficients sum to 1
            for i in range(rank):
                beta[:2, i] = beta[:2, i] / beta[:2, i].sum()
        
        if 'exclusion' in restrictions:
            excluded_vars = restrictions['exclusion']
            for var_idx in excluded_vars:
                beta[var_idx, :] = 0
        
        return beta
```

### VECM Estimation After Johansen Test

```python
def estimate_vecm_from_johansen(
    data: pd.DataFrame,
    johansen_result: dict,
    include_exogenous: list = None
) -> dict:
    """
    Estimate VECM using Johansen test results.
    
    VECM specification:
    Δy_t = α*β'*y_{t-1} + Σ Γ_i*Δy_{t-i} + ε_t
    """
    from statsmodels.tsa.vector_ar.vecm import VECM
    
    rank = johansen_result['rank_trace']
    
    if rank == 0:
        raise ValueError("No cointegration found. Use VAR in differences.")
    
    # Set up VECM
    model = VECM(
        endog=data,
        k_ar_diff=1,  # Can be determined by information criteria
        coint_rank=rank,
        deterministic='ci'  # Constant inside cointegration
    )
    
    # Fit model
    vecm_result = model.fit()
    
    # Extract key parameters
    alpha = vecm_result.alpha  # Adjustment parameters
    beta = vecm_result.beta    # Cointegrating vectors
    gamma = vecm_result.gamma  # Short-run parameters
    
    # Calculate persistence profiles
    persistence_profiles = calculate_persistence_profiles(
        alpha, beta, gamma, horizons=50
    )
    
    # Impulse responses
    irf = vecm_result.irf(periods=20)
    
    # Variance decomposition
    fevd = vecm_result.fevd(periods=20)
    
    return {
        'model': vecm_result,
        'alpha': alpha,
        'beta': beta,
        'gamma': gamma,
        'persistence_profiles': persistence_profiles,
        'irf': irf,
        'fevd': fevd,
        'diagnostics': {
            'serial_correlation': vecm_result.test_whiteness().pvalue,
            'normality': vecm_result.test_normality().pvalue,
            'stability': check_vecm_stability(vecm_result)
        }
    }

def calculate_persistence_profiles(
    alpha: np.ndarray,
    beta: np.ndarray,
    gamma: list,
    horizons: int = 50
) -> dict:
    """
    Calculate persistence profiles showing speed of adjustment
    to cointegrating relationships.
    """
    n_vars = alpha.shape[0]
    r = alpha.shape[1]  # Cointegration rank
    
    profiles = {}
    
    for i in range(r):
        # Initial shock to i-th cointegrating relation
        shock = np.zeros(r)
        shock[i] = 1
        
        # Calculate response over time
        response = np.zeros((horizons, n_vars))
        
        # Initial impact through alpha
        response[0] = alpha @ shock
        
        # Dynamic responses
        for h in range(1, horizons):
            # Simplified - full calculation requires companion form
            response[h] = 0.9 * response[h-1]  # Placeholder
        
        profiles[f'CI_{i+1}'] = response
    
    return profiles
```

## Bounds Testing (ARDL) Approach

### Implementation for Mixed I(0)/I(1) Variables

```python
class ARDLBoundsTest:
    """
    Pesaran, Shin & Smith (2001) bounds test for cointegration.
    
    Advantages:
    - Works with mixed I(0) and I(1) variables
    - Single equation approach
    - Small sample properties
    """
    
    def __init__(self):
        """Initialize with PSS critical values."""
        # Critical values for F-statistic
        # Format: {k: {significance: (I0_bound, I1_bound)}}
        self.f_bounds = {
            1: {  # k=1 (one regressor)
                0.10: (3.02, 3.51),
                0.05: (3.62, 4.16),
                0.01: (4.94, 5.58)
            },
            2: {  # k=2
                0.10: (2.63, 3.35),
                0.05: (3.10, 3.87),
                0.01: (4.13, 5.00)
            },
            3: {  # k=3
                0.10: (2.45, 3.28),
                0.05: (2.86, 3.78),
                0.01: (3.74, 4.85)
            }
        }
    
    def test(
        self,
        y: pd.Series,
        X: pd.DataFrame,
        max_lags: int = 4,
        criterion: str = 'aic'
    ) -> dict:
        """
        Perform ARDL bounds test.
        
        Parameters
        ----------
        y : Series
            Dependent variable
        X : DataFrame
            Independent variables
        max_lags : int
            Maximum lag order to consider
        criterion : str
            Information criterion for lag selection
        """
        # Step 1: Select optimal lags
        optimal_lags = self._select_lags(y, X, max_lags, criterion)
        
        # Step 2: Estimate unrestricted ECM
        # Δy_t = c + ρ*y_{t-1} + Σθ_i*x_{i,t-1} + Σγ_j*Δy_{t-j} + Σδ_i*Δx_{i,t} + ε_t
        
        ecm_data = self._prepare_ecm_data(y, X, optimal_lags)
        
        # Unrestricted model
        unrestricted = OLS(
            ecm_data['dy'],
            ecm_data[['const', 'y_lag1'] + ecm_data['x_lag1_vars'] + 
                    ecm_data['dy_lags'] + ecm_data['dx_vars']]
        ).fit()
        
        # Restricted model (no long-run relationship)
        restricted = OLS(
            ecm_data['dy'],
            ecm_data[['const'] + ecm_data['dy_lags'] + ecm_data['dx_vars']]
        ).fit()
        
        # Step 3: Calculate F-statistic
        rss_u = unrestricted.ssr
        rss_r = restricted.ssr
        
        k = len(X.columns)  # Number of regressors
        T = len(ecm_data['dy'])
        q = 1 + k  # Number of restrictions (y_lag1 and all x_lag1)
        
        f_stat = ((rss_r - rss_u) / q) / (rss_u / (T - unrestricted.df_model - 1))
        
        # Step 4: Compare with bounds
        if k not in self.f_bounds:
            raise ValueError(f"Critical values not available for k={k}")
        
        bounds_5pct = self.f_bounds[k][0.05]
        
        if f_stat < bounds_5pct[0]:
            conclusion = "No cointegration"
        elif f_stat > bounds_5pct[1]:
            conclusion = "Cointegration exists"
        else:
            conclusion = "Inconclusive"
        
        # Step 5: Estimate long-run coefficients if cointegrated
        if conclusion == "Cointegration exists":
            long_run_coefs = self._estimate_long_run_coefs(unrestricted, k)
        else:
            long_run_coefs = None
        
        return {
            'f_statistic': f_stat,
            'critical_values_5pct': bounds_5pct,
            'conclusion': conclusion,
            'optimal_lags': optimal_lags,
            'unrestricted_model': unrestricted,
            'long_run_coefficients': long_run_coefs
        }
    
    def _select_lags(
        self,
        y: pd.Series,
        X: pd.DataFrame,
        max_lags: int,
        criterion: str
    ) -> dict:
        """Select optimal lag structure using information criteria."""
        from itertools import product
        
        ic_values = {}
        
        # Try different lag combinations
        for p in range(1, max_lags + 1):  # Lags of dependent variable
            for q_tuple in product(range(max_lags + 1), repeat=X.shape[1]):
                # Estimate ARDL(p, q1, q2, ...)
                try:
                    ardl_result = self._estimate_ardl(y, X, p, q_tuple)
                    
                    if criterion == 'aic':
                        ic_value = ardl_result.aic
                    elif criterion == 'bic':
                        ic_value = ardl_result.bic
                    else:
                        ic_value = ardl_result.hqic
                    
                    ic_values[(p,) + q_tuple] = ic_value
                except:
                    continue
        
        # Select minimum IC
        optimal = min(ic_values, key=ic_values.get)
        
        return {
            'p': optimal[0],
            'q': optimal[1:],
            'ic_value': ic_values[optimal]
        }
```

## Panel Cointegration Tests

### Pedroni Test

```python
def pedroni_panel_cointegration_test(
    panel_data: pd.DataFrame,
    y_var: str,
    x_vars: list,
    entity_var: str = 'entity',
    time_var: str = 'date',
    trend: bool = True
) -> dict:
    """
    Pedroni (1999, 2004) panel cointegration tests.
    
    Tests for cointegration in heterogeneous panels.
    """
    entities = panel_data[entity_var].unique()
    n = len(entities)
    T = len(panel_data[time_var].unique())
    
    # Step 1: Run individual cointegrating regressions
    residuals_dict = {}
    
    for entity in entities:
        entity_data = panel_data[panel_data[entity_var] == entity]
        
        y = entity_data[y_var].values
        X = entity_data[x_vars].values
        
        if trend:
            time_trend = np.arange(len(y))
            X = np.column_stack([X, time_trend])
        
        X = sm.add_constant(X)
        
        # OLS regression
        model = OLS(y, X)
        results = model.fit()
        residuals_dict[entity] = results.resid
    
    # Step 2: Calculate panel test statistics
    
    # Panel v-statistic
    sigma_squared = {}
    for entity, resid in residuals_dict.items():
        # AR(1) regression on residuals
        resid_lag = resid[:-1]
        resid_diff = resid[1:]
        ar1 = OLS(resid_diff, resid_lag).fit()
        sigma_squared[entity] = ar1.scale
    
    # Within-dimension statistics
    panel_v = 0
    panel_rho = 0
    panel_t = 0
    panel_adf = 0
    
    for entity in entities:
        resid = residuals_dict[entity]
        sigma2 = sigma_squared[entity]
        
        # Various test statistics...
        # Simplified implementation
        panel_v += np.sum(resid[:-1]**2) / sigma2
        
    # Normalize statistics
    panel_v = panel_v / np.sqrt(n * T)
    
    # Between-dimension statistics
    group_rho = 0
    group_t = 0
    group_adf = 0
    
    # Critical values (asymptotic standard normal)
    from scipy import stats
    
    p_values = {
        'panel_v': 1 - stats.norm.cdf(panel_v),
        'panel_rho': stats.norm.cdf(panel_rho),
        'panel_t': stats.norm.cdf(panel_t),
        'panel_adf': stats.norm.cdf(panel_adf)
    }
    
    return {
        'statistics': {
            'panel_v': panel_v,
            'panel_rho': panel_rho,
            'panel_t': panel_t,
            'panel_adf': panel_adf,
            'group_rho': group_rho,
            'group_t': group_t,
            'group_adf': group_adf
        },
        'p_values': p_values,
        'n_entities': n,
        'n_periods': T,
        'conclusion': 'Cointegrated' if p_values['panel_t'] < 0.05 else 'Not cointegrated'
    }
```

## Threshold Cointegration

### Gregory-Hansen Test with Structural Breaks

```python
def gregory_hansen_test(
    y: np.ndarray,
    x: np.ndarray,
    trim: float = 0.15,
    model: str = 'regime'
) -> dict:
    """
    Gregory-Hansen (1996) cointegration test with structural breaks.
    
    Models:
    - 'level': Break in intercept only
    - 'trend': Break in intercept and trend
    - 'regime': Break in intercept and slope
    """
    n = len(y)
    trim_start = int(n * trim)
    trim_end = int(n * (1 - trim))
    
    test_stats = []
    
    # Test each possible break point
    for tau in range(trim_start, trim_end):
        # Create dummy for structural break
        D = np.zeros(n)
        D[tau:] = 1
        
        # Build design matrix based on model
        if model == 'level':
            # y_t = μ₁ + μ₂*D_t + β*x_t + e_t
            X_design = np.column_stack([
                np.ones(n), D, x
            ])
        
        elif model == 'trend':
            # y_t = μ₁ + μ₂*D_t + β₁*t + β₂*t*D_t + α*x_t + e_t
            t = np.arange(n)
            X_design = np.column_stack([
                np.ones(n), D, t, t*D, x
            ])
        
        elif model == 'regime':
            # y_t = μ₁ + μ₂*D_t + β₁*x_t + β₂*x_t*D_t + e_t
            X_design = np.column_stack([
                np.ones(n), D, x, x*D
            ])
        
        # Estimate model
        model_ols = OLS(y, X_design)
        results = model_ols.fit()
        residuals = results.resid
        
        # ADF test on residuals
        adf_stat = adfuller(residuals, regression='n', autolag='AIC')[0]
        test_stats.append((tau, adf_stat))
    
    # Find minimum test statistic
    min_stat = min(test_stats, key=lambda x: x[1])
    break_point = min_stat[0]
    adf_min = min_stat[1]
    
    # Critical values from Gregory-Hansen (1996)
    critical_values = {
        'level': {0.01: -5.13, 0.05: -4.61, 0.10: -4.34},
        'trend': {0.01: -5.45, 0.05: -4.99, 0.10: -4.72},
        'regime': {0.01: -5.47, 0.05: -4.95, 0.10: -4.68}
    }
    
    crit_vals = critical_values[model]
    
    return {
        'test_statistic': adf_min,
        'critical_values': crit_vals,
        'break_date': data.index[break_point] if hasattr(data, 'index') else break_point,
        'break_fraction': break_point / n,
        'cointegrated_with_break': adf_min < crit_vals[0.05],
        'model': model
    }
```

## See Also

- [Time Series Methods](time-series.md) - General time series analysis
- [Threshold Models](threshold-models.md) - Threshold cointegration models
- [Unit Root Tests](../statistical-tests/unit-root-tests.md) - Prerequisite testing
- [API Reference: Cointegration](../../03-api-reference/models/three_tier/tier2_commodity.md)