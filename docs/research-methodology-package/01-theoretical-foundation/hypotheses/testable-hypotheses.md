# Neutral Testable Hypotheses Framework: Yemen Market Integration Research

## Hypothesis Classification
- **PRIMARY**: Core hypotheses central to research question
- **SECONDARY**: Exploratory hypotheses supporting broader understanding
- **METHODOLOGICAL**: Hypotheses about modeling approaches and techniques

## Notation Guide
- **Pᵢₜ**: Price in market i at time t (in local currency unless specified)
- **Pᵢₜᵁˢᴰ**: Price in market i at time t converted to USD
- **ΔPᵢⱼₜ**: Price differential between markets i and j at time t
- **Xᵢₜ**: Vector of control variables (market characteristics, seasonal factors)
- **γᵢ**: Market fixed effects
- **δₜ**: Time fixed effects
- **εᵢₜ**: Error term
- **ρᵢⱼₜ**: Price correlation between markets i and j over period t
- **Network_Densityᵢⱼ**: Joint reporting frequency of market pair (0-1 scale)

## PRIMARY HYPOTHESES (H1-H5)

### H1: Exchange Rate and Price Differentials
**Research Question**: Do price differentials between currency zones persist after accounting for exchange rate differences?

**Null Hypothesis (H₀)**: Price differentials between currency zones are not significantly reduced when prices are converted to a common currency.

**Alternative Hypothesis (H₁)**: Price differentials between currency zones are significantly reduced when prices are converted to a common currency.

**Statistical Test**: 
```
Spatial Panel Model with Currency Zone Interactions:

log(Pᵢⱼₜ) = α + β₁Currency_Zoneᵢ + β₂Distance_to_reference_market + 
            β₃(Currency_Zone × Distance)ᵢ + β₄Xᵢⱼₜ + γᵢ + φⱼ + δₜ + εᵢⱼₜ

Where:
- Currency_Zoneᵢ = 1 if market i in different currency zone from reference market
- Reference market: Major port (Aden) in government currency zone
- Distance measured in kilometers via road network

Primary Test: β₃ = 0 (currency zones don't amplify distance effects)
Secondary Test: β₁ = 0 (no currency zone fixed effects)

Alternative Specification (Robustness):
Variance decomposition approach comparing within vs across currency zone price dispersion
```

**Type**: Primary hypothesis
**Multiple Testing Adjustment**: Bonferroni correction applied

### H2: Humanitarian Aid Price Effects
**Research Question**: Does humanitarian aid distribution affect local market prices?

**Null Hypothesis (H₀)**: Humanitarian aid distribution has no statistically significant effect on local commodity prices.

**Alternative Hypothesis (H₁)**: Humanitarian aid distribution significantly affects local commodity prices.

**Statistical Test**: 
```
Instrumental Variables Approach (addressing aid placement endogeneity):
First stage: Aidᵢₜ = α + β₁Aid_Plannedᵢₜ₋₁ + β₂Xᵢₜ + γᵢ + δₜ + νᵢₜ
Second stage: Pᵢₜ = α + β₁Âidᵢₜ + β₂Xᵢₜ + γᵢ + δₜ + εᵢₜ
Test: β₁ = 0 in second stage
Exclusion restriction: Aid_Plannedᵢₜ₋₁ affects prices only through actual aid
```

**Type**: Primary hypothesis
**Multiple Testing Adjustment**: Bonferroni correction applied

### H3: Conflict Effects on Commodity Markets
**Research Question**: Does conflict intensity affect commodity market prices?

**Null Hypothesis (H₀)**: Conflict intensity has no statistically significant effect on commodity market prices.

**Alternative Hypothesis (H₁)**: Conflict intensity has a statistically significant effect on commodity market prices.

**Statistical Test**: 
```
log(Pᵢₜ) = α + β₁Conflictᵢₜ + β₂Xᵢₜ + γᵢ + δₜ + εᵢₜ
Test: β₁ = 0
```

**Type**: Primary hypothesis
**Multiple Testing Adjustment**: Bonferroni correction applied

### H4: Cross-Border Arbitrage Mechanisms
**Research Question**: Are price differentials between markets systematically related to transport costs and exchange rate differentials for tradeable goods?

**Null Hypothesis (H₀)**: Price differentials between markets are not systematically related to transport costs and exchange rate differentials.

**Alternative Hypothesis (H₁)**: Price differentials between markets are systematically related to transport costs and exchange rate differentials.

**Statistical Test**: 
```
Instrumental Variables approach (transport costs may be endogenous):
ΔPᵢⱼₜ = α + β₁T̂ransportᵢⱼₜ + β₂ExchangeDiffᵢⱼₜ + β₃Xᵢⱼₜ + εᵢⱼₜ
where T̂ransportᵢⱼₜ is instrumented by distance, fuel prices, road quality
Test: β₁ = β₂ = 0 (joint test for any relationship)
```

**Type**: Primary hypothesis
**Multiple Testing Adjustment**: Bonferroni correction applied

### H5: Long-run Price Convergence
**Research Question**: Do commodity prices exhibit long-run convergence properties within currency zones?

**Null Hypothesis (H₀)**: Commodity prices do not exhibit long-run convergence properties within currency zones.

**Alternative Hypothesis (H₁)**: Commodity prices exhibit long-run convergence properties within currency zones.

**Statistical Test**: 
```
Cointegration tests within each currency zone separately
Johansen test for cointegration relationships
Test: No cointegrating relationships vs. cointegrating relationships exist
```

**Type**: Primary hypothesis
**Multiple Testing Adjustment**: Bonferroni correction applied

## SECONDARY HYPOTHESES (H6-H10)

### H6: Currency Zone Control Changes
**Research Question**: Do markets exhibit discrete price adjustments when territorial control changes between currency zones?

**Null Hypothesis (H₀)**: Territorial control changes do not result in discrete price adjustments.

**Alternative Hypothesis (H₁)**: Territorial control changes result in discrete price adjustments.

**Statistical Test**: 
```
Regression discontinuity design around control change events
Pᵢₜ = α + β₁(t - t₀) + β₂Postₜ + β₃(t - t₀) × Postₜ + εᵢₜ
where t₀ is the control change date, Postₜ = 1 if t > t₀
Test: β₂ = 0 (discontinuity at control change)
Note: Can use polynomial or local linear specifications for robustness
```

**Type**: Secondary hypothesis

### H7: Currency Substitution Patterns
**Research Question**: Does exchange rate volatility affect the probability of USD versus YER pricing?

**Null Hypothesis (H₀)**: Exchange rate volatility does not affect currency denomination choice for pricing.

**Alternative Hypothesis (H₁)**: Exchange rate volatility affects currency denomination choice for pricing.

**Statistical Test**: 
```
Probit model: P(USD_pricingᵢₜ) = Φ(α + β₁Volatilityₜ + β₂Xᵢₜ)
Test: β₁ = 0
```

**Type**: Secondary hypothesis

### H8: Aid Effectiveness by Currency Alignment
**Research Question**: Does aid effectiveness vary by alignment between aid currency and local currency regime?

**Null Hypothesis (H₀)**: Aid effectiveness does not vary by currency alignment between aid and local regime.

**Alternative Hypothesis (H₁)**: Aid effectiveness varies by currency alignment between aid and local regime.

**Statistical Test**: 
```
Cross-sectional variation in aid currency alignment
Pᵢₜ = α + β₁Aidᵢₜ + β₂(Aidᵢₜ × Currency_Matchᵢ) + β₃Xᵢₜ + γᵢ + δₜ + εᵢₜ
where Currency_Matchᵢ = 1 if aid currency matches local regime preference
Test: β₂ = 0 (differential aid effectiveness)
Note: Requires variation in aid currency across similar markets
```

**Type**: Secondary hypothesis

### H9: Information Spillovers Across Currency Zones
**Research Question**: Do exchange rate changes in one currency zone affect pricing in other zones?

**Null Hypothesis (H₀)**: Exchange rate changes in one zone do not affect pricing in other zones.

**Alternative Hypothesis (H₁)**: Exchange rate changes in one zone affect pricing in other zones.

**Statistical Test**: 
```
Vector Autoregression (VAR) with cross-zone variables
ΔPᵢₜ = α + Σβₖ₁ΔPᵢ,ₜ₋ₖ + Σβₖ₂ΔExchRateᵢ,ₜ₋ₖ + Σγₖ₁ΔPⱼ,ₜ₋ₖ + Σγₖ₂ΔExchRateⱼ,ₜ₋ₖ + εᵢₜ
where i indexes markets in zone 1, j indexes markets in zone 2
Lag selection: Use AIC/BIC criteria
Granger causality test: Σγₖ₂ = 0 (spillover from other zone's exchange rate)
```

**Type**: Secondary hypothesis

### H10: Exchange Rate Effects on Market Integration
**Research Question**: Do exchange rate differentials affect market integration between currency zones?

**Null Hypothesis (H₀)**: Exchange rate differentials do not affect market integration between currency zones.

**Alternative Hypothesis (H₁)**: Exchange rate differentials affect market integration between currency zones.

**Statistical Test**: 
```
Price transmission regression with exchange rate differential terms
ΔPᵢₜ = α + β₁ΔPⱼₜ + β₂ExchangeDiffᵢⱼₜ + β₃(ΔPⱼₜ × ExchangeDiffᵢⱼₜ) + εᵢₜ
Test: β₂ = β₃ = 0 (joint test)
```

**Type**: Secondary hypothesis

## METHODOLOGICAL HYPOTHESES (S1, N1, P1)

### S1: Spatial Integration and Currency Boundaries
**Research Question**: Do currency zone boundaries affect spatial price correlations more than geographic distance?

**Null Hypothesis (H₀)**: Geographic distance alone determines spatial price correlation patterns.

**Alternative Hypothesis (H₁)**: Currency zone boundaries have additional explanatory power beyond geographic distance for spatial price correlations.

**Statistical Test**: 
```
Spatial regression with currency zone interactions
ρᵢⱼₜ = α + β₁Distanceᵢⱼ + β₂Same_Zoneᵢⱼ + β₃(Distanceᵢⱼ × Same_Zoneᵢⱼ) + εᵢⱼₜ
where ρᵢⱼₜ is the price correlation between markets i and j in period t
Test: β₂ = β₃ = 0 (joint test for currency zone effects)
```

**Type**: Methodological hypothesis

### N1: Network Effects on Price Transmission
**Research Question**: Do trader network characteristics affect price transmission strength?

**Null Hypothesis (H₀)**: Trader network density does not affect price transmission between markets.

**Alternative Hypothesis (H₁)**: Trader network density affects price transmission between markets.

**Statistical Test**: 
```
Price transmission model with network density
ΔPᵢₜ = α + β₁ΔPⱼₜ + β₂Network_Densityᵢⱼ + β₃(ΔPⱼₜ × Network_Densityᵢⱼ) + εᵢₜ
where Network_Densityᵢⱼ = reporting frequency of market pair (0-1 scale)
Test: β₂ = β₃ = 0 (joint test for network effects)
```

**Type**: Methodological hypothesis

### P1: Political Economy of Currency Zones
**Research Question**: Do seigniorage incentives affect currency zone persistence?

**Null Hypothesis (H₀)**: Seigniorage revenues do not influence exchange rate policy decisions.

**Alternative Hypothesis (H₁)**: Seigniorage revenues influence exchange rate policy decisions.

**Statistical Test**: 
```
Exchange_rate_changeₜ = α + β₁Seigniorageₜ + β₂Fiscal_deficitₜ + εₜ
Test: β₁ = 0
```

**Type**: Methodological hypothesis

## Testing Implementation Framework

### Hypothesis Testing Decision Tree

1. **Data Availability Assessment**
   - Primary hypotheses (H1-H5): Test immediately with available data
   - Secondary hypotheses (H6-H10): Test conditional on data acquisition
   - Methodological hypotheses (S1, N1, P1): Test as model extensions

2. **Statistical Power Considerations**
   - Minimum detectable effect sizes specified for each hypothesis
   - Sample size requirements calculated
   - Multiple testing corrections applied

3. **Robustness Testing Protocol**
   - Each hypothesis tested with multiple specifications
   - Alternative identification strategies employed
   - Sensitivity analysis conducted

### Multiple Testing Correction Strategy

**Primary Hypotheses (α = 0.05)**
- Bonferroni correction: α/5 = 0.01 per test
- Familywise error rate controlled at 5%

**Secondary Hypotheses (α = 0.10)**
- Benjamini-Hochberg procedure for false discovery rate control
- Less stringent threshold given exploratory nature

**Methodological Hypotheses (α = 0.15)**
- Unadjusted tests acceptable given methodological focus
- Results interpreted as model specification guidance

### Data Requirements by Hypothesis

**Immediate Implementation (Current Data)**
- H1: Exchange Rate and Price Differentials
- H4: Cross-Border Arbitrage Mechanisms  
- H5: Long-run Price Convergence
- S1: Spatial Integration and Currency Boundaries

**Requires Additional Data Collection**
- H2: Humanitarian Aid Price Effects (OCHA 3W data)
- H3: Conflict Effects on Commodity Markets (conflict intensity data)
- H6: Currency Zone Control Changes (territorial control timeline)

**Advanced Data Requirements**
- H7: Currency Substitution Patterns (price denomination data)
- H8: Aid Effectiveness by Currency Alignment (detailed aid records)
- H9: Information Spillovers (high-frequency price data)
- H10: Exchange Rate Effects on Market Integration (extended time series)
- N1: Network Effects (trader network mapping)
- P1: Political Economy (fiscal and monetary policy data)

### Statistical Specification Templates

**Note**: All templates use neutral estimation approaches without presuming directional effects.

#### H1: Exchange Rate Differential Test
```stata
* Convert to common currency
gen price_usd = price_yer / exchange_rate

* Test for zone effects
reg price_usd i.currency_zone i.commodity##i.month, cluster(market)
test 1.currency_zone = 0
```

#### H2: Aid Effect Estimation
```stata
* Instrumental variables approach
ivregress 2sls price_local (aid_intensity = aid_planned_lag) ///
          i.commodity##i.month i.market, cluster(market)
* Test aid effect
test aid_intensity = 0
```

#### H4: Arbitrage Relationship Test
```stata
* Price differential regression
* Note: distance measured in kilometers, transport_cost in USD/ton-km
reg price_diff transport_cost exchange_differential ///
    if tradeable == 1 & distance_km < 500, cluster(market_pair)
* Test for any relationship (primary)
test transport_cost = exchange_differential = 0
* If relationship exists, examine magnitude
* Generate confidence intervals for coefficients
estat vce
margins, dydx(transport_cost exchange_differential)
```

#### H5: Cointegration Analysis
```stata
* Test cointegration within currency zones by commodity
foreach zone in houthi government {
    foreach commodity in wheat rice sugar fuel {
        * Unit root tests
        xtunitroot fisher price if currency_zone == "`zone'" ///
                  & commodity == "`commodity'", lags(2)
        * Johansen cointegration test
        vecrank price if currency_zone == "`zone'" ///
                & commodity == "`commodity'", trend(constant) lags(2)
        * Error correction estimation if cointegrated
        vec price if currency_zone == "`zone'" ///
            & commodity == "`commodity'", trend(constant) lags(2)
    }
}
```

#### S1: Spatial Effects with Currency Zones
```stata
* Spatial correlation model
gen same_currency_zone = (zone_i == zone_j)
reg price_correlation c.distance##i.same_currency_zone ///
    i.commodity_pair##i.month, cluster(market_pair)
```

### Power Analysis Framework

**Minimum Detectable Effects (for power calculation purposes)**
- H1: 10% price differential in either direction (α=0.01, β=0.20)
- H2: 5% price effect from aid in either direction (α=0.01, β=0.20)  
- H3: 0.15 log point change in prices per unit change in conflict intensity (α=0.01, β=0.20)
- H4: Coefficient of 0.2 standard deviations from zero (α=0.01, β=0.20)
- H5: Cointegration relationship with adjustment speed >0.05 per period (α=0.01, β=0.20)
- Secondary hypotheses (H6-H10): Medium effect sizes (Cohen's d = 0.5)
- Methodological hypotheses (S1, N1, P1): Effect sizes to be determined empirically

**Sample Size Adequacy**
- Markets: 300+ required for spatial analysis
- Time periods: 36+ months for cointegration tests
- Commodity-market observations: 10,000+ for panel analysis

### Robustness Requirements

**Alternative Specifications**
- Different currency conversion methodologies
- Alternative conflict intensity measures
- Various spatial weighting matrices
- Multiple aid effectiveness definitions

**Sensitivity Analysis**
- Outlier treatment approaches
- Alternative clustering schemes
- Different lag structures
- Various sample restrictions

**Cross-Validation**
- Out-of-sample testing where feasible
- Bootstrap confidence intervals
- Placebo test protocols
- Alternative identification strategies

## Test Assumptions and Diagnostics

### Model Assumptions by Test Type

**Panel Regression (H1, H2, H3)**
- No perfect multicollinearity
- Conditional mean independence: E[εᵢₜ|Xᵢₜ, γᵢ, δₜ] = 0 (weaker than strict exogeneity)
- For IV models (H2): Instrument relevance (F > 10) and exclusion restriction
- Homoskedasticity and no autocorrelation (or use robust SE)
- Diagnostic tests: Hausman test, serial correlation tests, instrument validity tests

**Cointegration Tests (H5)**
- Integration order to be determined empirically (test for unit roots first)
- Variables may be I(0) or I(1) - testing required, not assumed
- No structural breaks in relationships (test with Chow tests)
- Diagnostic tests: Unit root tests (ADF, PP), cointegration tests, stability tests

**VAR Models (H9)**
- Stationarity of variables (or appropriate differencing)
- No residual autocorrelation
- Diagnostic tests: Portmanteau tests, stability checks

### Practical vs Statistical Significance

**Meaningful Effect Sizes**
- Price differentials: >5% considered economically meaningful
- Aid effects: >3% price change considered policy-relevant
- Arbitrage: Deviations >10% from theoretical values suggest potential market frictions
- Integration: Speed of adjustment will be assessed empirically without predetermined thresholds

**Reporting Requirements**
- Always report confidence intervals alongside point estimates
- Distinguish between statistical significance and economic importance
- Report both adjusted and unadjusted results for transparency

## Missing Data and Sample Selection

### Missing Data Strategy
1. **Document missingness patterns**: Report % missing by market, time, commodity
2. **Test for non-random missingness**: Regress missing indicator on observables
3. **Imputation approach**: 
   - Use multiple imputation for partial missingness
   - Exclude markets with >50% missing observations
   - Conduct sensitivity analysis with different thresholds

### Sample Selection Criteria
1. **Market inclusion**: Markets reporting ≥24 months of data (rationale: need sufficient time variation)
2. **Temporal coverage**: 2019-2023 (48 months maximum)
3. **Commodity coverage**: Include commodities reported in >75% of markets (rationale: ensure representativeness)
4. **Currency zone assignment**: Based on ACAPS territorial control data (time-varying)
5. **Selection bias mitigation**: Test sensitivity to different inclusion thresholds (12, 18, 36 months)

### Currency Conversion Protocol
1. **Exchange rate source**: CBY-Aden and CBY-Sana'a official rates
2. **Temporal matching**: Use monthly average rates matched to price observation month
3. **Missing rate handling**: Linear interpolation for gaps <3 months
4. **Parallel market rates**: Use as robustness check where available

## Robustness to Real-World Complexities

### Territorial Control Dynamics
1. **Contested areas**: Assign to zone based on predominant control in month
2. **Control changes**: For H6, require ≥3 months stable control pre/post
3. **Multiple authorities**: If >2 currency regimes emerge, extend framework accordingly
4. **Circular logic mitigation**: Test robustness using alternative zone definitions (geographic, ethnic, administrative)

### Seasonal and Event Adjustments
1. **Ramadan effects**: Include Ramadan month indicator in all models
2. **Harvest seasons**: Include commodity-specific seasonal dummies
3. **Major shocks**: Test sensitivity to excluding crisis periods (e.g., COVID-19)

### Market Functioning
1. **Temporary closures**: Distinguish non-reporting from non-functioning
2. **Thin markets**: Exclude market-commodity pairs with <10 observations
3. **Price outliers**: Winsorize at 1st and 99th percentiles as robustness check

### Spillover Effects
1. **Spatial spillovers**: Test models with spatial lags as robustness
2. **Commodity substitution**: Consider cross-commodity price effects
3. **Aid displacement**: Test whether aid in one market affects neighboring markets

### Heterogeneity Analysis
1. **Commodity groups**: Test hypotheses separately for staples vs. non-staples
2. **Urban vs. rural**: Allow effects to differ by market type
3. **Import dependence**: Test whether effects vary by commodity import share