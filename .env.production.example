# Production Environment Configuration
# Copy this file to .env.production and fill in the values

# Database Configuration
DB_PASSWORD=your-secure-database-password
DATABASE_URL=postgresql://yemen_user:${DB_PASSWORD}@postgres:5432/yemen_market

# Redis Configuration
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/1
CELERY_RESULT_BACKEND=redis://redis:6379/2

# Security
JWT_SECRET_KEY=your-very-long-random-secret-key-here
API_KEY_SECRET=another-very-long-random-secret-key
ALLOWED_HOSTS=api.yemen-market.org,localhost
CORS_ORIGINS=https://app.yemen-market.org,https://dashboard.yemen-market.org

# External Services
WFP_API_KEY=your-wfp-api-key
ACLED_API_KEY=your-acled-api-key
ACLED_EMAIL=<EMAIL>
HDX_API_KEY=your-hdx-api-key

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
GRAFANA_USER=admin
GRAFANA_PASSWORD=secure-grafana-password
FLOWER_USER=admin
FLOWER_PASSWORD=secure-flower-password

# Performance
WORKERS=4
WORKER_CONNECTIONS=1000
MAX_REQUESTS=1000
CACHE_TTL=3600
HYPOTHESIS_TEST_TIMEOUT=300

# Storage
HYPOTHESIS_RESULTS_PATH=/app/data/hypothesis_results
POLICY_RESULTS_PATH=/app/data/policy_results

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Feature Flags
ENABLE_REAL_TIME_MONITORING=true
ENABLE_BATCH_TESTING=true
ENABLE_SSE_STREAMING=true
ENABLE_CACHE_WARMING=true

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Email Notifications (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=smtp-password
NOTIFICATION_EMAIL=<EMAIL>

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=yemen-market-backups
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# SSL/TLS
SSL_CERT_PATH=/etc/nginx/ssl/fullchain.pem
SSL_KEY_PATH=/etc/nginx/ssl/privkey.pem

# Resource Limits
MAX_CONCURRENT_TESTS=10
MAX_BATCH_SIZE=50
MEMORY_LIMIT_MB=4096
CPU_LIMIT=4.0