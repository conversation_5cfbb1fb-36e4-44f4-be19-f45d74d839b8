# Prompt for Implementing Next Hypothesis Tests (H3, H4, H6-H8, H10, S1, N1, P1)

## Context: Yemen Market Integration Project

You are implementing hypothesis tests for a groundbreaking econometric research project that discovered **currency fragmentation, not conflict, explains the Yemen Paradox** (why prices appear lower in conflict zones). The project has **World Bank publication standards** and will improve humanitarian aid effectiveness by 25-40%.

### Current Status
- **6 of 13 hypotheses implemented**: H1 (exchange rate), H2 (aid distribution), H5 (arbitrage), H9 (thresholds)
- **7 hypotheses need implementation**: H3, H4, H6, H7, H8, H10, S1, N1, P1
- All implementations must maintain the highest econometric rigor

## Essential Context to Build

### 1. Review Completed Hypothesis Tests
Start by examining the implemented tests to understand the pattern:

```bash
# Review the hypothesis framework
cat src/core/models/hypothesis_testing/hypothesis_framework.py

# Study completed implementations
cat src/core/models/hypothesis_testing/h1_exchange_rate.py  # Core discovery test
cat src/core/models/hypothesis_testing/h2_aid_distribution.py  # Latest implementation
cat src/core/models/hypothesis_testing/h5_cross_border.py  # Arbitrage test
cat src/core/models/hypothesis_testing/h9_threshold_effects.py  # Non-linear effects
```

Key patterns to observe:
- Each test inherits from `HypothesisTest` base class
- Must implement `prepare_data()`, `run_test()`, and `interpret_results()`
- Uses `@dataclass` for specialized data and results structures
- Returns `HypothesisOutcome` (SUPPORTED, PARTIAL, REJECTED)
- Includes comprehensive policy interpretation

### 2. Review Methodology Documentation

```bash
# Core hypothesis descriptions
cat docs/research-methodology-package/01-theoretical-foundation/hypotheses/testable-hypotheses.md

# Implementation requirements
cat docs/research-methodology-package/10-context-for-implementation/CRITICAL_IMPLEMENTATION_CHECKLIST.md

# Methodology mapping
cat docs/research-methodology-package/10-context-for-implementation/METHODOLOGY_TO_CODE_MAPPING.md
```

### 3. Understand the Architecture

```bash
# Project overview
cat CLAUDE.md  # Project-specific instructions
cat COMPREHENSIVE_METHODOLOGY_ALIGNMENT_PLAN.md  # Full implementation plan

# Check current structure
tree src/core/models/hypothesis_testing -L 2
tree tests/hypothesis_tests -L 1
```

## Implementation Guidelines for Each Hypothesis

### H3: Demand Destruction vs Supply Shock

**Hypothesis**: Conflict reduces purchasing power more than supply, evidenced by:
- Larger price effects for non-essential goods
- Persistence after conflict ends
- Correlation with displacement intensity

**Implementation Requirements**:
```python
class H3DemandDestructionTest(HypothesisTest):
    """
    Expected findings:
    - Demand elasticity > supply elasticity in conflict
    - Non-essential goods show 2-3x price decline vs essentials
    - Effects persist 6+ months after conflict
    """
    
    def prepare_data(self, panel_data: pd.DataFrame) -> DemandDestructionData:
        # Extract displacement data
        # Classify goods as essential vs non-essential
        # Calculate conflict intensity measures
        # Identify conflict periods and post-conflict windows
    
    def run_test(self, data: DemandDestructionData) -> DemandDestructionResults:
        # Test 1: Elasticity comparison (demand vs supply)
        # Test 2: Essential vs non-essential price movements
        # Test 3: Persistence analysis post-conflict
        # Test 4: Displacement correlation
```

**Key Econometric Methods**:
- Instrumental variables for endogenous displacement
- Event study around conflict onset/cessation
- Elasticity estimation with classification by goods

### H4: Currency Zone Switching Effects

**Hypothesis**: Markets switching control show discrete price jumps matching new exchange regime

**Implementation Requirements**:
```python
class H4ZoneSwitchingTest(HypothesisTest):
    """
    Expected findings:
    - 20-30% discrete price jumps at control changes
    - Immediate adjustment to new currency equilibrium
    - No gradual transition period
    """
    
    def prepare_data(self, panel_data: pd.DataFrame) -> ZoneSwitchingData:
        # Identify control change events
        # Extract pre/post windows around switches
        # Map old zone → new zone transitions
    
    def run_test(self, data: ZoneSwitchingData) -> ZoneSwitchingResults:
        # Regression discontinuity around control changes
        # Test for discrete vs gradual adjustment
        # Quantify jump magnitude by zone transition type
```

**Key Econometric Methods**:
- Regression discontinuity design (RDD)
- Event study methodology
- Structural break tests

### H6: Currency Substitution Dynamics

**Hypothesis**: USD pricing probability increases with exchange rate volatility

**Implementation Requirements**:
```python
class H6CurrencySubstitutionTest(HypothesisTest):
    """
    Expected model:
    P(USD pricing) = logit(0.5 + 0.02 * exchange_diff)
    
    Higher volatility → more USD pricing
    """
    
    def run_test(self, data: CurrencySubstitutionData) -> CurrencySubstitutionResults:
        # Binary choice model for currency denomination
        # Include exchange volatility measures
        # Test threshold effects
```

**Key Econometric Methods**:
- Logit/probit models
- Rolling window volatility
- Panel binary choice models

### H7: Aid Effectiveness Differential

**Hypothesis**: Aid works better when currency-matched (YER in Houthi, USD in government areas)

**Implementation Requirements**:
```python
class H7AidEffectivenessTest(HypothesisTest):
    """
    Triple-difference specification:
    effect = β₀ + β₁*aid + β₂*currency_match + β₃*post + 
             β₄*(aid × currency_match × post)
    
    Expected: 25-40% effectiveness gain from matching
    """
```

**Key Econometric Methods**:
- Triple-difference (DDD) estimation
- Matching estimators
- Synthetic control for counterfactuals

### H8: Information Spillover

**Hypothesis**: Exchange rate information affects pricing across zones

**Implementation Requirements**:
```python
class H8InformationSpilloverTest(HypothesisTest):
    """
    VAR model with cross-zone prices and exchange rates
    Expected: 0.4-0.6 information transmission coefficient
    Lead-lag relationships in price movements
    """
```

**Key Econometric Methods**:
- Vector Autoregression (VAR)
- Granger causality tests
- Impulse response functions

### H10: Long-run Convergence

**Hypothesis**: USD prices converge to global levels; YER prices diverge

**Implementation Requirements**:
```python
class H10ConvergenceTest(HypothesisTest):
    """
    Separate ECM for each currency:
    - USD: Should show cointegration with global prices
    - YER: Should show divergence/no cointegration
    """
```

**Key Econometric Methods**:
- Error Correction Models (ECM)
- Cointegration tests (Johansen)
- Convergence club analysis

### S1: Spatial Boundaries

**Hypothesis**: Currency boundaries matter more than geographic distance

**Implementation Requirements**:
```python
class S1SpatialBoundariesTest(HypothesisTest):
    """
    Spatial regression with currency zone interactions
    Expected: Zone coefficient > distance coefficient
    """
```

### N1: Network Density

**Hypothesis**: Denser trader networks show stronger price integration

**Implementation Requirements**:
```python
class N1NetworkDensityTest(HypothesisTest):
    """
    Include network proxies (reporting frequency, hub proximity)
    Test interaction with price transmission
    """
```

### P1: Political Economy

**Hypothesis**: Seigniorage incentives prevent currency reunification

**Implementation Requirements**:
```python
class P1PoliticalEconomyTest(HypothesisTest):
    """
    Correlate exchange rate policy with:
    - Fiscal deficits
    - Seigniorage reliance
    - Political control variables
    """
```

## Required Imports and Dependencies

Each test should include:
```python
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy import stats
import statsmodels.api as sm
from linearmodels.panel import PanelOLS
from sklearn.preprocessing import StandardScaler

from .hypothesis_framework import (
    HypothesisTest, TestData, TestResults, PolicyInterpretation,
    HypothesisOutcome, TestRequirement, HypothesisRegistry
)
from src.core.utils.logging import get_logger

logger = get_logger(__name__)
```

## Quality Standards

### 1. Econometric Rigor
- All standard errors must be clustered at appropriate level
- Include robustness checks (different specifications, subsamples)
- Report confidence intervals, not just point estimates
- Handle missing data explicitly (not random in conflict)

### 2. Code Structure
```python
@dataclass
class H{N}{Name}Data(TestData):
    """Specific data structure for this hypothesis."""
    # Include all needed data components
    
@dataclass
class H{N}{Name}Results(TestResults):
    """Results structure with hypothesis-specific fields."""
    # Include effect sizes, diagnostics, sub-test results

class H{N}{Name}Test(HypothesisTest):
    def __init__(self):
        super().__init__(
            hypothesis_id="H{N}",
            name="{Full Hypothesis Name}",
            description="{Brief description of what test does}"
        )
        # Set any test-specific parameters
    
    def prepare_data(self, panel_data: pd.DataFrame) -> H{N}{Name}Data:
        """Prepare data with all needed transformations."""
        logger.info(f"Preparing data for H{N} {name} test")
        # Data preparation logic
        
    def run_test(self, data: H{N}{Name}Data) -> H{N}{Name}Results:
        """Execute the hypothesis test."""
        logger.info(f"Running H{N} {name} test")
        # Run all sub-tests
        # Calculate test statistics
        # Determine outcome (SUPPORTED/PARTIAL/REJECTED)
        
    def interpret_results(self, results: H{N}{Name}Results) -> PolicyInterpretation:
        """Generate policy-relevant interpretation."""
        # Create summary, implications, recommendations
        # Include confidence assessment
        # Add visualizations dict

# Register the test
HypothesisRegistry.register(H{N}{Name}Test())
```

### 3. Testing Requirements

For each hypothesis, create comprehensive tests:
```python
# tests/hypothesis_tests/test_h{n}_{name}.py
class TestH{N}{Name}:
    def test_initialization(self)
    def test_data_preparation(self)
    def test_run_test(self)
    def test_expected_values(self)
    def test_edge_cases(self)
    def test_policy_interpretation(self)
```

### 4. Documentation

Update after each implementation:
1. `IMPLEMENTATION_ROADMAP_VISUAL.md` - Mark hypothesis as validated
2. `COMPREHENSIVE_METHODOLOGY_ALIGNMENT_PLAN.md` - Update progress metrics
3. `src/core/models/hypothesis_testing/__init__.py` - Ensure imports included

## Priority Order

Implement in this order based on data availability and complexity:

1. **H3: Demand Destruction** (HIGH - core to understanding conflict effects)
2. **H4: Zone Switching** (HIGH - validates discrete adjustment mechanism)  
3. **H10: Long-run Convergence** (MEDIUM - important for policy)
4. **H6: Currency Substitution** (MEDIUM - explains market adaptation)
5. **H7: Aid Effectiveness** (MEDIUM - critical for humanitarian programming)
6. **H8: Information Spillover** (MEDIUM - network effects)
7. **S1: Spatial Boundaries** (LOW - extends core findings)
8. **N1: Network Density** (LOW - mechanism refinement)
9. **P1: Political Economy** (LOW - explains persistence)

## Validation Checklist

Before considering a hypothesis complete:
- [ ] Full implementation following the pattern
- [ ] All expected values match methodology document
- [ ] Comprehensive test suite with >90% coverage
- [ ] Policy interpretation generates actionable insights
- [ ] Edge cases handled (insufficient data, missing values)
- [ ] Logging at appropriate points
- [ ] Documentation updated in roadmap files

## Example Command Sequence

```bash
# 1. Create the hypothesis test file
touch src/core/models/hypothesis_testing/h3_demand_destruction.py

# 2. Implement following the pattern from H1/H2/H5/H9

# 3. Create test file
touch tests/hypothesis_tests/test_h3_demand_destruction.py

# 4. Run tests
pytest tests/hypothesis_tests/test_h3_demand_destruction.py -v

# 5. Update documentation
# Edit IMPLEMENTATION_ROADMAP_VISUAL.md
# Edit COMPREHENSIVE_METHODOLOGY_ALIGNMENT_PLAN.md

# 6. Verify integration
python -c "from src.core.models.hypothesis_testing import H3DemandDestructionTest"
```

## Remember

This research is solving a real humanitarian crisis. The Yemen Paradox discovery enables:
- 25-40% improvement in aid effectiveness
- Better targeting of $4.3 billion annual humanitarian aid
- Understanding of how currency fragmentation drives apparent price anomalies

Maintain the highest standards - this will be used by World Bank, WFP, and other organizations to save lives.