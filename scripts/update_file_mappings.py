#!/usr/bin/env python3
"""
Update file mappings, verify cross-references, and ensure consistent file naming
and linking throughout the Yemen Market Integration documentation package.

This script:
1. Scans all markdown files in the documentation
2. Updates navigation files with current structure
3. Verifies all cross-references and links
4. Detects and reports broken links
5. Ensures consistent file naming conventions
6. Generates updated index files
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, field
from collections import defaultdict
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class FileInfo:
    """Information about a documentation file."""
    path: Path
    title: str = ""
    description: str = ""
    links: List[str] = field(default_factory=list)
    headings: List[Tuple[int, str]] = field(default_factory=list)
    size: int = 0
    last_modified: float = 0


@dataclass
class LinkIssue:
    """Information about a link issue."""
    source_file: Path
    link_text: str
    link_target: str
    issue_type: str  # "broken", "relative", "external", "anchor_missing"
    line_number: int = 0


@dataclass
class CurrencyZoneReference:
    """Information about currency zone references in documentation."""
    file_path: Path
    line_number: int
    content: str
    zone_type: str  # "houthi", "government", "both", "unclear"
    has_exchange_rate_reference: bool = False
    exchange_rate_value: Optional[str] = None
    currency_mentioned: Set[str] = field(default_factory=set)  # YER, USD, etc.
    context: str = ""  # surrounding text for context


class DocumentationMapper:
    """Main class for updating documentation file mappings."""
    
    def __init__(self, docs_root: Path):
        self.docs_root = docs_root
        self.file_info: Dict[Path, FileInfo] = {}
        self.link_issues: List[LinkIssue] = []
        self.external_links: Set[str] = set()
        self.currency_zone_refs: List[CurrencyZoneReference] = []
        
    def scan_documentation(self):
        """Scan all markdown files and collect information."""
        logger.info(f"Scanning documentation in {self.docs_root}")
        
        for md_file in self.docs_root.rglob("*.md"):
            if any(part.startswith('.') for part in md_file.parts):
                continue  # Skip hidden directories
                
            self._process_file(md_file)
            
        logger.info(f"Scanned {len(self.file_info)} files")
        logger.info(f"Found {len(self.currency_zone_refs)} currency zone references")
        
    def _process_file(self, file_path: Path):
        """Process a single markdown file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            file_stat = file_path.stat()
            info = FileInfo(
                path=file_path,
                size=file_stat.st_size,
                last_modified=file_stat.st_mtime
            )
            
            # Extract title and description
            info.title = self._extract_title(content)
            info.description = self._extract_description(content)
            
            # Extract headings
            info.headings = self._extract_headings(content)
            
            # Extract links
            info.links = self._extract_links(content)
            
            # Extract currency zone references
            self._extract_currency_zone_references(file_path, content)
            
            self.file_info[file_path] = info
            
        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            
    def _extract_title(self, content: str) -> str:
        """Extract the main title from markdown content."""
        lines = content.split('\n')
        for line in lines:
            if line.strip().startswith('# '):
                return line.strip()[2:].strip()
        return ""
        
    def _extract_description(self, content: str) -> str:
        """Extract description (first paragraph after title)."""
        lines = content.split('\n')
        in_description = False
        description_lines = []
        
        for line in lines:
            if line.strip().startswith('# '):
                in_description = True
                continue
            elif in_description and line.strip() == '':
                if description_lines:
                    break
            elif in_description and line.strip():
                description_lines.append(line.strip())
                
        return ' '.join(description_lines)[:200]  # First 200 chars
        
    def _extract_headings(self, content: str) -> List[Tuple[int, str]]:
        """Extract all headings with their levels."""
        headings = []
        for line in content.split('\n'):
            match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if match:
                level = len(match.group(1))
                title = match.group(2).strip()
                headings.append((level, title))
        return headings
        
    def _extract_links(self, content: str) -> List[str]:
        """Extract all markdown links from content."""
        # Match [text](url) pattern
        link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        links = re.findall(link_pattern, content)
        return [link[1] for link in links]
        
    def _extract_currency_zone_references(self, file_path: Path, content: str):
        """Extract currency zone references and exchange rate mentions."""
        lines = content.split('\n')
        
        # Define patterns for different elements
        zone_patterns = {
            'houthi': [
                r'\bhouthi\b', r'\bansar\s*allah\b', r'\bsana[\'`\']?a\b', 
                r'\bnorthern\s+areas?\b', r'\bnorth\s+yemen\b',
                r'\bhouthi[\-\s]*controlled\b', r'\bcby[\-\s]*sana[\'`\']?a\b'
            ],
            'government': [
                r'\bgovernment\b', r'\baden\b', r'\bsouthern\s+areas?\b', 
                r'\bsouth\s+yemen\b', r'\blegitimate\s+government\b',
                r'\bgovernment[\-\s]*controlled\b', r'\bcby[\-\s]*aden\b',
                r'\brecognized\s+government\b'
            ]
        }
        
        # Exchange rate patterns
        exchange_rate_patterns = [
            r'\b\d+[\.,]?\d*\s*YER\s*/\s*USD\b',
            r'\b\d+[\.,]?\d*\s*YER\s*per\s*USD\b',
            r'\bexchange\s+rate\b', r'\bcurrency\s+conversion\b',
            r'\b535\s*YER\b', r'\b2[\.,]?000\+?\s*YER\b',
            r'\b\$\d+\s*USD\b', r'\bUSD\s*\$\d+\b'
        ]
        
        # Currency patterns
        currency_patterns = {
            'YER': r'\bYER\b|\bYemeni\s+Rial\b|\bRial\b',
            'USD': r'\bUSD\b|\bUS\s*\$\b|\bdollar\b|\bDollar\b'
        }
        
        for line_num, line in enumerate(lines, 1):
            line_lower = line.lower()
            
            # Check for zone references
            zone_type = 'unclear'
            has_houthi = any(re.search(pattern, line_lower, re.IGNORECASE) 
                           for pattern in zone_patterns['houthi'])
            has_government = any(re.search(pattern, line_lower, re.IGNORECASE) 
                               for pattern in zone_patterns['government'])
            
            if has_houthi and has_government:
                zone_type = 'both'
            elif has_houthi:
                zone_type = 'houthi'
            elif has_government:
                zone_type = 'government'
            
            # Check for exchange rate references
            has_exchange_rate = any(re.search(pattern, line_lower, re.IGNORECASE) 
                                  for pattern in exchange_rate_patterns)
            
            # Extract specific exchange rate values
            exchange_rate_value = None
            rate_match = re.search(r'\b(\d+[\.,]?\d*)\s*YER\s*/?\s*(?:per\s*)?USD\b', 
                                 line, re.IGNORECASE)
            if rate_match:
                exchange_rate_value = rate_match.group(1)
            
            # Check for currency mentions
            currencies_mentioned = set()
            for currency, pattern in currency_patterns.items():
                if re.search(pattern, line, re.IGNORECASE):
                    currencies_mentioned.add(currency)
            
            # Create reference if we found something relevant
            if (zone_type != 'unclear' or has_exchange_rate or 
                currencies_mentioned or 'currency' in line_lower or 
                'price' in line_lower):
                
                # Get context (surrounding lines)
                context_start = max(0, line_num - 2)
                context_end = min(len(lines), line_num + 1)
                context = '\n'.join(lines[context_start:context_end])
                
                ref = CurrencyZoneReference(
                    file_path=file_path,
                    line_number=line_num,
                    content=line.strip(),
                    zone_type=zone_type,
                    has_exchange_rate_reference=has_exchange_rate,
                    exchange_rate_value=exchange_rate_value,
                    currency_mentioned=currencies_mentioned,
                    context=context[:300]  # Limit context length
                )
                
                self.currency_zone_refs.append(ref)
        
    def verify_links(self):
        """Verify all links in the documentation."""
        logger.info("Verifying links...")
        
        for file_path, info in self.file_info.items():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self._verify_file_links(file_path, content)
            
        logger.info(f"Found {len(self.link_issues)} link issues")
        
    def _verify_file_links(self, source_file: Path, content: str):
        """Verify links in a single file."""
        lines = content.split('\n')
        link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        
        for line_num, line in enumerate(lines, 1):
            for match in re.finditer(link_pattern, line):
                link_text = match.group(1)
                link_target = match.group(2)
                
                self._check_link(source_file, link_text, link_target, line_num)
                
    def _check_link(self, source_file: Path, link_text: str, link_target: str, line_num: int):
        """Check if a link is valid."""
        # Skip external links
        if link_target.startswith(('http://', 'https://', 'mailto:', 'tel:')):
            self.external_links.add(link_target)
            return
            
        # Handle anchor links
        if link_target.startswith('#'):
            # TODO: Verify anchor exists in current file
            return
            
        # Parse link target
        if '#' in link_target:
            file_part, anchor_part = link_target.split('#', 1)
        else:
            file_part = link_target
            anchor_part = None
            
        # Resolve relative path
        if not file_part:
            target_file = source_file
        else:
            source_dir = source_file.parent
            target_path = source_dir / file_part
            target_file = target_path.resolve()
            
        # Check if file exists
        if not target_file.exists():
            self.link_issues.append(LinkIssue(
                source_file=source_file,
                link_text=link_text,
                link_target=link_target,
                issue_type="broken",
                line_number=line_num
            ))
        elif anchor_part:
            # TODO: Verify anchor exists in target file
            pass
            
    def update_navigation_files(self):
        """Update navigation and index files."""
        logger.info("Updating navigation files...")
        
        # Update main navigation file
        self._update_main_navigation()
        
        # Update section indexes
        self._update_section_indexes()
        
        # Update methodology index
        self._update_methodology_index()
        
    def _update_main_navigation(self):
        """Update the main NAVIGATION.md file."""
        nav_path = self.docs_root / "NAVIGATION.md"
        
        content = [
            "# Yemen Market Integration - Documentation Navigation",
            "",
            f"*Last updated: {self._get_timestamp()}*",
            "",
            "## Quick Navigation",
            "",
            "### Core Sections",
            ""
        ]
        
        # Group files by section
        sections = defaultdict(list)
        for file_path, info in self.file_info.items():
            rel_path = file_path.relative_to(self.docs_root)
            if len(rel_path.parts) > 1:
                section = rel_path.parts[0]
                sections[section].append((rel_path, info))
                
        # Sort sections
        section_order = [
            "00-overview",
            "01-theoretical-foundation",
            "02-data-infrastructure",
            "03-econometric-methodology",
            "04-external-validation",
            "05-welfare-analysis",
            "06-implementation-guides",
            "07-results-templates",
            "08-publication-materials",
            "09-policy-applications",
            "10-context-for-implementation"
        ]
        
        for section in section_order:
            if section in sections:
                content.append(f"### {self._format_section_name(section)}")
                content.append("")
                
                # Sort files in section
                section_files = sorted(sections[section], key=lambda x: x[0])
                
                for rel_path, info in section_files[:5]:  # Top 5 files
                    if info.title:
                        content.append(f"- [{info.title}]({rel_path})")
                    else:
                        content.append(f"- [{rel_path.name}]({rel_path})")
                        
                if len(section_files) > 5:
                    content.append(f"- *...and {len(section_files) - 5} more files*")
                    
                content.append("")
                
        # Add statistics
        content.extend([
            "## Documentation Statistics",
            "",
            f"- Total files: {len(self.file_info)}",
            f"- Total size: {self._format_size(sum(info.size for info in self.file_info.values()))}",
            f"- External links: {len(self.external_links)}",
            f"- Link issues: {len(self.link_issues)}",
            ""
        ])
        
        # Write navigation file
        with open(nav_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
            
        logger.info(f"Updated {nav_path}")
        
    def _update_section_indexes(self):
        """Update README.md files in each section."""
        sections = defaultdict(list)
        
        for file_path, info in self.file_info.items():
            rel_path = file_path.relative_to(self.docs_root)
            if len(rel_path.parts) > 1:
                section_dir = self.docs_root / rel_path.parts[0]
                sections[section_dir].append((rel_path, info))
                
        for section_dir, files in sections.items():
            self._update_section_readme(section_dir, files)
            
    def _update_section_readme(self, section_dir: Path, files: List[Tuple[Path, FileInfo]]):
        """Update README.md for a section."""
        readme_path = section_dir / "README.md"
        section_name = self._format_section_name(section_dir.name)
        
        content = [
            f"# {section_name}",
            "",
            f"*Last updated: {self._get_timestamp()}*",
            "",
            "## Overview",
            "",
            f"This section contains {len(files)} documents related to {section_name.lower()}.",
            "",
            "## Contents",
            ""
        ]
        
        # Group by subdirectory
        by_subdir = defaultdict(list)
        direct_files = []
        
        for rel_path, info in files:
            if len(rel_path.parts) > 2:
                subdir = rel_path.parts[1]
                by_subdir[subdir].append((rel_path, info))
            else:
                direct_files.append((rel_path, info))
                
        # Direct files first
        if direct_files:
            content.append("### Main Documents")
            content.append("")
            for rel_path, info in sorted(direct_files):
                if rel_path.name != "README.md":
                    file_link = rel_path.name
                    if info.title:
                        content.append(f"- [{info.title}]({file_link})")
                        if info.description:
                            content.append(f"  - {info.description}")
                    else:
                        content.append(f"- [{file_link}]({file_link})")
            content.append("")
            
        # Subdirectories
        for subdir in sorted(by_subdir.keys()):
            content.append(f"### {self._format_section_name(subdir)}")
            content.append("")
            
            for rel_path, info in sorted(by_subdir[subdir])[:10]:
                file_link = "/".join(rel_path.parts[1:])
                if info.title:
                    content.append(f"- [{info.title}]({file_link})")
                else:
                    content.append(f"- [{rel_path.name}]({file_link})")
                    
            if len(by_subdir[subdir]) > 10:
                content.append(f"- *...and {len(by_subdir[subdir]) - 10} more files*")
                
            content.append("")
            
        # Write README
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
            
        logger.info(f"Updated {readme_path}")
        
    def _update_methodology_index(self):
        """Update the METHODOLOGY_INDEX.md file."""
        index_path = self.docs_root / "00-overview" / "METHODOLOGY_INDEX.md"
        
        content = [
            "# Methodology Index - Complete Navigation Guide",
            "",
            f"*Generated: {self._get_timestamp()}*",
            "",
            "## Quick Links",
            "",
            "### Essential Starting Points",
            "- [Quick Start Guide](QUICK_START.md)",
            "- [Pre-Analysis Plan](PRE_ANALYSIS_PLAN.md)",
            "- [Analysis Workflow](ANALYSIS_WORKFLOW.md)",
            "- [Methodological Transparency](METHODOLOGICAL_TRANSPARENCY.md)",
            "",
            "## Complete Document Structure",
            ""
        ]
        
        # Build hierarchical structure
        tree = self._build_file_tree()
        content.extend(self._format_tree(tree))
        
        # Add cross-reference section
        content.extend([
            "",
            "## Cross-Reference Matrix",
            "",
            "### By Topic",
            ""
        ])
        
        # Group by common topics
        topics = self._extract_topics()
        for topic, files in sorted(topics.items()):
            if len(files) > 1:
                content.append(f"#### {topic}")
                content.append("")
                for file_path, info in sorted(files)[:5]:
                    rel_path = file_path.relative_to(self.docs_root)
                    content.append(f"- [{info.title or rel_path.name}](../{rel_path})")
                if len(files) > 5:
                    content.append(f"- *...and {len(files) - 5} more*")
                content.append("")
                
        # Write index
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
            
        logger.info(f"Updated {index_path}")
        
    def generate_link_report(self):
        """Generate a report of link issues."""
        if not self.link_issues:
            logger.info("No link issues found!")
            return
            
        report_path = self.docs_root.parent / "link_issues_report.md"
        
        content = [
            "# Link Issues Report",
            "",
            f"*Generated: {self._get_timestamp()}*",
            "",
            f"Found {len(self.link_issues)} link issues.",
            "",
            "## Issues by Type",
            ""
        ]
        
        # Group by issue type
        by_type = defaultdict(list)
        for issue in self.link_issues:
            by_type[issue.issue_type].append(issue)
            
        for issue_type, issues in sorted(by_type.items()):
            content.append(f"### {issue_type.replace('_', ' ').title()} ({len(issues)} issues)")
            content.append("")
            
            for issue in sorted(issues, key=lambda x: x.source_file)[:20]:
                rel_path = issue.source_file.relative_to(self.docs_root.parent)
                content.append(f"- `{rel_path}:{issue.line_number}`")
                content.append(f"  - Link: `[{issue.link_text}]({issue.link_target})`")
                
            if len(issues) > 20:
                content.append(f"- *...and {len(issues) - 20} more*")
                
            content.append("")
            
        # Write report
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
            
        logger.info(f"Generated link report: {report_path}")
        
    def generate_currency_zone_report(self):
        """Generate a comprehensive currency zone cross-reference report."""
        logger.info("Generating currency zone analysis...")
        
        report_path = self.docs_root.parent / "currency_zone_analysis_report.md"
        
        content = [
            "# Currency Zone Analysis Report",
            "",
            f"*Generated: {self._get_timestamp()}*",
            "",
            "This report identifies all references to Houthi vs Government controlled areas",
            "and tracks whether proper exchange rate conversion is mentioned.",
            "",
            f"**Total currency zone references found: {len(self.currency_zone_refs)}**",
            "",
            "## Executive Summary",
            ""
        ]
        
        # Calculate summary statistics
        zone_stats = self._calculate_zone_statistics()
        content.extend(self._format_zone_summary(zone_stats))
        
        content.extend([
            "",
            "## Cross-Reference Matrix",
            "",
            "### Files by Zone Type and Exchange Rate Coverage",
            ""
        ])
        
        # Generate cross-reference matrix
        matrix = self._generate_currency_matrix()
        content.extend(self._format_currency_matrix(matrix))
        
        content.extend([
            "",
            "## Detailed Findings",
            "",
            "### High-Risk Files (Zone References Without Exchange Rate Mention)",
            ""
        ])
        
        # Identify high-risk files
        high_risk_files = self._identify_high_risk_files()
        for file_path, issues in high_risk_files.items():
            rel_path = file_path.relative_to(self.docs_root.parent)
            content.append(f"#### {rel_path}")
            content.append("")
            for issue in issues[:3]:  # Show top 3 issues per file
                content.append(f"- Line {issue.line_number}: `{issue.content[:100]}...`")
                content.append(f"  - Zone: {issue.zone_type}, Currencies: {', '.join(issue.currency_mentioned) or 'None'}")
            if len(issues) > 3:
                content.append(f"- *...and {len(issues) - 3} more references*")
            content.append("")
            
        content.extend([
            "",
            "### Exchange Rate Values Found",
            ""
        ])
        
        # List found exchange rate values
        rate_values = self._extract_rate_values()
        for rate, locations in rate_values.items():
            content.append(f"#### {rate} YER/USD")
            content.append("")
            for file_path, line_num in locations[:5]:
                rel_path = file_path.relative_to(self.docs_root.parent)
                content.append(f"- {rel_path}:{line_num}")
            if len(locations) > 5:
                content.append(f"- *...and {len(locations) - 5} more locations*")
            content.append("")
            
        content.extend([
            "",
            "## Recommendations",
            "",
            "### Critical Actions Required",
            "",
            "1. **Add exchange rate context** to zone-specific analysis sections",
            "2. **Specify currency conversion method** when comparing prices across zones", 
            "3. **Include rate disclaimers** for time-sensitive exchange rate references",
            "4. **Standardize zone terminology** for consistency",
            "",
            "### Template Language for Currency Zone References",
            "",
            "```markdown",
            "**Northern areas (Houthi-controlled)**: Exchange rate ~535 YER/USD",
            "**Southern areas (Government-controlled)**: Exchange rate ~2,000+ YER/USD",
            "",
            "*Note: All price comparisons converted to USD using applicable regional rates*",
            "```",
            "",
            "### Files Requiring Immediate Attention",
            ""
        ])
        
        # Priority files for updates
        priority_files = self._identify_priority_files()
        for file_path, risk_score in priority_files:
            rel_path = file_path.relative_to(self.docs_root.parent)
            content.append(f"- {rel_path} (Risk Score: {risk_score})")
            
        # Write report
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
            
        logger.info(f"Generated currency zone report: {report_path}")
        
    def _calculate_zone_statistics(self) -> Dict[str, int]:
        """Calculate summary statistics for zone references."""
        stats = {
            'total_refs': len(self.currency_zone_refs),
            'houthi_refs': 0,
            'government_refs': 0,
            'both_refs': 0,
            'unclear_refs': 0,
            'with_exchange_rate': 0,
            'with_currency': 0,
            'files_with_zones': len(set(ref.file_path for ref in self.currency_zone_refs)),
            'files_with_rates': len(set(ref.file_path for ref in self.currency_zone_refs 
                                      if ref.has_exchange_rate_reference))
        }
        
        for ref in self.currency_zone_refs:
            if ref.zone_type == 'houthi':
                stats['houthi_refs'] += 1
            elif ref.zone_type == 'government':
                stats['government_refs'] += 1
            elif ref.zone_type == 'both':
                stats['both_refs'] += 1
            else:
                stats['unclear_refs'] += 1
                
            if ref.has_exchange_rate_reference:
                stats['with_exchange_rate'] += 1
            if ref.currency_mentioned:
                stats['with_currency'] += 1
                
        return stats
        
    def _format_zone_summary(self, stats: Dict[str, int]) -> List[str]:
        """Format zone statistics summary."""
        return [
            f"- **Total zone references**: {stats['total_refs']}",
            f"- **Files with zone references**: {stats['files_with_zones']}",
            f"- **Files with exchange rate mentions**: {stats['files_with_rates']}",
            "",
            "### Zone Type Distribution",
            f"- Houthi-controlled areas: {stats['houthi_refs']}",
            f"- Government-controlled areas: {stats['government_refs']}",
            f"- Both zones mentioned: {stats['both_refs']}",
            f"- Unclear zone reference: {stats['unclear_refs']}",
            "",
            "### Currency Conversion Coverage",
            f"- References with exchange rate context: {stats['with_exchange_rate']}",
            f"- References with currency mentions: {stats['with_currency']}",
            f"- **Coverage gap**: {stats['total_refs'] - stats['with_exchange_rate']} references lack exchange rate context"
        ]
        
    def _generate_currency_matrix(self) -> Dict[str, Dict[str, List[Path]]]:
        """Generate cross-reference matrix of files by zone and exchange rate coverage."""
        matrix = {
            'houthi': {'with_rate': [], 'without_rate': []},
            'government': {'with_rate': [], 'without_rate': []},
            'both': {'with_rate': [], 'without_rate': []},
            'unclear': {'with_rate': [], 'without_rate': []}
        }
        
        # Group by file and analyze coverage
        by_file = defaultdict(list)
        for ref in self.currency_zone_refs:
            by_file[ref.file_path].append(ref)
            
        for file_path, refs in by_file.items():
            # Determine dominant zone type
            zone_counts = defaultdict(int)
            has_rate = False
            
            for ref in refs:
                zone_counts[ref.zone_type] += 1
                if ref.has_exchange_rate_reference:
                    has_rate = True
                    
            dominant_zone = max(zone_counts.items(), key=lambda x: x[1])[0]
            rate_status = 'with_rate' if has_rate else 'without_rate'
            
            matrix[dominant_zone][rate_status].append(file_path)
            
        return matrix
        
    def _format_currency_matrix(self, matrix: Dict[str, Dict[str, List[Path]]]) -> List[str]:
        """Format the currency cross-reference matrix."""
        lines = [
            "| Zone Type | With Exchange Rate | Without Exchange Rate |",
            "|-----------|--------------------|-----------------------|"
        ]
        
        for zone_type in ['houthi', 'government', 'both', 'unclear']:
            with_count = len(matrix[zone_type]['with_rate'])
            without_count = len(matrix[zone_type]['without_rate'])
            
            zone_name = zone_type.replace('_', ' ').title()
            lines.append(f"| {zone_name} | {with_count} files | {without_count} files |")
            
        return lines
        
    def _identify_high_risk_files(self) -> Dict[Path, List[CurrencyZoneReference]]:
        """Identify files with zone references but no exchange rate mentions."""
        high_risk = defaultdict(list)
        
        # Group by file
        by_file = defaultdict(list)
        for ref in self.currency_zone_refs:
            by_file[ref.file_path].append(ref)
            
        for file_path, refs in by_file.items():
            # Check if file has zone references but no exchange rate mentions
            has_zone_refs = any(ref.zone_type != 'unclear' for ref in refs)
            has_rate_refs = any(ref.has_exchange_rate_reference for ref in refs)
            
            if has_zone_refs and not has_rate_refs:
                # Add zone references without rate context
                for ref in refs:
                    if ref.zone_type != 'unclear':
                        high_risk[file_path].append(ref)
                        
        return dict(high_risk)
        
    def _extract_rate_values(self) -> Dict[str, List[Tuple[Path, int]]]:
        """Extract and group found exchange rate values."""
        rate_values = defaultdict(list)
        
        for ref in self.currency_zone_refs:
            if ref.exchange_rate_value:
                rate_values[ref.exchange_rate_value].append((ref.file_path, ref.line_number))
                
        return dict(rate_values)
        
    def _identify_priority_files(self) -> List[Tuple[Path, int]]:
        """Identify files that should be prioritized for currency zone updates."""
        file_scores = defaultdict(int)
        
        # Group by file
        by_file = defaultdict(list)
        for ref in self.currency_zone_refs:
            by_file[ref.file_path].append(ref)
            
        for file_path, refs in by_file.items():
            score = 0
            
            # Score based on zone references without exchange rate context
            zone_refs_no_rate = sum(1 for ref in refs 
                                  if ref.zone_type != 'unclear' and not ref.has_exchange_rate_reference)
            score += zone_refs_no_rate * 3
            
            # Score based on currency mentions without context
            currency_no_context = sum(1 for ref in refs 
                                    if ref.currency_mentioned and not ref.has_exchange_rate_reference)
            score += currency_no_context * 2
            
            # Score based on price mentions in zone context
            price_in_zone = sum(1 for ref in refs 
                              if 'price' in ref.content.lower() and ref.zone_type != 'unclear')
            score += price_in_zone * 4
            
            if score > 0:
                file_scores[file_path] = score
                
        # Return top 10 priority files
        return sorted(file_scores.items(), key=lambda x: x[1], reverse=True)[:10]
        
    def fix_common_issues(self):
        """Automatically fix common link issues."""
        logger.info("Fixing common link issues...")
        
        fixes_applied = 0
        
        for issue in self.link_issues:
            if issue.issue_type == "broken":
                # Try to find the correct file
                possible_fix = self._find_similar_file(issue.link_target)
                if possible_fix:
                    if self._apply_link_fix(issue, possible_fix):
                        fixes_applied += 1
                        
        logger.info(f"Applied {fixes_applied} fixes")
        
    def _find_similar_file(self, broken_link: str) -> Optional[str]:
        """Find a similar file that might be the correct target."""
        # Extract filename from broken link
        if '/' in broken_link:
            filename = broken_link.split('/')[-1]
        else:
            filename = broken_link
            
        # Look for files with same name
        for file_path in self.file_info:
            if file_path.name == filename:
                return str(file_path.relative_to(self.docs_root))
                
        return None
        
    def _apply_link_fix(self, issue: LinkIssue, new_target: str) -> bool:
        """Apply a fix to a broken link."""
        try:
            with open(issue.source_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Replace the broken link
            old_link = f"[{issue.link_text}]({issue.link_target})"
            new_link = f"[{issue.link_text}]({new_target})"
            
            if old_link in content:
                content = content.replace(old_link, new_link)
                
                with open(issue.source_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                logger.info(f"Fixed link in {issue.source_file}: {issue.link_target} -> {new_target}")
                return True
                
        except Exception as e:
            logger.error(f"Error fixing link: {e}")
            
        return False
        
    def _build_file_tree(self) -> Dict:
        """Build a hierarchical tree of files."""
        tree = {}
        
        for file_path, info in self.file_info.items():
            rel_path = file_path.relative_to(self.docs_root)
            parts = rel_path.parts
            
            current = tree
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
                
            current[parts[-1]] = info
            
        return tree
        
    def _format_tree(self, tree: Dict, prefix: str = "", is_last: bool = True) -> List[str]:
        """Format file tree for display."""
        lines = []
        items = sorted(tree.items())
        
        for i, (name, value) in enumerate(items):
            is_last_item = (i == len(items) - 1)
            
            if isinstance(value, FileInfo):
                # It's a file
                connector = "└── " if is_last_item else "├── "
                lines.append(f"{prefix}{connector}{name}")
            else:
                # It's a directory
                connector = "└── " if is_last_item else "├── "
                lines.append(f"{prefix}{connector}{name}/")
                
                extension = "    " if is_last_item else "│   "
                lines.extend(self._format_tree(value, prefix + extension, is_last_item))
                
        return lines
        
    def _extract_topics(self) -> Dict[str, List[Tuple[Path, FileInfo]]]:
        """Extract common topics from file titles and content."""
        topics = defaultdict(list)
        
        # Define topic keywords
        topic_keywords = {
            "Exchange Rate": ["exchange rate", "currency", "YER", "USD"],
            "Conflict": ["conflict", "violence", "ACLED", "security"],
            "Market Integration": ["integration", "cointegration", "price transmission"],
            "Methodology": ["methodology", "econometric", "statistical", "analysis"],
            "Data": ["data", "dataset", "WFP", "HDX", "collection"],
            "Policy": ["policy", "humanitarian", "intervention", "program"],
            "Validation": ["validation", "robustness", "testing", "verification"],
            "Results": ["results", "findings", "outcomes", "impact"]
        }
        
        for file_path, info in self.file_info.items():
            file_topics = set()
            
            # Check title and description
            text_to_check = (info.title + " " + info.description).lower()
            
            for topic, keywords in topic_keywords.items():
                if any(keyword.lower() in text_to_check for keyword in keywords):
                    file_topics.add(topic)
                    
            # Add to topics
            for topic in file_topics:
                topics[topic].append((file_path, info))
                
        return dict(topics)
        
    def _format_section_name(self, name: str) -> str:
        """Format section name for display."""
        # Remove number prefix
        if '-' in name:
            name = name.split('-', 1)[1]
            
        # Convert to title case
        return name.replace('-', ' ').title()
        
    def _format_size(self, size_bytes: int) -> str:
        """Format file size for display."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024
        return f"{size_bytes:.1f} TB"
        
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """Main entry point."""
    # Determine documentation root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    docs_root = project_root / "docs" / "research-methodology-package"
    
    if not docs_root.exists():
        logger.error(f"Documentation root not found: {docs_root}")
        return 1
        
    # Create mapper instance
    mapper = DocumentationMapper(docs_root)
    
    # Run all operations
    try:
        # Scan documentation
        mapper.scan_documentation()
        
        # Verify links
        mapper.verify_links()
        
        # Update navigation files
        mapper.update_navigation_files()
        
        # Generate link report
        mapper.generate_link_report()
        
        # Generate currency zone analysis report
        mapper.generate_currency_zone_report()
        
        # Optionally fix common issues
        if input("\nAttempt to fix common link issues? (y/n): ").lower() == 'y':
            mapper.fix_common_issues()
            
        logger.info("Documentation mapping complete!")
        
        # Print summary
        print("\n" + "="*60)
        print("SUMMARY")
        print("="*60)
        print(f"Files scanned: {len(mapper.file_info)}")
        print(f"Link issues found: {len(mapper.link_issues)}")
        print(f"External links: {len(mapper.external_links)}")
        print(f"Currency zone references: {len(mapper.currency_zone_refs)}")
        
        # Currency zone summary
        zone_stats = mapper._calculate_zone_statistics()
        print(f"Files with zone references: {zone_stats['files_with_zones']}")
        print(f"Files missing exchange rate context: {zone_stats['files_with_zones'] - zone_stats['files_with_rates']}")
        
        print("\nGenerated reports:")
        print("- NAVIGATION.md")
        print("- Section README files")
        print("- METHODOLOGY_INDEX.md")
        print("- link_issues_report.md")
        print("- currency_zone_analysis_report.md")
        print("="*60)
        
        return 0
        
    except Exception as e:
        logger.error(f"Error during documentation mapping: {e}")
        return 1


if __name__ == "__main__":
    exit(main())