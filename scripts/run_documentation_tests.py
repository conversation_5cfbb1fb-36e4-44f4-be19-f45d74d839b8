#!/usr/bin/env python3
"""
Run documentation integration tests and generate a report.
"""

import sys
import subprocess
from pathlib import Path


def run_tests():
    """Run the documentation integration tests."""
    # Get project root
    project_root = Path(__file__).parent.parent
    
    # Run pytest on the integration test file
    test_file = project_root / "tests" / "test_documentation_integration.py"
    
    print("Running documentation integration tests...")
    print("=" * 60)
    
    # Run with pytest if available, otherwise run directly
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", str(test_file), "-v", "--tb=short"],
            capture_output=True,
            text=True
        )
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        return result.returncode
    except subprocess.CalledProcessError:
        # Fallback to direct execution
        print("Running tests directly (pytest not available)...")
        result = subprocess.run(
            [sys.executable, str(test_file)],
            capture_output=True,
            text=True
        )
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        return result.returncode


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)