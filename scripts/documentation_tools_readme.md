# Documentation Integration Tools

This directory contains scripts for verifying and maintaining the research methodology documentation.

## Master Verification Script

### verify_documentation_integration.py
The master script that runs all verification tools in sequence:

```bash
# Basic verification
python scripts/verify_documentation_integration.py

# Verbose output
python scripts/verify_documentation_integration.py --verbose

# Auto-fix issues where possible
python scripts/verify_documentation_integration.py --fix

# Both verbose and fix
python scripts/verify_documentation_integration.py --verbose --fix
```

### What it checks:
1. **Consistency Check** - Verifies documentation structure and standards
2. **Link Verification** - Checks all internal links are valid
3. **Integration Tests** - Runs comprehensive documentation tests
4. **Report Generation** - Creates detailed integration report

### Output:
- Console summary with pass/fail status
- Detailed JSON report: `reports/master_integration_verification.json`
- Comprehensive markdown report: `reports/documentation_integration_report.md`

## Individual Tools

If you need to run specific checks individually:

### check_documentation_consistency.py
```bash
python scripts/check_documentation_consistency.py [--verbose] [--fix]
```

### verify_documentation_links.py
```bash
python scripts/verify_documentation_links.py [--verbose]
```

### test_documentation_integration.py
```bash
python scripts/test_documentation_integration.py [--verbose]
```

### generate_integration_report.py
```bash
python scripts/generate_integration_report.py
```

## Exit Codes
- 0: All checks passed
- 1: One or more checks failed

## Integration with CI/CD
The master verification script can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions
- name: Verify Documentation Integration
  run: python scripts/verify_documentation_integration.py
```

## Troubleshooting

### Common Issues:
1. **Import errors**: Ensure you're running from project root
2. **Missing dependencies**: Install required packages with `pip install -r requirements.txt`
3. **Permission errors**: Ensure scripts have execute permissions

### Fix Mode:
The `--fix` flag will attempt to automatically fix:
- Formatting issues
- Simple consistency problems
- Directory structure issues

It will NOT fix:
- Broken links (requires manual review)
- Failing tests (requires code changes)
- Complex structural issues