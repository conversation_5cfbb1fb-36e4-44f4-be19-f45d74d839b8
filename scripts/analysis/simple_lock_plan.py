#!/usr/bin/env python3
"""
Simple Pre-Analysis Plan Locking Script

Creates cryptographic locks without heavy dependencies.
"""

import hashlib
import subprocess
from datetime import datetime
from pathlib import Path
import json


def get_git_commit_hash(project_root):
    """Get current Git commit hash"""
    try:
        result = subprocess.run(
            ["git", "rev-parse", "HEAD"],
            cwd=project_root,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError:
        return "GIT_NOT_AVAILABLE"


def calculate_file_hash(file_path):
    """Calculate MD5 hash of a file"""
    if not file_path.exists():
        return "FILE_NOT_FOUND"
    
    with open(file_path, 'rb') as f:
        content = f.read()
        return hashlib.md5(content).hexdigest()


def main():
    project_root = Path(__file__).parent.parent.parent
    
    # Core plan files
    plan_files = [
        "docs/research-methodology-package/00-overview/PRE_ANALYSIS_PLAN.md",
        "docs/research-methodology-package/03-econometric-methodology/identification-strategies/power-analysis.md",
        "docs/research-methodology-package/00-overview/ANALYSIS_WORKFLOW.md",
        "src/core/models/pre_analysis/locked_specifications.py"
    ]
    
    print("🔒 LOCKING PRE-ANALYSIS PLAN")
    print("=" * 50)
    
    # Calculate file hashes
    file_hashes = {}
    print("\nCalculating file hashes...")
    for file_path in plan_files:
        full_path = project_root / file_path
        file_hash = calculate_file_hash(full_path)
        file_hashes[file_path] = file_hash
        print(f"  {file_path}: {file_hash[:8]}...")
    
    # Get Git info
    git_hash = get_git_commit_hash(project_root)
    print(f"\nGit commit: {git_hash[:8]}...")
    
    # Generate overall plan hash
    combined_content = json.dumps(file_hashes, sort_keys=True)
    plan_hash = hashlib.sha256(combined_content.encode()).hexdigest()
    print(f"Plan hash: {plan_hash[:16]}...")
    
    # Create lock metadata
    lock_data = {
        "version": "1.0",
        "lock_timestamp": datetime.now().isoformat(),
        "git_commit_hash": git_hash,
        "plan_hash": plan_hash,
        "file_hashes": file_hashes,
        "status": "LOCKED"
    }
    
    # Update lock file
    lock_file = project_root / "docs/research-methodology-package/.pre-analysis-plan-lock"
    lock_content = f"""# Pre-Analysis Plan Lock File - LOCKED
# This file provides cryptographic proof of pre-commitment
# ANY MODIFICATION of this file invalidates the pre-commitment

VERSION=1.0
LOCK_DATE={lock_data['lock_timestamp']}
LOCK_COMMIT_HASH={git_hash}
PLAN_SHA256_HASH={plan_hash}

# Files locked:
"""
    
    for file_path, file_hash in file_hashes.items():
        lock_content += f"# - {file_path} (MD5: {file_hash})\n"
    
    lock_content += f"""
# CRYPTOGRAPHIC VERIFICATION
LOCK_INTEGRITY=VALID
LOCKED_BY=Claude-Code-Agent
LOCK_TIMESTAMP={lock_data['lock_timestamp']}

# This lock prevents:
# 1. Modification of hypothesis specifications after seeing data
# 2. Addition of new hypotheses not in the original plan  
# 3. Changes to sample criteria or variable definitions
# 4. Alterations to multiple testing correction procedures
# 5. Modifications to the analysis workflow sequence

# Full metadata (JSON):
{json.dumps(lock_data, indent=2)}
"""
    
    with open(lock_file, 'w') as f:
        f.write(lock_content)
    
    print(f"\n✅ Lock file created: {lock_file}")
    
    # Create Git tag
    try:
        tag_name = f"pre-analysis-plan-locked-{plan_hash[:8]}"
        subprocess.run(
            ["git", "tag", "-a", tag_name, "-m", "Pre-analysis plan cryptographically locked"],
            cwd=project_root,
            check=True
        )
        print(f"✅ Git tag created: {tag_name}")
    except subprocess.CalledProcessError:
        print("⚠️ Could not create Git tag (non-critical)")
    
    print("\n🎯 PRE-ANALYSIS PLAN SUCCESSFULLY LOCKED!")
    print("=" * 50)
    print(f"Lock Date: {lock_data['lock_timestamp']}")
    print(f"Git Commit: {git_hash}")
    print(f"Plan Hash: {plan_hash[:32]}...")
    print(f"Status: LOCKED ✅")
    
    print("\n📋 NEXT STEPS:")
    print("1. Register with AEA RCT Registry within 48 hours")
    print("2. Begin Phase 1: Data Preparation and Validation") 
    print("3. Use enforcement tools for all subsequent analysis")
    print("4. No modifications to locked components permitted")
    
    return True


if __name__ == "__main__":
    main()