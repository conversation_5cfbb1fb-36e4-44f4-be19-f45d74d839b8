"""
Unit tests for Data Quality Framework.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.infrastructure.monitoring import (
    DataQualityFramework,
    Price,
    ExchangeRate,
    MarketPair
)


class TestDataQualityFramework:
    """Test data quality framework functionality."""
    
    @pytest.fixture
    def framework(self):
        """Create framework instance."""
        return DataQualityFramework()
    
    @pytest.fixture
    def sample_prices(self):
        """Create sample price data."""
        return [
            Price('wheat_flour', 'Sana\'a', 800, 'YER', datetime(2024, 1, 1), 'WFP'),
            Price('wheat_flour', 'Aden', 1200, 'YER', datetime(2024, 1, 1), 'WFP'),
            Price('rice', 'Sana\'a', 900, 'YER', datetime(2024, 1, 1), 'WFP'),
            Price('rice', 'Aden', 1500, 'YER', datetime(2024, 1, 1), 'WFP'),
            # Add outliers
            Price('wheat_flour', 'Taiz', 5000, 'YER', datetime(2024, 1, 1), 'Local'),  # Too high
            Price('rice', 'Hodeidah', 50, 'YER', datetime(2024, 1, 1), 'Local'),  # Too low
        ]
    
    @pytest.fixture
    def sample_exchange_rates(self):
        """Create sample exchange rate data."""
        return [
            ExchangeRate('houthi', 530, datetime(2024, 1, 1), 'CBY-Sanaa', 0.9),
            ExchangeRate('houthi', 535, datetime(2024, 1, 2), 'CBY-Sanaa', 0.9),
            ExchangeRate('government', 1800, datetime(2024, 1, 1), 'CBY-Aden', 0.85),
            ExchangeRate('government', 2000, datetime(2024, 1, 2), 'CBY-Aden', 0.8),
            # Add anomalies
            ExchangeRate('houthi', 200, datetime(2024, 1, 3), 'Unknown', 0.3),  # Too low
            ExchangeRate('government', 5000, datetime(2024, 1, 3), 'Unknown', 0.2),  # Too high
        ]
    
    def test_price_reasonableness_check(self, framework, sample_prices):
        """Test price reasonableness checking."""
        report = framework.check_price_reasonableness(sample_prices)
        
        # Should detect anomalies
        assert not report.passed  # Should fail due to outliers
        assert report.n_issues >= 2  # At least 2 outliers
        assert any(a.anomaly_type == 'price_too_high' for a in report.anomalies)
        assert any(a.anomaly_type == 'price_too_low' for a in report.anomalies)
        
        # Check summary
        assert report.summary['total_prices'] == 6
        assert report.summary['anomaly_rate'] > 0
    
    def test_exchange_rate_bounds_check(self, framework, sample_exchange_rates):
        """Test exchange rate bounds checking."""
        report = framework.check_exchange_rate_bounds(sample_exchange_rates)
        
        # Should detect anomalies
        assert not report.passed
        assert report.n_issues >= 2
        
        # Check specific anomalies
        anomaly_types = [a.anomaly_type for a in report.anomalies]
        assert 'exchange_rate_too_low' in anomaly_types
        assert 'exchange_rate_too_high' in anomaly_types
        
        # Low confidence should be flagged
        assert any(a.anomaly_type == 'low_confidence_rate' for a in report.anomalies)
    
    def test_temporal_consistency_check(self, framework):
        """Test temporal consistency checking."""
        # Create time series with issues
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        values = np.random.normal(1000, 50, 30)
        
        # Add sudden change
        values[15] = values[14] * 2.5  # 150% increase
        
        # Add gap
        dates_with_gap = dates[:10].append(dates[20:])
        values_with_gap = np.concatenate([values[:10], values[20:]])
        
        ts = pd.Series(values_with_gap, index=dates_with_gap)
        
        report = framework.check_temporal_consistency(ts)
        
        # Should detect issues
        assert report.n_issues >= 2  # Gap and sudden change
        assert any(a.anomaly_type == 'temporal_gap' for a in report.anomalies)
        assert any(a.anomaly_type == 'sudden_change' for a in report.anomalies)
    
    def test_arbitrage_violations_check(self, framework):
        """Test arbitrage violation checking."""
        market_pairs = [
            MarketPair('Sana\'a', 'Aden', 'wheat_flour', 300, 150),
            MarketPair('Aden', 'Taiz', 'rice', 100, 80),
            MarketPair('Sana\'a', 'Hodeidah', 'sugar', 150, 100),
            # Add anomalies
            MarketPair('Sana\'a', 'Socotra', 'oil', 1500, 800),  # Impossible distance
            MarketPair('Aden', 'Marib', 'fuel', 200, 1000),  # Excessive transport cost
        ]
        
        report = framework.check_arbitrage_violations(market_pairs)
        
        # Should detect issues
        assert report.n_issues >= 2
        assert any(a.anomaly_type == 'invalid_distance' for a in report.anomalies)
        assert any(a.anomaly_type == 'excessive_transport_cost' for a in report.anomalies)
    
    def test_suspicious_patterns_detection(self, framework):
        """Test suspicious pattern detection."""
        # Create panel data with suspicious patterns
        data = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=10, freq='D').repeat(4),
            'market': ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah'] * 10,
            'commodity': 'wheat_flour',
            'price': [1000] * 20 + [1200] * 20  # Identical prices across markets
        })
        
        # Add some round numbers
        data.loc[data.index[:30], 'price'] = 1500  # Round number
        
        anomalies = framework.flag_suspicious_patterns(data)
        
        # Should detect patterns
        assert len(anomalies) > 0
        anomaly_types = [a.anomaly_type for a in anomalies]
        assert 'price_collusion_pattern' in anomaly_types or 'synchronized_prices' in anomaly_types
        assert 'round_number_bias' in anomaly_types
    
    def test_quality_dashboard_generation(self, framework, sample_prices, sample_exchange_rates):
        """Test quality dashboard generation."""
        # Generate reports
        price_report = framework.check_price_reasonableness(sample_prices)
        exchange_report = framework.check_exchange_rate_bounds(sample_exchange_rates)
        
        # Create temporal report
        ts = pd.Series(
            np.random.normal(1000, 50, 30),
            index=pd.date_range('2024-01-01', periods=30, freq='D')
        )
        temporal_report = framework.check_temporal_consistency(ts)
        
        # Generate dashboard
        dashboard = framework.generate_quality_dashboard(
            price_report,
            exchange_report,
            [temporal_report]
        )
        
        # Check dashboard structure
        assert 'overall_health' in dashboard
        assert 'component_scores' in dashboard
        assert 'severity_breakdown' in dashboard
        assert 'top_issues' in dashboard
        assert 'recommendations' in dashboard
        
        # Check values
        assert 0 <= dashboard['overall_health']['score'] <= 100
        assert dashboard['overall_health']['total_anomalies'] > 0
        assert dashboard['severity_breakdown']['critical'] > 0
    
    def test_edge_cases(self, framework):
        """Test edge cases."""
        # Empty data
        empty_report = framework.check_price_reasonableness([])
        assert empty_report.passed  # Should pass with no data
        assert empty_report.score == 100
        
        # Single observation
        single_ts = pd.Series([1000], index=[datetime(2024, 1, 1)])
        temporal_report = framework.check_temporal_consistency(single_ts)
        assert not temporal_report.passed  # Need at least 2 observations
        
        # All missing values
        missing_data = pd.DataFrame({
            'price': [np.nan] * 10,
            'market': ['Sana\'a'] * 10,
            'date': pd.date_range('2024-01-01', periods=10, freq='D')
        })
        
        anomalies = framework.flag_suspicious_patterns(missing_data)
        assert any(a.anomaly_type == 'systematic_missing_data' for a in anomalies)