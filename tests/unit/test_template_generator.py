"""
Unit tests for the results template generator.

Tests ensure that the template generator properly:
1. Detects and rejects predetermined language
2. Generates appropriate templates based on results
3. <PERSON>les null results correctly
4. Validates scientific integrity
"""

import pytest
from unittest.mock import Mock, patch

from src.core.reporting.template_generator import (
    ResultsTemplateGenerator,
    TestResult,
    IntegrationMetrics,
    TemplateType,
    EffectSize
)


class TestTemplateGenerator:
    """Test suite for ResultsTemplateGenerator."""

    def setup_method(self):
        """Set up test fixtures."""
        self.generator = ResultsTemplateGenerator()
        
        # Create sample test results
        self.significant_result = TestResult(
            test_name="Integration Test",
            test_statistic=3.45,
            p_value=0.001,
            effect_size=0.65,
            confidence_interval=(0.45, 0.85),
            interpretation="Strong positive effect detected",
            robustness_checks=[]
        )
        
        self.null_result = TestResult(
            test_name="Conflict Effect Test",
            test_statistic=0.23,
            p_value=0.82,
            effect_size=0.02,
            confidence_interval=(-0.18, 0.22),
            interpretation="No significant effect found",
            robustness_checks=[]
        )
        
        self.mixed_results = [
            self.significant_result,
            self.null_result
        ]
        
        self.integration_metrics = IntegrationMetrics(
            speed_of_adjustment=0.35,
            half_life=2.8,
            r_squared=0.67,
            pass_through=0.84,
            error_correction_coef=-0.35
        )

    def test_forbidden_phrases_detection(self):
        """Test that predetermined language is detected and rejected."""
        bad_result = TestResult(
            test_name="Bad Test",
            test_statistic=2.5,
            p_value=0.02,
            effect_size=0.5,
            confidence_interval=(0.1, 0.9),
            interpretation="This revolutionary discovery changes everything",
            robustness_checks=[]
        )
        
        with pytest.raises(ValueError, match="Predetermined language detected"):
            self.generator.generate_results_section(
                [bad_result], 
                "H1",
                TemplateType.EXCHANGE_RATE
            )

    def test_more_forbidden_phrases(self):
        """Test all forbidden phrases are caught."""
        forbidden_phrases = [
            "groundbreaking findings",
            "paradigm shift in understanding",
            "as expected by our theory",
            "confirms our hypothesis perfectly",
            "game-changing results"
        ]
        
        for phrase in forbidden_phrases:
            bad_result = TestResult(
                test_name="Test",
                test_statistic=1.0,
                p_value=0.05,
                effect_size=0.3,
                confidence_interval=(0.1, 0.5),
                interpretation=f"Analysis shows {phrase}",
                robustness_checks=[]
            )
            
            with pytest.raises(ValueError):
                self.generator.generate_results_section(
                    [bad_result],
                    "H1", 
                    TemplateType.EXCHANGE_RATE
                )

    def test_null_result_generation(self):
        """Test proper generation of null result sections."""
        section = self.generator.generate_results_section(
            [self.null_result],
            "H2",
            TemplateType.CONFLICT
        )
        
        # Check that null result template is used
        assert "No Significant Effect Found" in section
        assert "p = 0.820" in section
        assert "Scientific Value" in section
        assert "Possible Explanations" in section

    def test_significant_result_generation(self):
        """Test proper generation of significant result sections."""
        section = self.generator.generate_results_section(
            [self.significant_result],
            "H1",
            TemplateType.INTEGRATION,
            self.integration_metrics
        )
        
        # Check that significant result template is used
        assert "Significant Effect Detected" in section
        assert "Effect Size: 0.650" in section
        assert "medium effect" in section  # 0.65 should be classified as medium
        assert "Moderate market integration detected" in section

    def test_mixed_results_generation(self):
        """Test proper handling of mixed results."""
        section = self.generator.generate_results_section(
            self.mixed_results,
            "H3",
            TemplateType.COMMODITY
        )
        
        # Check mixed results template
        assert "Mixed Evidence" in section
        assert "1 of 2 tests significant" in section

    def test_effect_size_classification(self):
        """Test correct classification of effect sizes."""
        test_cases = [
            (0.02, EffectSize.NEGLIGIBLE),
            (0.15, EffectSize.SMALL),
            (0.35, EffectSize.MEDIUM),
            (0.95, EffectSize.LARGE)
        ]
        
        for effect_size, expected_class in test_cases:
            result = self.generator._classify_effect_size(effect_size)
            assert result == expected_class

    def test_integration_strength_assessment(self):
        """Test market integration strength classification."""
        # Strong integration
        strong = IntegrationMetrics(
            speed_of_adjustment=0.6,
            half_life=2.0,
            r_squared=0.8,
            pass_through=0.9,
            error_correction_coef=-0.6
        )
        assert "Strong market integration" in self.generator._assess_integration_strength(strong)
        
        # Weak integration
        weak = IntegrationMetrics(
            speed_of_adjustment=0.1,
            half_life=15.0,
            r_squared=0.3,
            pass_through=0.2,
            error_correction_coef=-0.1
        )
        assert "Weak market integration" in self.generator._assess_integration_strength(weak)

    def test_policy_implications_generation(self):
        """Test conditional policy implications based on results."""
        # Null results should generate null policy implications
        null_policy = self.generator.generate_policy_implications(
            [self.null_result],
            TemplateType.EXCHANGE_RATE
        )
        assert "Avoid unnecessary interventions" in null_policy
        assert "Redirect resources" in null_policy
        
        # Significant results should generate action-oriented implications
        sig_policy = self.generator.generate_policy_implications(
            [self.significant_result],
            TemplateType.EXCHANGE_RATE
        )
        assert "Priority Level" in sig_policy
        assert "Implementation Considerations" in sig_policy

    def test_template_integrity_validation(self):
        """Test validation of template integrity."""
        # Good template with placeholders
        good_template = """
        Results for [COMMODITY]:
        - Price effect: [TO BE CALCULATED]
        - Significance: [TO BE DETERMINED]
        - Policy implication: [TO BE ASSESSED BASED ON RESULTS]
        """
        assert self.generator.validate_template_integrity(good_template)
        
        # Bad template with predetermined language
        bad_template = """
        Our revolutionary discovery shows that markets are integrated
        as we expected. This game-changing finding proves our theory.
        """
        assert not self.generator.validate_template_integrity(bad_template)

    def test_executive_summary_generation(self):
        """Test executive summary generation."""
        all_results = {
            "H1": [self.significant_result],
            "H2": [self.null_result],
            "H3": self.mixed_results
        }
        
        study_context = {
            'start_date': '2019-01-01',
            'end_date': '2023-12-31',
            'sample_size': 10000
        }
        
        summary = self.generator.generate_executive_summary(
            all_results,
            study_context
        )
        
        # Check summary contains required elements
        assert "Study Period: 2019-01-01 to 2023-12-31" in summary
        assert "Sample Size: 10000" in summary
        assert "Hypotheses Tested: 3" in summary
        assert "Significant Results: 2 of 4 tests" in summary
        assert "[TO BE WRITTEN BASED ON ACTUAL RESULTS]" in summary

    def test_empty_results_handling(self):
        """Test handling of empty results list."""
        with pytest.raises(ValueError, match="At least one test result required"):
            self.generator.generate_results_section(
                [],
                "H1",
                TemplateType.EXCHANGE_RATE
            )

    def test_missing_hypothesis_id(self):
        """Test handling of missing hypothesis ID."""
        with pytest.raises(ValueError, match="Hypothesis ID required"):
            self.generator.generate_results_section(
                [self.significant_result],
                "",
                TemplateType.EXCHANGE_RATE
            )

    def test_result_classification_edge_cases(self):
        """Test edge cases in result classification."""
        # All null results
        all_null = [self.null_result, self.null_result]
        result_type = self.generator._classify_results(all_null, None)
        assert result_type == 'null'
        
        # All significant
        all_sig = [self.significant_result, self.significant_result]
        result_type = self.generator._classify_results(all_sig, None)
        assert result_type == 'significant'
        
        # Mixed
        mixed = [self.significant_result, self.null_result]
        result_type = self.generator._classify_results(mixed, None)
        assert result_type == 'mixed'

    def test_confidence_interval_formatting(self):
        """Test proper formatting of confidence intervals."""
        section = self.generator.generate_results_section(
            [self.significant_result],
            "H1",
            TemplateType.EXCHANGE_RATE
        )
        
        # Check CI is properly formatted
        assert "[0.450, 0.850]" in section

    def test_robustness_section_placeholders(self):
        """Test that robustness sections contain proper placeholders."""
        section = self.generator.generate_results_section(
            [self.significant_result],
            "H1",
            TemplateType.INTEGRATION,
            self.integration_metrics
        )
        
        # Check for robustness placeholders
        assert "[TO BE COMPLETED AFTER ROBUSTNESS CHECKS]" in section
        assert "Alternative specifications tested" in section
        assert "Outlier sensitivity checked" in section


class TestIntegrationWithTemplates:
    """Test integration between generator and actual templates."""

    def test_template_files_exist(self):
        """Verify that referenced template files exist."""
        import os
        template_dir = "docs/research-methodology-package/07-results-templates"
        
        expected_files = [
            "RESULTS_DECISION_FRAMEWORK.md",
            "NULL_RESULTS_TEMPLATE.md",
            "TEMPLATE_USAGE_GUIDE.md",
            "main-findings/exchange-rate-impact.md",
            "main-findings/wheat-analysis.md"
        ]
        
        for file in expected_files:
            path = os.path.join(template_dir, file)
            assert os.path.exists(path), f"Template file missing: {path}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])