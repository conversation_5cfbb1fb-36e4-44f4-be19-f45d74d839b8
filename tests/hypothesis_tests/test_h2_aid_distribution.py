"""
Test suite for H2: Aid Distribution Channel Effects hypothesis.

Tests that humanitarian aid depresses local prices with effects varying by modality:
- Cash aid: -8% price effect
- In-kind aid: -15% price effect
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from src.core.models.hypothesis_testing import (
    H2AidDistributionTest,
    HypothesisOutcome,
    HypothesisRegistry
)


class TestH2AidDistribution:
    """Test suite for aid distribution channel effects."""
    
    @pytest.fixture
    def test_data(self):
        """Create test panel data with aid distributions."""
        # Create date range
        dates = pd.date_range('2020-01-01', '2024-12-31', freq='D')
        
        # Create markets
        markets = [
            "Sana'a", "Aden", "Taiz", "Al Hudaydah", "Ibb",
            "Dhamar", "Mukalla", "Sa'ada", "Hajjah", "Amran"
        ]
        
        # Create commodities
        commodities = [
            'wheat_flour', 'rice', 'sugar', 'oil', 'beans',
            'lentils', 'eggs', 'tomatoes', 'onions', 'salt'
        ]
        
        # Create panel data
        data = []
        
        for date in dates[::7]:  # Weekly data
            for market in markets:
                for commodity in commodities:
                    # Determine currency zone
                    if market in ["Sana'a", "Sa'ada", "Hajjah", "Amran", "Dhamar"]:
                        zone = 'houthi'
                        base_price = 1000
                    elif market in ["Aden", "Mukalla"]:
                        zone = 'government'
                        base_price = 1500
                    else:
                        zone = 'contested'
                        base_price = 1200
                    
                    # Add some variation
                    price = base_price * (1 + np.random.normal(0, 0.1))
                    
                    data.append({
                        'date': date,
                        'market': market,
                        'commodity': commodity,
                        'price': price,
                        'price_yer': price,
                        'currency_zone': zone
                    })
        
        df = pd.DataFrame(data)
        
        # Add aid distribution events
        # Cash aid events
        cash_events = [
            {'market': "Sana'a", 'date': pd.Timestamp('2023-01-15'), 'impact': -0.08},
            {'market': "Aden", 'date': pd.Timestamp('2023-02-15'), 'impact': -0.12},
            {'market': "Taiz", 'date': pd.Timestamp('2023-03-15'), 'impact': -0.10},
        ]
        
        # In-kind aid events
        inkind_events = [
            {'market': "Sa'ada", 'date': pd.Timestamp('2023-01-20'), 'impact': -0.15},
            {'market': "Mukalla", 'date': pd.Timestamp('2023-02-20'), 'impact': -0.18},
            {'market': "Al Hudaydah", 'date': pd.Timestamp('2023-03-20'), 'impact': -0.16},
        ]
        
        # Apply aid effects
        for event in cash_events:
            mask = (
                (df['market'] == event['market']) &
                (df['date'] >= event['date']) &
                (df['date'] < event['date'] + timedelta(days=90))
            )
            df.loc[mask, 'price'] *= (1 + event['impact'])
            df.loc[mask, 'price_yer'] *= (1 + event['impact'])
        
        for event in inkind_events:
            mask = (
                (df['market'] == event['market']) &
                (df['date'] >= event['date']) &
                (df['date'] < event['date'] + timedelta(days=90))
            )
            df.loc[mask, 'price'] *= (1 + event['impact'])
            df.loc[mask, 'price_yer'] *= (1 + event['impact'])
        
        return df
    
    @pytest.fixture
    def h2_test(self):
        """Create H2 test instance."""
        return H2AidDistributionTest()
    
    def test_h2_initialization(self, h2_test):
        """Test H2 hypothesis test initialization."""
        assert h2_test.hypothesis_id == "H2"
        assert h2_test.name == "Aid Distribution Channel Effects"
        assert h2_test.min_aid_events == 10
        assert h2_test.spillover_radius_km == 50
        assert h2_test.effect_window_days == 90
    
    def test_h2_registered(self):
        """Test that H2 is registered in hypothesis registry."""
        registry = HypothesisRegistry()
        h2_test = registry.get("H2")
        assert h2_test is not None
        assert isinstance(h2_test, H2AidDistributionTest)
    
    def test_prepare_data(self, h2_test, test_data):
        """Test data preparation for H2."""
        aid_data = h2_test.prepare_data(test_data)
        
        assert hasattr(aid_data, 'panel_data')
        assert hasattr(aid_data, 'aid_distributions')
        assert hasattr(aid_data, 'market_characteristics')
        assert hasattr(aid_data, 'spillover_matrix')
        assert hasattr(aid_data, 'control_markets')
        
        # Check aid distributions were created
        assert len(aid_data.aid_distributions) > 0
        assert 'modality' in aid_data.aid_distributions.columns
        assert 'market' in aid_data.aid_distributions.columns
        assert 'date' in aid_data.aid_distributions.columns
        
        # Check market characteristics
        assert len(aid_data.market_characteristics) == test_data['market'].nunique()
        assert 'currency_zone' in aid_data.market_characteristics.columns
        assert 'latitude' in aid_data.market_characteristics.columns
        assert 'longitude' in aid_data.market_characteristics.columns
        
        # Check spillover matrix
        assert len(aid_data.spillover_matrix) > 0
        assert 'spillover_probability' in aid_data.spillover_matrix.columns
        
        # Check treatment variables were added
        assert 'cash_treatment' in aid_data.panel_data.columns
        assert 'inkind_treatment' in aid_data.panel_data.columns
        assert 'spillover_treatment' in aid_data.panel_data.columns
    
    def test_run_test(self, h2_test, test_data):
        """Test running H2 hypothesis test."""
        # Prepare data
        aid_data = h2_test.prepare_data(test_data)
        
        # Run test
        results = h2_test.run_test(aid_data)
        
        # Check results structure
        assert results.hypothesis_id == "H2"
        assert hasattr(results, 'cash_effect')
        assert hasattr(results, 'inkind_effect')
        assert hasattr(results, 'cash_effect_ci')
        assert hasattr(results, 'inkind_effect_ci')
        assert hasattr(results, 'zone_differential')
        assert hasattr(results, 'spillover_effects')
        assert hasattr(results, 'timing_decay')
        assert hasattr(results, 'commodity_effects')
        assert hasattr(results, 'modality_difference_pvalue')
        
        # Check that cash and in-kind effects have correct signs
        assert results.cash_effect < 0, "Cash aid should reduce prices"
        assert results.inkind_effect < 0, "In-kind aid should reduce prices"
        
        # Check that in-kind has larger effect than cash
        assert abs(results.inkind_effect) > abs(results.cash_effect), \
            "In-kind aid should have larger price effect than cash"
        
        # Check zone differential exists
        assert 'houthi' in results.zone_differential
        assert 'government' in results.zone_differential
        assert 'contested' in results.zone_differential
        
        # Check spillover effects
        assert 'spillover_coefficient' in results.spillover_effects
        assert results.spillover_effects['spillover_coefficient'] < 0
        
        # Check timing decay
        assert 'half_life_days' in results.timing_decay
        assert results.timing_decay['half_life_days'] > 0
        
        # Check commodity effects
        assert len(results.commodity_effects) > 0
        for effect in results.commodity_effects.values():
            assert effect < 0, "All commodity effects should be negative"
    
    def test_expected_values(self, h2_test, test_data):
        """Test that results match expected values from hypothesis."""
        # Prepare and run test
        aid_data = h2_test.prepare_data(test_data)
        results = h2_test.run_test(aid_data)
        
        # Check that cash effect is approximately -8%
        assert -0.12 <= results.cash_effect <= -0.04, \
            f"Cash effect {results.cash_effect} not in expected range [-12%, -4%]"
        
        # Check that in-kind effect is approximately -15%
        assert -0.21 <= results.inkind_effect <= -0.09, \
            f"In-kind effect {results.inkind_effect} not in expected range [-21%, -9%]"
        
        # Check confidence intervals include expected values
        assert results.cash_effect_ci[0] <= -0.08 <= results.cash_effect_ci[1], \
            "Cash confidence interval should include -8%"
        assert results.inkind_effect_ci[0] <= -0.15 <= results.inkind_effect_ci[1], \
            "In-kind confidence interval should include -15%"
    
    def test_zone_differential_effects(self, h2_test, test_data):
        """Test that aid effects vary by currency zone."""
        # Prepare and run test
        aid_data = h2_test.prepare_data(test_data)
        results = h2_test.run_test(aid_data)
        
        # Government zones (depreciated currency) should show larger effects
        gov_cash = abs(results.zone_differential['government']['cash_effect'])
        houthi_cash = abs(results.zone_differential['houthi']['cash_effect'])
        
        # This is expected based on purchasing power differences
        # In depreciated zones, same nominal aid has less real impact
        assert gov_cash > 0
        assert houthi_cash > 0
    
    def test_spillover_effects(self, h2_test, test_data):
        """Test spillover effects to neighboring markets."""
        # Prepare and run test
        aid_data = h2_test.prepare_data(test_data)
        results = h2_test.run_test(aid_data)
        
        # Check spillover coefficient
        spillover_coef = results.spillover_effects['spillover_coefficient']
        assert spillover_coef < 0, "Spillover should also reduce prices"
        assert abs(spillover_coef) < abs(results.cash_effect), \
            "Spillover effect should be smaller than direct effect"
        
        # Check spillover distance
        assert results.spillover_effects['avg_spillover_distance'] > 0
        assert results.spillover_effects['avg_spillover_distance'] <= h2_test.spillover_radius_km
    
    def test_timing_decay(self, h2_test, test_data):
        """Test that aid effects decay over time."""
        # Prepare and run test
        aid_data = h2_test.prepare_data(test_data)
        results = h2_test.run_test(aid_data)
        
        decay = results.timing_decay
        
        # Effects should decrease over time
        assert abs(decay['week1_effect']) > abs(decay['week2_4_effect'])
        assert abs(decay['week2_4_effect']) > abs(decay['month2_effect'])
        assert abs(decay['month2_effect']) > abs(decay['month3_effect'])
        
        # Half-life should be reasonable (2-4 weeks)
        assert 14 <= decay['half_life_days'] <= 30
    
    def test_commodity_effects(self, h2_test, test_data):
        """Test differential effects by commodity type."""
        # Prepare and run test
        aid_data = h2_test.prepare_data(test_data)
        results = h2_test.run_test(aid_data)
        
        effects = results.commodity_effects
        
        # Staples should have larger effects (commonly distributed)
        if 'staples' in effects and 'perishables' in effects:
            assert abs(effects['staples']) > abs(effects['perishables']), \
                "Staples should show larger aid effects than perishables"
        
        # All effects should be negative
        for commodity_group, effect in effects.items():
            assert effect < 0, f"{commodity_group} should have negative price effect"
    
    def test_hypothesis_outcome(self, h2_test, test_data):
        """Test hypothesis outcome determination."""
        # Prepare and run test
        aid_data = h2_test.prepare_data(test_data)
        results = h2_test.run_test(aid_data)
        
        # With synthetic data matching expected values, should be supported or partial
        assert results.outcome in [HypothesisOutcome.SUPPORTED, HypothesisOutcome.PARTIAL]
        
        # If supported, check specific conditions
        if results.outcome == HypothesisOutcome.SUPPORTED:
            # Cash effect should be near -8%
            assert abs(results.cash_effect - (-0.08)) < 0.05
            # In-kind effect should be near -15%
            assert abs(results.inkind_effect - (-0.15)) < 0.05
            # Difference should be significant
            assert results.modality_difference_pvalue < 0.05
    
    def test_interpret_results(self, h2_test, test_data):
        """Test policy interpretation of results."""
        # Prepare and run test
        aid_data = h2_test.prepare_data(test_data)
        results = h2_test.run_test(aid_data)
        
        # Get interpretation
        interpretation = h2_test.interpret_results(results)
        
        # Check interpretation structure
        assert hasattr(interpretation, 'summary')
        assert hasattr(interpretation, 'implications')
        assert hasattr(interpretation, 'recommendations')
        assert hasattr(interpretation, 'confidence_statement')
        assert hasattr(interpretation, 'caveats')
        assert hasattr(interpretation, 'visualizations')
        
        # Check visualizations
        assert 'modality_comparison' in interpretation.visualizations
        assert 'zone_effects' in interpretation.visualizations
        assert 'timing_decay' in interpretation.visualizations
        
        # Check that recommendations align with results
        if results.outcome == HypothesisOutcome.SUPPORTED:
            recs_text = ' '.join(interpretation.recommendations)
            assert 'cash' in recs_text.lower()
            assert 'in-kind' in recs_text.lower() or 'inkind' in recs_text.lower()
    
    def test_insufficient_data_handling(self, h2_test):
        """Test handling of insufficient data."""
        # Create minimal data
        minimal_data = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=10),
            'market': ['Market1'] * 10,
            'commodity': ['wheat'] * 10,
            'price': np.random.uniform(100, 200, 10),
            'price_yer': np.random.uniform(100, 200, 10),
            'currency_zone': ['houthi'] * 10
        })
        
        # Prepare data
        aid_data = h2_test.prepare_data(minimal_data)
        
        # Manually set insufficient aid events
        aid_data.aid_distributions = pd.DataFrame()  # Empty
        
        # Run test
        results = h2_test.run_test(aid_data)
        
        # Should return rejected outcome
        assert results.outcome == HypothesisOutcome.REJECTED
        assert results.confidence_level == 0
        assert 'error' in results.diagnostic_tests