"""
Test H9: Threshold Effects in Market Integration

Small exchange rate gaps don't significantly affect market integration,
but large gaps (>100% differential) create a regime switch that breaks
down market relationships. This tests for non-linear threshold effects.
"""

import pytest
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import statsmodels.api as sm
from scipy import stats

from src.core.models.time_series.threshold_vecm import ThresholdVECM
from src.infrastructure.estimators.panel_estimators import PanelThresholdModel


class TestH9ThresholdEffects:
    """Test suite for Hypothesis H9: Non-linear threshold effects in integration."""
    
    @pytest.fixture
    def integration_data(self) -> pd.DataFrame:
        """Create data showing threshold effects in market integration."""
        np.random.seed(42)
        
        # Create time series data with varying exchange rate gaps
        dates = pd.date_range('2020-01-01', '2024-12-31', freq='W')
        
        data = []
        
        # Simulate different exchange rate gap scenarios
        for t, date in enumerate(dates):
            # Create exchange rate gap that varies over time
            if t < 52:  # First year: small gap
                exchange_gap_pct = np.random.normal(20, 5)
            elif t < 156:  # Years 2-3: medium gap
                exchange_gap_pct = np.random.normal(80, 10)
            else:  # Years 4-5: large gap
                exchange_gap_pct = np.random.normal(150, 20)
            
            # Market pairs with different characteristics
            market_pairs = [
                ('Sana\'a-Aden', 'high_volume'),
                ('Sa\'ada-Marib', 'medium_volume'),
                ('Taiz-Ibb', 'low_volume'),
                ('Aden-Mukalla', 'same_zone'),
                ('Sana\'a-Hajjah', 'same_zone')
            ]
            
            for pair_name, pair_type in market_pairs:
                # Base correlation depends on exchange gap (threshold effect)
                if exchange_gap_pct < 50:
                    base_correlation = 0.85 if pair_type != 'low_volume' else 0.70
                elif exchange_gap_pct < 100:
                    base_correlation = 0.60 if pair_type != 'low_volume' else 0.45
                else:  # Large gap - regime switch
                    base_correlation = 0.25 if pair_type != 'same_zone' else 0.80
                
                # Add noise
                actual_correlation = np.clip(base_correlation + np.random.normal(0, 0.1), 0, 1)
                
                # Price transmission speed (adjustment parameter)
                if exchange_gap_pct < 100:
                    adjustment_speed = 0.4 + np.random.normal(0, 0.05)
                else:
                    adjustment_speed = 0.1 + np.random.normal(0, 0.02)
                
                # Integration measure (composite of correlation and adjustment)
                integration_score = actual_correlation * adjustment_speed
                
                data.append({
                    'date': date,
                    'market_pair': pair_name,
                    'pair_type': pair_type,
                    'exchange_gap_pct': exchange_gap_pct,
                    'price_correlation': actual_correlation,
                    'adjustment_speed': adjustment_speed,
                    'integration_score': integration_score,
                    'regime': 'integrated' if exchange_gap_pct < 100 else 'fragmented'
                })
        
        return pd.DataFrame(data)
    
    def test_threshold_exists_at_100_percent(self, integration_data: pd.DataFrame):
        """Test that a threshold effect exists around 100% exchange rate differential."""
        # Test for structural break in integration score around 100% gap
        integration_data = integration_data.sort_values('exchange_gap_pct')
        
        # Split data around potential threshold
        threshold = 100
        below_threshold = integration_data[integration_data['exchange_gap_pct'] < threshold]
        above_threshold = integration_data[integration_data['exchange_gap_pct'] >= threshold]
        
        # Compare mean integration scores
        mean_below = below_threshold['integration_score'].mean()
        mean_above = above_threshold['integration_score'].mean()
        
        # T-test for difference
        t_stat, p_value = stats.ttest_ind(
            below_threshold['integration_score'],
            above_threshold['integration_score']
        )
        
        assert mean_below > mean_above, \
            f"Integration not higher below threshold: {mean_below:.3f} vs {mean_above:.3f}"
        
        assert p_value < 0.001, \
            f"Threshold effect not significant: p={p_value:.3f}"
        
        # Calculate effect size (Cohen's d)
        pooled_std = np.sqrt(
            (below_threshold['integration_score'].std()**2 + 
             above_threshold['integration_score'].std()**2) / 2
        )
        cohens_d = (mean_below - mean_above) / pooled_std
        
        assert cohens_d > 0.8, \
            f"Effect size too small: Cohen's d = {cohens_d:.3f} (expected > 0.8)"
        
        print(f"✓ Threshold effect confirmed at 100% exchange gap:")
        print(f"  - Mean integration below: {mean_below:.3f}")
        print(f"  - Mean integration above: {mean_above:.3f}")
        print(f"  - Effect size (Cohen's d): {cohens_d:.3f}")
        print(f"  - Statistical significance: p={p_value:.4f}")
    
    def test_nonlinear_relationship(self, integration_data: pd.DataFrame):
        """Test that the relationship between exchange gap and integration is non-linear."""
        # Fit linear model
        X_linear = sm.add_constant(integration_data['exchange_gap_pct'])
        y = integration_data['integration_score']
        
        linear_model = sm.OLS(y, X_linear).fit()
        linear_r2 = linear_model.rsquared
        
        # Fit polynomial model (captures non-linearity)
        integration_data['gap_squared'] = integration_data['exchange_gap_pct'] ** 2
        integration_data['gap_cubed'] = integration_data['exchange_gap_pct'] ** 3
        
        X_poly = sm.add_constant(
            integration_data[['exchange_gap_pct', 'gap_squared', 'gap_cubed']]
        )
        poly_model = sm.OLS(y, X_poly).fit()
        poly_r2 = poly_model.rsquared
        
        # Fit piecewise linear model (threshold at 100%)
        integration_data['above_100'] = (integration_data['exchange_gap_pct'] > 100).astype(int)
        integration_data['gap_above_100'] = (
            integration_data['exchange_gap_pct'] - 100
        ) * integration_data['above_100']
        
        X_threshold = sm.add_constant(
            integration_data[['exchange_gap_pct', 'gap_above_100']]
        )
        threshold_model = sm.OLS(y, X_threshold).fit()
        threshold_r2 = threshold_model.rsquared
        
        # Non-linear models should fit better
        assert poly_r2 > linear_r2 * 1.1, \
            f"Polynomial model doesn't improve fit: {poly_r2:.3f} vs {linear_r2:.3f}"
        
        assert threshold_r2 > linear_r2 * 1.1, \
            f"Threshold model doesn't improve fit: {threshold_r2:.3f} vs {linear_r2:.3f}"
        
        # Check that slope changes at threshold
        slope_below = threshold_model.params['exchange_gap_pct']
        slope_change = threshold_model.params['gap_above_100']
        
        assert slope_change < 0, \
            f"Slope doesn't become more negative above threshold: {slope_change:.3f}"
        
        print(f"✓ Non-linear relationship confirmed:")
        print(f"  - Linear R²: {linear_r2:.3f}")
        print(f"  - Polynomial R²: {poly_r2:.3f}")
        print(f"  - Threshold R²: {threshold_r2:.3f}")
        print(f"  - Slope below 100%: {slope_below:.4f}")
        print(f"  - Additional slope above 100%: {slope_change:.4f}")
    
    def test_regime_specific_dynamics(self, integration_data: pd.DataFrame):
        """Test that market dynamics differ fundamentally between regimes."""
        # Separate regimes
        integrated_regime = integration_data[integration_data['regime'] == 'integrated']
        fragmented_regime = integration_data[integration_data['regime'] == 'fragmented']
        
        # Test 1: Variance of integration scores
        var_integrated = integrated_regime['integration_score'].var()
        var_fragmented = fragmented_regime['integration_score'].var()
        
        # F-test for equality of variances
        f_stat = var_integrated / var_fragmented
        df1 = len(integrated_regime) - 1
        df2 = len(fragmented_regime) - 1
        p_value_var = 2 * min(stats.f.cdf(f_stat, df1, df2), 1 - stats.f.cdf(f_stat, df1, df2))
        
        assert p_value_var < 0.05, \
            "Variances not significantly different between regimes"
        
        # Test 2: Persistence of shocks (autocorrelation)
        def calculate_autocorrelation(data: pd.Series, lag: int = 1) -> float:
            if len(data) > lag:
                return data.autocorr(lag=lag)
            return np.nan
        
        # Calculate by market pair to get multiple observations
        autocorr_integrated = integrated_regime.groupby('market_pair')['integration_score'].apply(
            lambda x: calculate_autocorrelation(x)
        ).mean()
        
        autocorr_fragmented = fragmented_regime.groupby('market_pair')['integration_score'].apply(
            lambda x: calculate_autocorrelation(x)
        ).mean()
        
        # Fragmented regime should show higher persistence (slower adjustment)
        assert autocorr_fragmented > autocorr_integrated, \
            f"Fragmented regime doesn't show higher persistence: {autocorr_fragmented:.3f} vs {autocorr_integrated:.3f}"
        
        # Test 3: Response to shocks differs by regime
        integrated_adjustment = integrated_regime['adjustment_speed'].mean()
        fragmented_adjustment = fragmented_regime['adjustment_speed'].mean()
        
        assert integrated_adjustment > fragmented_adjustment * 2, \
            f"Adjustment speed not sufficiently different: {integrated_adjustment:.3f} vs {fragmented_adjustment:.3f}"
        
        print(f"✓ Regime-specific dynamics confirmed:")
        print(f"  - Variance ratio (integrated/fragmented): {f_stat:.3f} (p={p_value_var:.3f})")
        print(f"  - Autocorrelation integrated: {autocorr_integrated:.3f}")
        print(f"  - Autocorrelation fragmented: {autocorr_fragmented:.3f}")
        print(f"  - Adjustment speed integrated: {integrated_adjustment:.3f}")
        print(f"  - Adjustment speed fragmented: {fragmented_adjustment:.3f}")
    
    def test_threshold_stability_over_time(self, integration_data: pd.DataFrame):
        """Test that the threshold value is stable over different time periods."""
        # Test threshold in different time windows
        integration_data['year'] = pd.to_datetime(integration_data['date']).dt.year
        
        threshold_estimates = []
        
        for year in integration_data['year'].unique():
            year_data = integration_data[integration_data['year'] == year]
            
            if len(year_data) > 100:  # Need sufficient data
                # Grid search for optimal threshold
                potential_thresholds = np.arange(50, 150, 10)
                best_threshold = None
                best_r2 = 0
                
                for threshold in potential_thresholds:
                    year_data['above_threshold'] = (
                        year_data['exchange_gap_pct'] > threshold
                    ).astype(int)
                    year_data['gap_above'] = (
                        year_data['exchange_gap_pct'] - threshold
                    ) * year_data['above_threshold']
                    
                    X = sm.add_constant(
                        year_data[['exchange_gap_pct', 'gap_above']]
                    )
                    y = year_data['integration_score']
                    
                    try:
                        model = sm.OLS(y, X).fit()
                        if model.rsquared > best_r2 and model.pvalues['gap_above'] < 0.1:
                            best_r2 = model.rsquared
                            best_threshold = threshold
                    except:
                        continue
                
                if best_threshold:
                    threshold_estimates.append({
                        'year': year,
                        'threshold': best_threshold,
                        'r2': best_r2
                    })
        
        # Check stability of threshold estimates
        thresholds = [est['threshold'] for est in threshold_estimates]
        threshold_std = np.std(thresholds)
        threshold_mean = np.mean(thresholds)
        
        # Coefficient of variation should be low (stable threshold)
        cv = threshold_std / threshold_mean
        assert cv < 0.2, \
            f"Threshold estimates too variable: CV={cv:.3f} (expected < 0.2)"
        
        # Mean should be close to 100%
        assert 80 < threshold_mean < 120, \
            f"Mean threshold {threshold_mean:.0f}% not close to expected 100%"
        
        print(f"✓ Threshold stability confirmed:")
        print(f"  - Threshold estimates by year: {thresholds}")
        print(f"  - Mean threshold: {threshold_mean:.0f}%")
        print(f"  - Coefficient of variation: {cv:.3f}")
    
    def test_market_pair_heterogeneity(self, integration_data: pd.DataFrame):
        """Test that threshold effects vary by market pair characteristics."""
        # Test threshold effect by market pair type
        pair_types = integration_data['pair_type'].unique()
        
        threshold_effects = {}
        
        for pair_type in pair_types:
            pair_data = integration_data[integration_data['pair_type'] == pair_type]
            
            # Calculate mean integration above/below threshold
            below_100 = pair_data[pair_data['exchange_gap_pct'] < 100]['integration_score'].mean()
            above_100 = pair_data[pair_data['exchange_gap_pct'] >= 100]['integration_score'].mean()
            
            threshold_effect = (below_100 - above_100) / below_100 * 100  # % drop
            
            threshold_effects[pair_type] = {
                'effect_size': threshold_effect,
                'below_mean': below_100,
                'above_mean': above_100
            }
        
        # Same-zone pairs should show smaller threshold effects
        same_zone_effect = threshold_effects['same_zone']['effect_size']
        cross_zone_effects = [
            threshold_effects[pt]['effect_size'] 
            for pt in threshold_effects 
            if pt != 'same_zone'
        ]
        avg_cross_zone_effect = np.mean(cross_zone_effects)
        
        assert same_zone_effect < avg_cross_zone_effect * 0.5, \
            f"Same-zone pairs don't show smaller threshold effect: {same_zone_effect:.1f}% vs {avg_cross_zone_effect:.1f}%"
        
        # High-volume pairs should maintain more integration
        high_volume_above = threshold_effects['high_volume']['above_mean']
        low_volume_above = threshold_effects['low_volume']['above_mean']
        
        assert high_volume_above > low_volume_above, \
            f"High-volume pairs don't maintain better integration: {high_volume_above:.3f} vs {low_volume_above:.3f}"
        
        print(f"✓ Market pair heterogeneity confirmed:")
        for pair_type, effects in threshold_effects.items():
            print(f"  - {pair_type}: {effects['effect_size']:.1f}% drop at threshold")
            print(f"    (from {effects['below_mean']:.3f} to {effects['above_mean']:.3f})")


class TestThresholdVECMImplementation:
    """Test the actual Threshold VECM implementation."""
    
    @pytest.fixture
    def price_series_data(self) -> Tuple[pd.Series, pd.Series]:
        """Create cointegrated price series with threshold effects."""
        np.random.seed(42)
        n = 500
        
        # Common stochastic trend
        trend = np.cumsum(np.random.normal(0, 1, n))
        
        # Market 1 prices
        price1 = 100 + trend + np.random.normal(0, 2, n)
        
        # Market 2 prices with threshold effect
        price2 = np.zeros(n)
        threshold_value = 10  # Price differential threshold
        
        for t in range(n):
            price_diff = abs(price1[t] - (price2[t-1] if t > 0 else 100))
            
            if price_diff < threshold_value:
                # Normal regime - strong cointegration
                price2[t] = 102 + trend[t] + 0.5 * np.random.normal(0, 2)
            else:
                # Threshold exceeded - weak cointegration
                price2[t] = price2[t-1] if t > 0 else 100
                price2[t] += 0.1 * (price1[t] - price2[t]) + np.random.normal(0, 5)
        
        return pd.Series(price1, name='market1'), pd.Series(price2, name='market2')
    
    @pytest.mark.integration
    def test_threshold_vecm_estimation(self, price_series_data: Tuple[pd.Series, pd.Series]):
        """Test that Threshold VECM correctly identifies regime switching."""
        price1, price2 = price_series_data
        
        # Estimate Threshold VECM
        model = ThresholdVECM(threshold_variable='price_differential')
        
        # Fit model
        results = model.fit(
            endog=pd.concat([price1, price2], axis=1),
            threshold_search_range=(5, 15),
            max_lags=2
        )
        
        # Check that threshold is correctly identified
        assert 8 < results.threshold < 12, \
            f"Threshold {results.threshold:.1f} not in expected range (8-12)"
        
        # Check that adjustment speeds differ by regime
        alpha_regime1 = results.adjustment_speeds['regime1']
        alpha_regime2 = results.adjustment_speeds['regime2']
        
        assert abs(alpha_regime1[0]) > abs(alpha_regime2[0]), \
            "Adjustment speed not higher in normal regime"
        
        print(f"✓ Threshold VECM estimation successful:")
        print(f"  - Estimated threshold: {results.threshold:.1f}")
        print(f"  - Regime 1 adjustment: {alpha_regime1[0]:.3f}")
        print(f"  - Regime 2 adjustment: {alpha_regime2[0]:.3f}")