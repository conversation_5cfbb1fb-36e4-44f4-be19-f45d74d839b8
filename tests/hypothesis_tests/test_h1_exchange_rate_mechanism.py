"""
Test H1: Exchange Rate Mechanism Hypothesis

The Yemen Paradox (lower prices in conflict zones) disappears when prices 
are analyzed in USD rather than YER, revealing that currency fragmentation,
not conflict itself, drives apparent price differentials.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime
from decimal import Decimal
from typing import Dict, Tuple

from src.core.domain.market.currency_zones import CurrencyZone, CurrencyZoneService
from src.infrastructure.processors.currency_aware_wfp_processor import CurrencyAwareWFPProcessor
from src.core.models.panel.pooled_panel import PooledPanelModel


class TestH1ExchangeRateMechanism:
    """Test suite for Hypothesis H1: Exchange rate mechanism explains price paradox."""
    
    @pytest.fixture
    def sample_price_data(self) -> pd.DataFrame:
        """Create sample data demonstrating the Yemen Paradox."""
        data = {
            'market_id': ['sana01', 'sana02', 'aden01', 'aden02', 'taiz01'] * 12,
            'market_name': ['Sana\'a Central', 'Sana\'a North', 'Aden Port', 'Aden City', 'Taiz Market'] * 12,
            'governorate': ['Sana\'a', 'Sana\'a', 'Aden', 'Aden', 'Taiz'] * 12,
            'date': pd.date_range('2024-01-01', periods=60, freq='M'),
            'commodity': ['Wheat Flour'] * 60,
            'price_yer': [450, 440, 1800, 1850, 800] * 12,  # YER prices
            'currency': ['YER'] * 60,
            'unit': ['50 kg'] * 60
        }
        
        df = pd.DataFrame(data)
        # Add zone classification
        df['currency_zone'] = df['governorate'].map({
            'Sana\'a': 'houthi',
            'Aden': 'government',
            'Taiz': 'contested'
        })
        
        return df
    
    @pytest.fixture
    def zone_exchange_rates(self) -> Dict[CurrencyZone, Decimal]:
        """Exchange rates by currency zone."""
        return {
            CurrencyZone.HOUTHI: Decimal("535"),      # Stable CBY Sana'a rate
            CurrencyZone.GOVERNMENT: Decimal("2000"),  # Depreciated CBY Aden rate
            CurrencyZone.CONTESTED: Decimal("1200")    # Mixed control average
        }
    
    def test_yer_prices_show_paradox(self, sample_price_data: pd.DataFrame):
        """Test that YER prices show the Yemen Paradox (conflict zones cheaper)."""
        # Calculate average prices by zone in YER
        zone_prices_yer = sample_price_data.groupby('currency_zone')['price_yer'].mean()
        
        # Verify the paradox: Houthi (conflict) zones appear cheaper
        assert zone_prices_yer['houthi'] < zone_prices_yer['government'], \
            "Yemen Paradox not demonstrated: Houthi areas should show lower YER prices"
        
        # Calculate percentage difference
        price_diff_pct = (zone_prices_yer['government'] - zone_prices_yer['houthi']) / zone_prices_yer['government'] * 100
        assert price_diff_pct > 50, \
            f"Price differential too small: {price_diff_pct:.1f}% (expected > 50%)"
        
        print(f"✓ Yemen Paradox confirmed: Houthi areas {price_diff_pct:.1f}% 'cheaper' in YER")
    
    def test_usd_prices_reveal_truth(self, sample_price_data: pd.DataFrame, zone_exchange_rates: Dict):
        """Test that USD prices reveal the truth (conflict zones more expensive)."""
        # Convert prices to USD using zone-specific rates
        zone_service = CurrencyZoneService()
        
        # Add USD prices
        sample_price_data['price_usd'] = sample_price_data.apply(
            lambda row: float(zone_service.convert_price_to_usd(
                Decimal(str(row['price_yer'])),
                CurrencyZone(row['currency_zone']),
                zone_exchange_rates
            )), axis=1
        )
        
        # Calculate average USD prices by zone
        zone_prices_usd = sample_price_data.groupby('currency_zone')['price_usd'].mean()
        
        # Verify the truth: Houthi (conflict) zones are actually MORE expensive
        assert zone_prices_usd['houthi'] > zone_prices_usd['government'], \
            "Exchange rate adjustment failed: Houthi areas should show higher USD prices"
        
        # Calculate true premium in conflict zones
        conflict_premium_pct = (zone_prices_usd['houthi'] - zone_prices_usd['government']) / zone_prices_usd['government'] * 100
        assert conflict_premium_pct > 15, \
            f"Conflict premium too small: {conflict_premium_pct:.1f}% (expected > 15%)"
        
        print(f"✓ Truth revealed: Houthi areas {conflict_premium_pct:.1f}% MORE expensive in USD")
    
    def test_panel_regression_confirms_mechanism(self, sample_price_data: pd.DataFrame, zone_exchange_rates: Dict):
        """Test that panel regression confirms exchange rate mechanism."""
        # Prepare data with both YER and USD prices
        zone_service = CurrencyZoneService()
        sample_price_data['price_usd'] = sample_price_data.apply(
            lambda row: float(zone_service.convert_price_to_usd(
                Decimal(str(row['price_yer'])),
                CurrencyZone(row['currency_zone']),
                zone_exchange_rates
            )), axis=1
        )
        
        # Create zone dummies
        sample_price_data['is_houthi'] = (sample_price_data['currency_zone'] == 'houthi').astype(int)
        sample_price_data['is_contested'] = (sample_price_data['currency_zone'] == 'contested').astype(int)
        
        # Model 1: YER prices (should show negative coefficient for Houthi)
        model_yer = PooledPanelModel()
        X_yer = sample_price_data[['is_houthi', 'is_contested']]
        y_yer = np.log(sample_price_data['price_yer'])  # Log prices
        
        results_yer = model_yer.fit(X_yer, y_yer, entity_effects=True, time_effects=True)
        
        # Model 2: USD prices (should show positive coefficient for Houthi)
        model_usd = PooledPanelModel()
        y_usd = np.log(sample_price_data['price_usd'])  # Log prices
        
        results_usd = model_usd.fit(X_yer, y_usd, entity_effects=True, time_effects=True)
        
        # Verify coefficient signs
        assert results_yer.params['is_houthi'] < 0, \
            "YER model should show negative Houthi coefficient (apparent lower prices)"
        
        assert results_usd.params['is_houthi'] > 0, \
            "USD model should show positive Houthi coefficient (true higher prices)"
        
        # Check statistical significance
        assert results_yer.pvalues['is_houthi'] < 0.05, \
            "YER Houthi effect not statistically significant"
        
        assert results_usd.pvalues['is_houthi'] < 0.05, \
            "USD Houthi effect not statistically significant"
        
        print(f"✓ Panel regression confirms mechanism:")
        print(f"  - YER model: Houthi coefficient = {results_yer.params['is_houthi']:.3f} (p={results_yer.pvalues['is_houthi']:.3f})")
        print(f"  - USD model: Houthi coefficient = {results_usd.params['is_houthi']:.3f} (p={results_usd.pvalues['is_houthi']:.3f})")
    
    def test_arbitrage_condition_violation(self, sample_price_data: pd.DataFrame, zone_exchange_rates: Dict):
        """Test that arbitrage conditions are violated due to currency fragmentation."""
        # Calculate implied exchange rates from price differentials
        wheat_prices = sample_price_data[sample_price_data['commodity'] == 'Wheat Flour'].copy()
        
        # Get average prices by zone
        houthi_price_yer = wheat_prices[wheat_prices['currency_zone'] == 'houthi']['price_yer'].mean()
        gov_price_yer = wheat_prices[wheat_prices['currency_zone'] == 'government']['price_yer'].mean()
        
        # Calculate implied exchange rate if arbitrage held
        # If arbitrage worked: P_houthi_YER / E_houthi = P_gov_YER / E_gov
        # Implied E_houthi = P_houthi_YER * E_gov / P_gov_YER
        
        actual_houthi_rate = float(zone_exchange_rates[CurrencyZone.HOUTHI])
        actual_gov_rate = float(zone_exchange_rates[CurrencyZone.GOVERNMENT])
        
        implied_houthi_rate = (houthi_price_yer * actual_gov_rate) / gov_price_yer
        
        # Calculate deviation from arbitrage
        arbitrage_violation_pct = abs(implied_houthi_rate - actual_houthi_rate) / actual_houthi_rate * 100
        
        assert arbitrage_violation_pct > 50, \
            f"Arbitrage violation too small: {arbitrage_violation_pct:.1f}% (expected > 50%)"
        
        print(f"✓ Arbitrage condition violated by {arbitrage_violation_pct:.1f}%")
        print(f"  - Actual Houthi rate: {actual_houthi_rate:.0f} YER/USD")
        print(f"  - Implied by arbitrage: {implied_houthi_rate:.0f} YER/USD")
    
    def test_exchange_rate_fully_explains_differential(self, sample_price_data: pd.DataFrame, zone_exchange_rates: Dict):
        """Test that exchange rate differences fully explain price differentials."""
        # Add exchange rates to data
        sample_price_data['exchange_rate'] = sample_price_data['currency_zone'].map({
            'houthi': float(zone_exchange_rates[CurrencyZone.HOUTHI]),
            'government': float(zone_exchange_rates[CurrencyZone.GOVERNMENT]),
            'contested': float(zone_exchange_rates[CurrencyZone.CONTESTED])
        })
        
        # Model with exchange rate interaction
        sample_price_data['log_price'] = np.log(sample_price_data['price_yer'])
        sample_price_data['log_exchange_rate'] = np.log(sample_price_data['exchange_rate'])
        sample_price_data['is_houthi'] = (sample_price_data['currency_zone'] == 'houthi').astype(int)
        
        # Full model: Price = f(zone, exchange_rate, zone × exchange_rate)
        model = PooledPanelModel()
        X = sample_price_data[['is_houthi', 'log_exchange_rate']]
        X['houthi_x_exchange'] = X['is_houthi'] * X['log_exchange_rate']
        y = sample_price_data['log_price']
        
        results = model.fit(X, y, entity_effects=True, time_effects=True)
        
        # The Houthi dummy should become insignificant when controlling for exchange rates
        assert results.pvalues['is_houthi'] > 0.10, \
            "Zone effect remains significant after controlling for exchange rates"
        
        # Exchange rate should be highly significant
        assert results.pvalues['log_exchange_rate'] < 0.01, \
            "Exchange rate effect not significant"
        
        print(f"✓ Exchange rate fully explains price differentials:")
        print(f"  - Zone effect after controlling for exchange rate: p={results.pvalues['is_houthi']:.3f} (insignificant)")
        print(f"  - Exchange rate effect: β={results.params['log_exchange_rate']:.3f}, p={results.pvalues['log_exchange_rate']:.3f}")
    
    def test_temporal_stability_of_mechanism(self, sample_price_data: pd.DataFrame, zone_exchange_rates: Dict):
        """Test that the exchange rate mechanism is stable over time."""
        # Test the mechanism in different time periods
        sample_price_data['year'] = pd.to_datetime(sample_price_data['date']).dt.year
        
        zone_service = CurrencyZoneService()
        sample_price_data['price_usd'] = sample_price_data.apply(
            lambda row: float(zone_service.convert_price_to_usd(
                Decimal(str(row['price_yer'])),
                CurrencyZone(row['currency_zone']),
                zone_exchange_rates
            )), axis=1
        )
        
        # Test for each year
        years = sample_price_data['year'].unique()
        paradox_reversal_confirmed = []
        
        for year in years:
            year_data = sample_price_data[sample_price_data['year'] == year]
            
            # Check YER paradox
            yer_means = year_data.groupby('currency_zone')['price_yer'].mean()
            paradox_in_yer = yer_means['houthi'] < yer_means['government']
            
            # Check USD truth
            usd_means = year_data.groupby('currency_zone')['price_usd'].mean()
            truth_in_usd = usd_means['houthi'] > usd_means['government']
            
            paradox_reversal_confirmed.append(paradox_in_yer and truth_in_usd)
        
        # Mechanism should be stable across all periods
        assert all(paradox_reversal_confirmed), \
            "Exchange rate mechanism not stable across all time periods"
        
        print(f"✓ Exchange rate mechanism stable across {len(years)} time periods")


@pytest.mark.integration
class TestH1Integration:
    """Integration tests for H1 hypothesis with real data structure."""
    
    async def test_h1_with_currency_aware_processor(self):
        """Test H1 using the actual CurrencyAwareWFPProcessor."""
        # This would use real data files in integration testing
        processor = CurrencyAwareWFPProcessor(enable_zone_conversion=True)
        
        # Process sample data
        markets, prices, rates, metrics = await processor.process(
            "tests/fixtures/wfp_sample.csv",
            zone_aware=True
        )
        
        # Verify metrics show paradox resolution
        assert 'price_paradox_examples' in metrics
        assert len(metrics['price_paradox_examples']) > 0
        
        # Check that examples show conflict premium
        for example in metrics['price_paradox_examples']:
            assert example['premium_pct'] > 0, \
                f"Example doesn't show conflict premium: {example}"
        
        print(f"✓ Integration test confirms {len(metrics['price_paradox_examples'])} paradox examples")