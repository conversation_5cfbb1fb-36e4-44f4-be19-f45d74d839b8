"""
Test H5: Cross-Border Arbitrage Hypothesis

For tradeable goods, price differentials between markets should equal 
transport costs plus exchange rate differentials. The exchange rate 
component should have a coefficient of approximately 1 for perfectly 
tradeable goods.
"""

import pytest
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import statsmodels.api as sm

from src.core.domain.market.value_objects import MarketPair, Currency
from src.core.domain.market.currency_zones import CurrencyZone
from src.infrastructure.estimators.panel_estimators import PanelOLS


class TestH5CrossBorderArbitrage:
    """Test suite for Hypothesis H5: Cross-border arbitrage conditions."""
    
    @pytest.fixture
    def market_pair_data(self) -> pd.DataFrame:
        """Create market pair data for arbitrage testing."""
        # Create data for market pairs across currency zones
        pairs = []
        dates = pd.date_range('2023-01-01', '2024-12-31', freq='W')
        
        # Market pairs
        market_pairs = [
            ('Sana\'a Central', 'houthi', 'Aden Port', 'government', 850),  # km
            ('Sa\'ada Market', 'houthi', 'Marib City', 'government', 150),
            ('Sana\'a Central', 'houthi', 'Taiz Market', 'contested', 250),
            ('Aden Port', 'government', 'Mukalla', 'government', 480),
            ('Sana\'a Central', 'houthi', 'Hajjah', 'houthi', 120)
        ]
        
        commodities = ['Wheat Flour', 'Rice (Imported)', 'Sugar', 'Fuel (Diesel)']
        
        for date in dates:
            for market1, zone1, market2, zone2, distance in market_pairs:
                for commodity in commodities:
                    # Base prices with some randomness
                    base_price = np.random.normal(500, 50)
                    
                    # Add transport cost component
                    transport_cost = distance * 0.5  # 0.5 YER per km
                    
                    # Add exchange rate differential component
                    if zone1 == 'houthi' and zone2 == 'government':
                        exchange_diff = base_price * 2.74  # ~(2000-535)/535
                    elif zone1 == 'government' and zone2 == 'houthi':
                        exchange_diff = -base_price * 0.73  # ~(535-2000)/2000
                    elif zone1 == 'houthi' and zone2 == 'contested':
                        exchange_diff = base_price * 1.24  # ~(1200-535)/535
                    else:
                        exchange_diff = 0
                    
                    # Calculate prices with noise
                    price1 = base_price + np.random.normal(0, 20)
                    price2 = price1 + transport_cost + exchange_diff + np.random.normal(0, 30)
                    
                    pairs.append({
                        'date': date,
                        'market1': market1,
                        'market2': market2,
                        'zone1': zone1,
                        'zone2': zone2,
                        'commodity': commodity,
                        'price1_yer': price1,
                        'price2_yer': price2,
                        'distance_km': distance,
                        'transport_cost': transport_cost,
                        'same_zone': zone1 == zone2
                    })
        
        return pd.DataFrame(pairs)
    
    @pytest.fixture
    def exchange_rates(self) -> Dict[str, float]:
        """Zone-specific exchange rates."""
        return {
            'houthi': 535,
            'government': 2000,
            'contested': 1200
        }
    
    def test_price_differential_decomposition(self, market_pair_data: pd.DataFrame, exchange_rates: Dict):
        """Test that price differentials can be decomposed into transport and exchange components."""
        # Calculate price differential
        market_pair_data['price_diff'] = market_pair_data['price2_yer'] - market_pair_data['price1_yer']
        
        # Calculate exchange rate differential
        market_pair_data['exchange_rate1'] = market_pair_data['zone1'].map(exchange_rates)
        market_pair_data['exchange_rate2'] = market_pair_data['zone2'].map(exchange_rates)
        market_pair_data['exchange_diff'] = (
            (market_pair_data['exchange_rate2'] - market_pair_data['exchange_rate1']) / 
            market_pair_data['exchange_rate1'] * market_pair_data['price1_yer']
        )
        
        # Regression: price_diff = β₁*transport_cost + β₂*exchange_diff + ε
        X = market_pair_data[['transport_cost', 'exchange_diff']]
        X = sm.add_constant(X)
        y = market_pair_data['price_diff']
        
        model = sm.OLS(y, X)
        results = model.fit()
        
        # Test β₁ ≈ 1 (full pass-through of transport costs)
        transport_coef = results.params['transport_cost']
        transport_se = results.bse['transport_cost']
        
        assert 0.8 < transport_coef < 1.2, \
            f"Transport cost coefficient {transport_coef:.3f} not close to 1"
        
        # Test β₂ ≈ 1 (full pass-through of exchange differentials)
        exchange_coef = results.params['exchange_diff']
        exchange_se = results.bse['exchange_diff']
        
        assert 0.8 < exchange_coef < 1.2, \
            f"Exchange differential coefficient {exchange_coef:.3f} not close to 1"
        
        # Both should be highly significant
        assert results.pvalues['transport_cost'] < 0.01
        assert results.pvalues['exchange_diff'] < 0.01
        
        print(f"✓ Price differential decomposition confirmed:")
        print(f"  - Transport cost coefficient: {transport_coef:.3f} (SE: {transport_se:.3f})")
        print(f"  - Exchange diff coefficient: {exchange_coef:.3f} (SE: {exchange_se:.3f})")
        print(f"  - R-squared: {results.rsquared:.3f}")
    
    def test_arbitrage_stronger_within_zones(self, market_pair_data: pd.DataFrame):
        """Test that arbitrage works better within currency zones than across zones."""
        # Calculate price correlation for market pairs
        correlations = []
        
        for (market1, market2), group in market_pair_data.groupby(['market1', 'market2']):
            if len(group) > 10:  # Need sufficient observations
                corr = group[['price1_yer', 'price2_yer']].corr().iloc[0, 1]
                same_zone = group['same_zone'].iloc[0]
                distance = group['distance_km'].iloc[0]
                
                correlations.append({
                    'correlation': corr,
                    'same_zone': same_zone,
                    'distance': distance
                })
        
        corr_df = pd.DataFrame(correlations)
        
        # Average correlation within vs across zones
        within_zone_corr = corr_df[corr_df['same_zone']]['correlation'].mean()
        across_zone_corr = corr_df[~corr_df['same_zone']]['correlation'].mean()
        
        assert within_zone_corr > across_zone_corr, \
            f"Within-zone correlation ({within_zone_corr:.3f}) not higher than across-zone ({across_zone_corr:.3f})"
        
        # Regression to control for distance
        X = pd.get_dummies(corr_df[['same_zone']], columns=['same_zone'], drop_first=True)
        X['distance'] = corr_df['distance']
        X['distance_sq'] = corr_df['distance'] ** 2
        X = sm.add_constant(X)
        y = corr_df['correlation']
        
        model = sm.OLS(y, X)
        results = model.fit()
        
        # Same zone effect should be positive and significant
        same_zone_effect = results.params.get('same_zone_True', 0)
        assert same_zone_effect > 0.1, \
            f"Same zone effect too small: {same_zone_effect:.3f}"
        
        print(f"✓ Arbitrage stronger within currency zones:")
        print(f"  - Within-zone correlation: {within_zone_corr:.3f}")
        print(f"  - Across-zone correlation: {across_zone_corr:.3f}")
        print(f"  - Same-zone effect (controlling for distance): {same_zone_effect:.3f}")
    
    def test_tradeable_vs_nontradeable_goods(self, market_pair_data: pd.DataFrame):
        """Test that arbitrage coefficient is higher for tradeable goods."""
        # Classify goods by tradeability
        tradeable = ['Wheat Flour', 'Rice (Imported)', 'Sugar']
        less_tradeable = ['Fuel (Diesel)']  # Harder to transport/store
        
        market_pair_data['is_tradeable'] = market_pair_data['commodity'].isin(tradeable)
        
        # Calculate exchange differential
        market_pair_data['price_diff'] = market_pair_data['price2_yer'] - market_pair_data['price1_yer']
        
        # Test arbitrage separately for tradeable vs non-tradeable
        results_by_type = {}
        
        for is_tradeable, group in market_pair_data.groupby('is_tradeable'):
            # Simple arbitrage test: correlation between price differentials and transport costs
            X = group[['transport_cost']]
            X = sm.add_constant(X)
            y = group['price_diff']
            
            model = sm.OLS(y, X)
            results = model.fit()
            
            good_type = 'tradeable' if is_tradeable else 'less_tradeable'
            results_by_type[good_type] = {
                'coefficient': results.params['transport_cost'],
                'r_squared': results.rsquared,
                'pvalue': results.pvalues['transport_cost']
            }
        
        # Tradeable goods should show stronger arbitrage
        assert results_by_type['tradeable']['r_squared'] > results_by_type['less_tradeable']['r_squared'], \
            "Tradeable goods don't show stronger arbitrage relationship"
        
        print(f"✓ Arbitrage stronger for tradeable goods:")
        print(f"  - Tradeable R²: {results_by_type['tradeable']['r_squared']:.3f}")
        print(f"  - Less tradeable R²: {results_by_type['less_tradeable']['r_squared']:.3f}")
    
    def test_exchange_rate_passthrough_by_commodity(self, market_pair_data: pd.DataFrame, exchange_rates: Dict):
        """Test exchange rate pass-through varies by commodity characteristics."""
        # Calculate exchange differential
        market_pair_data['exchange_rate1'] = market_pair_data['zone1'].map(exchange_rates)
        market_pair_data['exchange_rate2'] = market_pair_data['zone2'].map(exchange_rates)
        market_pair_data['exchange_diff_pct'] = (
            (market_pair_data['exchange_rate2'] - market_pair_data['exchange_rate1']) / 
            market_pair_data['exchange_rate1'] * 100
        )
        
        # Calculate price differential percentage
        market_pair_data['price_diff_pct'] = (
            (market_pair_data['price2_yer'] - market_pair_data['price1_yer']) / 
            market_pair_data['price1_yer'] * 100
        )
        
        # Test pass-through by commodity
        passthrough_rates = {}
        
        for commodity, group in market_pair_data.groupby('commodity'):
            # Only use cross-zone pairs
            cross_zone = group[~group['same_zone']]
            
            if len(cross_zone) > 20:
                X = cross_zone[['exchange_diff_pct']]
                X = sm.add_constant(X)
                y = cross_zone['price_diff_pct']
                
                model = sm.OLS(y, X)
                results = model.fit()
                
                passthrough_rates[commodity] = {
                    'coefficient': results.params['exchange_diff_pct'],
                    'se': results.bse['exchange_diff_pct'],
                    'pvalue': results.pvalues['exchange_diff_pct']
                }
        
        # Verify pass-through rates are significant and vary by commodity
        significant_count = sum(1 for r in passthrough_rates.values() if r['pvalue'] < 0.05)
        assert significant_count >= 3, \
            f"Only {significant_count} commodities show significant exchange rate pass-through"
        
        # Check variation in pass-through rates
        coefficients = [r['coefficient'] for r in passthrough_rates.values()]
        coef_std = np.std(coefficients)
        assert coef_std > 0.1, \
            f"Pass-through rates too similar across commodities (std={coef_std:.3f})"
        
        print(f"✓ Exchange rate pass-through varies by commodity:")
        for commodity, results in passthrough_rates.items():
            print(f"  - {commodity}: {results['coefficient']:.3f} (p={results['pvalue']:.3f})")
    
    def test_arbitrage_breakdown_threshold(self, market_pair_data: pd.DataFrame, exchange_rates: Dict):
        """Test that arbitrage breaks down beyond certain exchange rate differentials."""
        # Calculate exchange rate gap
        market_pair_data['exchange_rate1'] = market_pair_data['zone1'].map(exchange_rates)
        market_pair_data['exchange_rate2'] = market_pair_data['zone2'].map(exchange_rates)
        market_pair_data['exchange_gap'] = abs(
            market_pair_data['exchange_rate2'] - market_pair_data['exchange_rate1']
        )
        
        # Bin by exchange rate gap
        market_pair_data['gap_category'] = pd.cut(
            market_pair_data['exchange_gap'],
            bins=[0, 100, 500, 1000, 2000],
            labels=['minimal', 'low', 'medium', 'high']
        )
        
        # Calculate price correlation by gap category
        correlation_by_gap = market_pair_data.groupby('gap_category').apply(
            lambda x: x[['price1_yer', 'price2_yer']].corr().iloc[0, 1]
        )
        
        # Correlation should decrease with larger gaps
        assert correlation_by_gap['minimal'] > correlation_by_gap['high'], \
            "Price correlation doesn't decrease with exchange rate gap"
        
        # Test for threshold effect
        threshold_gaps = [500, 1000, 1500]
        best_threshold = None
        best_r2_improvement = 0
        
        for threshold in threshold_gaps:
            market_pair_data['high_gap'] = (market_pair_data['exchange_gap'] > threshold).astype(int)
            
            # Model with threshold interaction
            X = market_pair_data[['transport_cost', 'high_gap']]
            X['transport_x_gap'] = X['transport_cost'] * X['high_gap']
            X = sm.add_constant(X)
            y = market_pair_data['price2_yer'] - market_pair_data['price1_yer']
            
            model = sm.OLS(y, X)
            results = model.fit()
            
            # Check if interaction is significant
            if results.pvalues['transport_x_gap'] < 0.05:
                r2_improvement = results.rsquared
                if r2_improvement > best_r2_improvement:
                    best_threshold = threshold
                    best_r2_improvement = r2_improvement
        
        assert best_threshold is not None, \
            "No significant threshold effect found for arbitrage breakdown"
        
        print(f"✓ Arbitrage breakdown confirmed:")
        print(f"  - Correlation by exchange gap: {correlation_by_gap.to_dict()}")
        print(f"  - Optimal threshold: {best_threshold} YER/USD gap")


@pytest.mark.parametrize("commodity,expected_passthrough", [
    ("Wheat Flour", 0.85),      # High tradeability → high pass-through
    ("Rice (Imported)", 0.90),   # Imported → very high pass-through
    ("Fuel (Diesel)", 0.60),     # Some constraints → moderate pass-through
    ("Vegetables", 0.30),        # Perishable → low pass-through
])
def test_commodity_specific_passthrough(commodity: str, expected_passthrough: float):
    """Test that pass-through rates match commodity characteristics."""
    # This would be implemented with real data
    # For now, we document expected behavior
    pass