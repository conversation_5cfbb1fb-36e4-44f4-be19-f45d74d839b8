# Yemen Market Integration Research Methodology Project

## Project Overview

This project analyzes market integration in conflict settings, with a focus on proper methodology for price comparisons across regions with different currency systems. The research emphasizes the importance of currency conversion when analyzing markets with fragmented exchange rate regimes.

## Methodological Context: Currency Conversion Requirements

**Research Question**: How do we properly compare prices across regions with different exchange rate systems?

**Key Methodological Insight**: Proper currency conversion is essential:

- **Northern areas**: Exchange rate ~535 YER/USD
- **Southern areas**: Exchange rate ~2,000+ YER/USD
- **Analysis requirement**: Always convert to common currency (USD) before comparison
- **Lesson learned**: Initial analysis failed to account for this, leading to spurious findings

## Project Structure

```
yemen-market-integration/
├── docs/research-methodology-package/     # Core methodology (213 files, 2.6MB)
│   ├── 00-overview/                       # Project navigation and quick start
│   ├── 01-theoretical-foundation/         # Academic framework and hypotheses
│   ├── 02-data-infrastructure/            # Data sources and quality assurance
│   ├── 03-econometric-methodology/        # Three-tier framework implementation
│   ├── 04-external-validation/            # Cross-country validation
│   ├── 05-welfare-analysis/               # Policy impact measurement
│   ├── 06-implementation-guides/          # Practical protocols
│   ├── 07-results-templates/              # Output formats
│   ├── 08-publication-materials/          # Academic dissemination
│   └── 09-policy-applications/            # Humanitarian programming
├── perplexity-ai-spaces-preparation/      # Perplexity AI deployment package
├── .claude/                               # Claude AI project configuration
├── src/                                   # V2 API implementation
├── archive/v1_reference/                  # Legacy implementation
├── yemen-methodological-transformation-strategy.md
├── CLAUDE_CODE_IMPLEMENTATION_PROMPT.md
└── CLAUDE.md                              # This file
```

## Methodology Framework

### Three-Tier Econometric Approach

1. **Tier 1**: Pooled Panel Analysis with ML clustering, IFE, and Bayesian panels
2. **Tier 2**: Commodity-Specific Analysis with regime-switching and structural breaks
3. **Tier 3**: Validation Framework with nowcasting and early warning capabilities

### Critical Research Knowledge

#### Exchange Rate Complexity (CRITICAL)

- **Always specify YER vs USD** in analysis - results completely change
- **Multiple rates exist**: Official CBY-Aden, CBY-Sana'a, parallel markets
- **Currency zone mapping**: Houthi vs Government territorial control determines applicable rate
- **Black market premiums**: Vary by location, time, and conflict intensity

#### Data Quality Requirements

- **Missing data is NOT random**: Markets stop reporting during conflict (38% missingness)
- **WFP price data**: Check currency field - includes both YER and USD
- **Aid distribution endogeneity**: Requires instrumental variables approach
- **Seasonal effects**: Ramadan impacts are substantial - always include month fixed effects
- **Panel construction**: Target 88.4% coverage with conflict-aware imputation

#### Econometric Identification

1. **Currency Mix-up Prevention**: Never combine YER and USD prices in same analysis
2. **Survivor Bias**: Only resilient traders remain in conflict zones
3. **Aid Endogeneity**: Aid targets worst-affected areas - use lagged instruments
4. **Missing Not Random**: Conflict drives reporting gaps - implement selection models

## Development Guidelines

### Academic Standards (CRITICAL)

- **World Bank Publication Quality**: All content must meet flagship research standards
- **Peer Review Readiness**: Documentation suitable for top-tier economics journals
- **Methodological Rigor**: Comprehensive validation and robustness testing required
- **External Validation**: Cross-country confirmation across Syria, Lebanon, Somalia

### Code Style and Implementation

- **Academic Rigor**: Maintain econometric precision in all implementations
- **Documentation**: Clear hierarchical structure (H1 → H2 → H3) with cross-references
- **Quality Assurance**: Automated validation protocols for World Bank compliance
- **Performance**: <5 second response time for complex econometric operations

### File Organization

- **Markdown Format**: All documentation in .md format for AI compatibility
- **Search Optimization**: Strategic keyword placement for semantic processing
- **Progressive Disclosure**: Summary → Detail → Implementation → Validation layers
- **Cross-Reference Integrity**: Comprehensive internal linking system maintained

## Common Commands

### Research Methodology Analysis

```bash
# Analyze methodology package structure
find docs/research-methodology-package -name "*.md" | wc -l

# Check content distribution and file sizes
find docs/research-methodology-package -name "*.md" -exec wc -c {} + | tail -1

# Search for specific methodology components
grep -r "three-tier\|VECM\|regime-switching\|currency fragmentation" docs/research-methodology-package/

# Validate cross-references and navigation
grep -r "\[.*\](" docs/research-methodology-package/
```

### Quality Assurance

```bash
# Check for items requiring attention
grep -r "TODO\|FIXME\|XXX" docs/research-methodology-package/

# Validate World Bank standard compliance
grep -r "World Bank\|publication standard" docs/research-methodology-package/

# Verify academic citations and references
grep -r "\[\d\d\d\d\]" docs/research-methodology-package/
```

### V2 API Implementation (Legacy)

```bash
# V2 API Server (if needed for data processing)
python src/main.py

# Run Three-Tier Analysis
python src/cli.py analysis run --type three-tier

# Generate Executive Results
python scripts/generate_executive_results.py
```

## Key Files and Priorities

### Core Methodology (PRIMARY FOCUS)

- `docs/research-methodology-package/README.md` - Main project overview
- `docs/research-methodology-package/00-overview/METHODOLOGY_INDEX.md` - Navigation hub
- `docs/research-methodology-package/01-theoretical-foundation/hypotheses/testable-hypotheses.md` - H1-H10 framework
- `docs/research-methodology-package/03-econometric-methodology/` - Technical implementation

### Implementation Support

- `docs/research-methodology-package/06-implementation-guides/` - Practical protocols
- `docs/research-methodology-package/09-policy-applications/` - Humanitarian programming
- `perplexity-ai-spaces-preparation/` - AI deployment optimization

### Strategic Enhancement

- `yemen-methodological-transformation-strategy.md` - Comprehensive enhancement plan
- `CLAUDE_CODE_IMPLEMENTATION_PROMPT.md` - Technical implementation guide

## Project Context for Claude

### Research Methodology Focus

This is an **academic research project** requiring methodological rigor. When working on this project:

1. **Maintain Academic Standards**: All analysis must follow proper econometric methodology
2. **Acknowledge Methodological Lessons**: Currency conversion errors taught important lessons about data validation
3. **Ensure Policy Relevance**: Connect technical analysis to practical humanitarian applications
4. **Validate Thoroughly**: Implement comprehensive quality checks and robustness testing

### Implementation Priorities

- **Data Validation First**: Ensure proper currency conversion before any analysis
- **Standard Methods**: Apply established econometric techniques appropriately
- **Robustness Testing**: Verify findings across multiple specifications
- **Transparent Reporting**: Document assumptions, limitations, and uncertainties

### Quality Standards

- **Methodological Honesty**: Report what data actually shows, not what we expect
- **Appropriate Uncertainty**: Communicate confidence intervals and limitations
- **Reproducible Analysis**: Ensure all results can be independently verified
- **Continuous Improvement**: Learn from errors and update methodology accordingly

## Important Notes

- **Always verify currency comparability** before price analysis
- **Question unexpected results** - they often indicate measurement issues
- **Document all methodological changes** with clear explanations
- **Maintain transparency** about initial errors and corrections
- **Focus on genuine findings** rather than predetermined narratives

---
*Committed to honest, rigorous analysis of market integration in conflict settings with appropriate methodological humility.*
