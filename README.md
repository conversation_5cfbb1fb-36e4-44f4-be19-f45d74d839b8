# Yemen Market Integration Platform

An econometric research system for analyzing market integration in conflict settings, with emphasis on proper currency conversion in multi-exchange rate environments.

## 🚀 Key Methodological Focus

**Currency Conversion Requirements**: Different regions use different exchange rates, requiring careful currency conversion for valid price comparisons.

**Key Insight**: Price comparisons must account for currency differences:
- Northern areas: Exchange rate ~535 YER/USD
- Southern areas: Exchange rate ~2,000+ YER/USD
- Implication: Currency-aware analysis essential for accurate humanitarian programming

## 📊 Quick Start

```bash
# Clone and setup
git clone https://github.com/your-org/yemen-market-integration.git
cd yemen-market-integration
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# Run analysis
python scripts/run_analysis.py

# Start API server
python src/main.py

# Run hypothesis tests
python src/cli.py hypothesis test H1
```

## 🔬 Core Components

### Research Methodology
- **13 Hypothesis Tests**: H1-H10, S1, N1, P1
- **Three-Tier Framework**: Pooled → Commodity-Specific → Validation
- **Currency-Aware Analysis**: Dual exchange rate handling

### Technical Stack
- **API**: FastAPI with async processing
- **Database**: PostgreSQL with TimescaleDB
- **Caching**: Redis
- **Queue**: Celery for background tasks
- **Monitoring**: Prometheus + Grafana

### Production Features
- Real-time SSE streaming
- Horizontal autoscaling
- 99.9% uptime capability
- <100ms health checks

## 📁 Project Structure

```
yemen-market-integration/
├── src/                    # Core application code
├── docs/                   # User documentation
│   └── research-methodology-package/  # Academic framework
├── deployment/             # Production configs
├── tests/                  # Test suite
├── examples/               # Usage examples
└── notebooks/              # Analysis notebooks
```

## 📖 Documentation

- **[Project Overview](PROJECT_OVERVIEW.md)** - Comprehensive introduction
- **[Research Methodology](docs/research-methodology-package/README.md)** - Academic framework
- **[API Documentation](docs/11-v2-implementation/api/README.md)** - Technical reference
- **[Deployment Guide](docs/11-v2-implementation/deployment/production-deployment-guide.md)** - Production setup

## 🔧 Development

```bash
# Run tests
pytest tests/

# Run linting
make lint

# Build Docker images
docker-compose build

# Deploy to production
./deployment/scripts/deploy.sh production deploy
```

## 📈 Impact

- **292-540%** price increases tracked across commodities
- **46,200+** price observations analyzed
- **21** markets monitored
- **88.4%** data coverage despite conflict

## 🤝 Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines and [CLAUDE.md](CLAUDE.md) for AI pair programming standards.

## 📜 License

This project is part of humanitarian research efforts. See LICENSE for details.

---
*Transforming conflict economics research into actionable humanitarian insights*