# Production Dockerfile for Yemen Market Integration Worker
# For background hypothesis testing and long-running analysis tasks

# Stage 1: Builder (same as API)
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /build

# Copy requirements
COPY requirements.txt .
COPY pyproject.toml .
COPY setup.py .

# Create virtual environment and install dependencies
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

RUN pip install --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir celery[redis]==5.3.4 flower==2.0.1

# Stage 2: Runtime
FROM python:3.11-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Set environment variables
ENV PATH="/opt/venv/bin:$PATH" \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    CELERY_LOG_LEVEL=info

WORKDIR /app

# Copy application code
COPY --chown=appuser:appuser src/ ./src/
COPY --chown=appuser:appuser config/ ./config/

# Create necessary directories
RUN mkdir -p /app/logs /app/data/hypothesis_results /app/data/cache && \
    chown -R appuser:appuser /app

# Health check for worker
HEALTHCHECK --interval=30s --timeout=3s --start-period=40s --retries=3 \
    CMD celery -A src.infrastructure.tasks.celery_app inspect ping || exit 1

USER appuser

# Worker command with autoscaling
CMD ["celery", \
     "-A", "src.infrastructure.tasks.celery_app", \
     "worker", \
     "--loglevel=info", \
     "--concurrency=4", \
     "--max-tasks-per-child=100", \
     "--autoscale=10,3", \
     "--queue=hypothesis,analysis,default", \
     "--heartbeat-interval=30", \
     "--without-gossip", \
     "--without-mingle", \
     "--without-heartbeat"]