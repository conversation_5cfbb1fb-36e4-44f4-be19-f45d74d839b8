# HDX Datasets for Yemen Econometric Analysis

## Executive Summary

This report documents available datasets on the Humanitarian Data Exchange (HDX) that can support refined econometric analysis of Yemen market integration. We identified **247 Yemen-related datasets** on HDX and successfully downloaded key datasets from the HDX Humanitarian API (HAPI) system.

### Key Findings

1. **Exchange Rate Data**: Limited direct exchange rate data available. The HDX HAPI system provides some currency-related information, but official/parallel market rates need supplementation from other sources.

2. **Aid Distribution (3W)**: Operational presence data available with 1,000+ organizations tracked across Yemen governorates, providing good coverage for aid flow analysis.

3. **Food Price Data**: Comprehensive price monitoring data available with 18 governorates covered, multiple commodities tracked, and monthly updates from January 2024 to March 2025.

4. **Conflict Data**: Recent conflict event data (2025) available at governorate level with event types and fatality counts, suitable for creating conflict intensity variables.

5. **Currency Zones**: ACAPS control area shapefiles available (71 files) with monthly updates, essential for defining currency zones.

6. **Humanitarian Access**: Limited structured data, but 8 datasets identified including access constraints and hard-to-reach districts.

## Dataset Overview

### Total Yemen Datasets by Category:
- **Exchange Rates**: 24 datasets (mostly indirect indicators)
- **Aid Distribution**: 40 datasets (including 3W and funding data)
- **Food Prices**: 67 datasets (WFP, FAO, and market monitoring)
- **Currency Zones**: 7 datasets (including ACAPS control areas)
- **Access Constraints**: 8 datasets
- **Trade Routes**: 21 datasets (ports, roads, logistics)
- **Economic Data**: 51 datasets (World Bank, IMF indicators)

## Downloaded and Analyzed Datasets

### 1. Food Security & Market Prices

**Dataset**: Food Security, Nutrition & Poverty: Food Prices & Market Monitor for Yemen
- **Coverage**: 18 governorates
- **Time Period**: January 2024 - March 2025
- **Update Frequency**: Monthly
- **Key Variables**:
  - Market-level prices in local currency
  - Commodity categories and names
  - Geographic coordinates (lat/lon)
  - Price types and flags
- **Econometric Potential**: HIGH - Direct price data for market integration analysis

### 2. Conflict Events

**Dataset**: Coordination & Context: Conflict Events for Yemen
- **Coverage**: 23 governorates
- **Time Period**: January 2025 - March 2025 (recent data)
- **Key Variables**:
  - Event types by location
  - Event counts and fatalities
  - Monthly aggregation
- **Econometric Potential**: HIGH - Can create conflict intensity variables and spatial spillover measures

### 3. Humanitarian Operations (3W)

**Dataset**: Coordination & Context: Operational Presence for Yemen
- **Coverage**: Multiple governorates
- **Organizations**: 1,000+ entries
- **Key Variables**:
  - Organization names and types
  - Sector codes and activities
  - Geographic presence
- **Econometric Potential**: MEDIUM - Useful for aid distribution proxies and market intervention analysis

### 4. Humanitarian Needs

**Dataset**: Affected People: Humanitarian Needs for Yemen
- **Coverage**: National and sub-national
- **Key Variables**:
  - Population in need by sector
  - Category breakdowns
  - Administrative levels
- **Econometric Potential**: MEDIUM - Context for market demand and vulnerability

### 5. Funding Data

**Dataset**: Coordination & Context: Funding for Yemen
- **Time Period**: 2001-2027 (historical and planned)
- **Key Variables**:
  - Appeal requirements and funding in USD
  - Funding percentages
  - Appeal types
- **Econometric Potential**: LOW-MEDIUM - Macro-level context, limited geographic detail

## Data Gaps and Recommendations

### Critical Data Gaps

1. **Exchange Rate Data**
   - **Gap**: No direct official or parallel market exchange rate time series
   - **Solution**: 
     - Extract from WFP price data (USD prices)
     - Web scrape Central Bank of Yemen reports
     - Use OCHA situation reports for parallel rates

2. **Currency Zone Boundaries**
   - **Gap**: Need time-varying boundaries linked to control areas
   - **Solution**:
     - Process ACAPS monthly shapefiles
     - Create governorate-month panel of currency zones
     - Link to market locations

3. **Port/Trade Route Functionality**
   - **Gap**: No structured time series of port operations
   - **Solution**:
     - Combine OCHA logistics cluster reports
     - Use conflict events near ports as proxy
     - Extract from situation reports

4. **Cash and Voucher Assistance**
   - **Gap**: Limited granular CVA distribution data
   - **Solution**:
     - Aggregate from 3W operational data
     - Request specific datasets from Cash Working Group
     - Use funding data as proxy

### Integration Strategy

1. **Phase 1: Core Data Integration**
   - Integrate WFP food price data (already in system)
   - Process HDX HAPI price monitoring data
   - Add conflict event data at governorate-month level
   - Create currency zone indicators from ACAPS

2. **Phase 2: Enhanced Variables**
   - Develop exchange rate series from multiple sources
   - Create aid distribution intensity measures
   - Build market access indices
   - Generate conflict spillover variables

3. **Phase 3: Advanced Features**
   - Time-varying currency zone boundaries
   - Port functionality indicators
   - Humanitarian access scores
   - Trade route disruption measures

## Technical Implementation

### Data Pipeline Requirements

```python
# Example integration for HDX HAPI data
class HDXHAPIProcessor:
    def __init__(self):
        self.hdx_client = HDXClient()
        
    def process_price_data(self):
        # Read HDX HAPI price data
        df = pd.read_csv("data/interim/hdx_hapi/food_prices.csv")
        
        # Standardize to match existing schema
        df['date'] = pd.to_datetime(df['reference_period_start'])
        df['market'] = df['market_name']
        df['commodity'] = df['commodity_name']
        df['price_local'] = df['price']
        
        # Add currency zone based on control areas
        df = self.assign_currency_zones(df)
        
        return df
        
    def process_conflict_data(self):
        # Aggregate to governorate-month level
        # Create intensity measures
        # Generate spatial lags
        pass
```

### Quality Assurance

1. **Temporal Consistency**: Align all datasets to monthly frequency
2. **Geographic Harmonization**: Use Yemen p-codes for all joins
3. **Missing Data Strategy**: Document and impute where appropriate
4. **Validation**: Cross-check overlapping indicators across sources

## Conclusion

HDX provides substantial data resources for Yemen market integration analysis. While some critical gaps exist (particularly exchange rates and currency zones), the available data on prices, conflict, and humanitarian operations provides a strong foundation. The recommended phased integration approach will maximize the value of these datasets while maintaining analytical rigor.

### Next Steps

1. Download remaining ACAPS control area shapefiles
2. Extract exchange rate proxies from price data
3. Develop automated update pipeline for HDX HAPI data
4. Create comprehensive data dictionary
5. Implement quality checks and validation procedures

### Data Citation

All HDX data should be cited according to their respective licenses:
- HDX HAPI: [HDX Humanitarian API Data](https://data.humdata.org/organization/hdx-hapi)
- ACLED: [Armed Conflict Location & Event Data Project](https://acleddata.com/)
- WFP: [World Food Programme](https://data.humdata.org/organization/wfp)
- OCHA: [UN Office for the Coordination of Humanitarian Affairs](https://data.humdata.org/organization/ocha-yemen)