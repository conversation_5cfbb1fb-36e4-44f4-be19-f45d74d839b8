#!/usr/bin/env python3
"""
Example of using the performance optimization framework.

This script demonstrates how to:
- Benchmark model operations
- Identify performance bottlenecks
- Test optimization strategies
- Generate performance reports
"""

import numpy as np
import pandas as pd
import time
from datetime import datetime

from src.infrastructure.performance import (
    PerformanceOptimizer,
    performance_monitor
)


# Example functions to optimize
@performance_monitor(threshold_ms=100)
def slow_data_processing(df: pd.DataFrame) -> pd.DataFrame:
    """Example of inefficient data processing."""
    result = df.copy()
    
    # Inefficient: Loop through rows
    for idx, row in df.iterrows():
        result.loc[idx, 'computed'] = row['A'] * row['B'] + np.sin(row['C'])
    
    return result


@performance_monitor(threshold_ms=100)
def fast_data_processing(df: pd.DataFrame) -> pd.DataFrame:
    """Optimized version using vectorization."""
    result = df.copy()
    
    # Efficient: Vectorized operations
    result['computed'] = df['A'] * df['B'] + np.sin(df['C'])
    
    return result


def slow_model_prediction(X: np.ndarray) -> np.ndarray:
    """Simulate slow model predictions."""
    predictions = []
    
    # Process one sample at a time
    for i in range(len(X)):
        # Simulate complex computation
        pred = np.sum(X[i] ** 2) + np.random.randn()
        predictions.append(pred)
        
    return np.array(predictions)


def fast_model_prediction(X: np.ndarray) -> np.ndarray:
    """Optimized batch prediction."""
    # Process all samples at once
    return np.sum(X ** 2, axis=1) + np.random.randn(len(X))


def demonstrate_basic_benchmarking():
    """Demonstrate basic performance benchmarking."""
    print("\n" + "="*80)
    print("BASIC PERFORMANCE BENCHMARKING")
    print("="*80)
    
    # Create test data
    n_rows = 10000
    test_df = pd.DataFrame({
        'A': np.random.randn(n_rows),
        'B': np.random.randn(n_rows),
        'C': np.random.randn(n_rows)
    })
    
    # Initialize optimizer
    optimizer = PerformanceOptimizer()
    
    # Benchmark slow version
    print("\nBenchmarking slow data processing...")
    slow_metrics = optimizer.measure_performance(
        slow_data_processing,
        test_df,
        operation_name="Slow DataFrame Processing",
        n_iterations=3
    )
    
    # Benchmark fast version
    print("\nBenchmarking fast data processing...")
    fast_metrics = optimizer.measure_performance(
        fast_data_processing,
        test_df,
        operation_name="Fast DataFrame Processing",
        n_iterations=3
    )
    
    # Display results
    print(f"\nResults:")
    print(f"  Slow version: {slow_metrics.execution_time:.3f}s")
    print(f"  Fast version: {fast_metrics.execution_time:.3f}s")
    print(f"  Speedup: {slow_metrics.execution_time / fast_metrics.execution_time:.1f}x")
    
    return optimizer


def demonstrate_profiling():
    """Demonstrate code profiling to find bottlenecks."""
    print("\n" + "="*80)
    print("CODE PROFILING")
    print("="*80)
    
    optimizer = PerformanceOptimizer()
    
    # Create a function with multiple bottlenecks
    def complex_analysis(data):
        # Bottleneck 1: Repeated DataFrame operations
        for i in range(100):
            data = data.sort_values('A')
        
        # Bottleneck 2: Inefficient aggregation
        results = []
        for group_name, group_data in data.groupby('category'):
            results.append({
                'category': group_name,
                'mean': group_data['value'].mean(),
                'std': group_data['value'].std()
            })
        
        # Bottleneck 3: String operations
        data['formatted'] = data.apply(
            lambda row: f"{row['category']}_{row['value']:.2f}", axis=1
        )
        
        return pd.DataFrame(results)
    
    # Create test data
    test_data = pd.DataFrame({
        'category': np.random.choice(['A', 'B', 'C'], 1000),
        'value': np.random.randn(1000),
        'A': np.random.randn(1000)
    })
    
    print("\nProfiling complex analysis function...")
    profile_output = optimizer.profile_function(complex_analysis, test_data)
    
    print("\nTop time-consuming operations:")
    print("-" * 60)
    # Show first few lines of profile
    for line in profile_output.split('\n')[5:15]:
        if line.strip():
            print(line)
    
    return optimizer


def demonstrate_optimization_testing():
    """Demonstrate testing different optimization strategies."""
    print("\n" + "="*80)
    print("OPTIMIZATION STRATEGY TESTING")
    print("="*80)
    
    optimizer = PerformanceOptimizer()
    
    # Test pandas optimizations
    print("\n1. Testing Pandas Optimizations...")
    pandas_results = optimizer.optimize_pandas_operations(
        pd.DataFrame(np.random.randn(10000, 3), columns=['A', 'B', 'C'])
    )
    
    for name, result in pandas_results.items():
        print(f"\n{result.optimization_name}:")
        print(f"  Speedup: {result.speedup_factor:.1f}x")
        print(f"  Implementation: {result.implementation_notes}")
    
    # Test numpy optimizations
    print("\n2. Testing NumPy Optimizations...")
    numpy_results = optimizer.optimize_numpy_operations(size=5000)
    
    for name, result in numpy_results.items():
        print(f"\n{result.optimization_name}:")
        print(f"  Speedup: {result.speedup_factor:.1f}x")
        print(f"  Implementation: {result.implementation_notes}")
    
    # Store results for report
    for result in pandas_results.values():
        optimizer.optimization_history.append(result)
    for result in numpy_results.values():
        optimizer.optimization_history.append(result)
    
    return optimizer


def demonstrate_parallelization():
    """Demonstrate parallelization optimization."""
    print("\n" + "="*80)
    print("PARALLELIZATION OPTIMIZATION")
    print("="*80)
    
    optimizer = PerformanceOptimizer()
    
    # Create CPU-bound task
    def cpu_bound_task(n):
        """Simulate CPU-intensive computation."""
        result = 0
        for i in range(n * 1000000):
            result += i ** 0.5
        return result
    
    # Test with different data sizes
    data_list = [10, 10, 10, 10, 10, 10, 10, 10]  # 8 tasks
    
    print("\nTesting parallelization strategies...")
    parallel_results = optimizer.test_parallelization(
        cpu_bound_task,
        data_list,
        max_workers=4
    )
    
    for name, result in parallel_results.items():
        print(f"\n{result.optimization_name}:")
        print(f"  Speedup: {result.speedup_factor:.1f}x")
        print(f"  Implementation: {result.implementation_notes}")
    
    return optimizer


def demonstrate_model_optimization():
    """Demonstrate model-specific optimizations."""
    print("\n" + "="*80)
    print("MODEL INFERENCE OPTIMIZATION")
    print("="*80)
    
    optimizer = PerformanceOptimizer()
    
    # Create mock model
    class MockModel:
        def predict(self, X):
            # Simulate model computation
            return np.sum(X.values ** 2, axis=1) + np.random.randn(len(X)) * 0.1
    
    model = MockModel()
    test_data = pd.DataFrame(np.random.randn(10000, 10))
    
    print("\nTesting model inference optimizations...")
    model_results = optimizer.optimize_model_inference(model, test_data)
    
    for name, result in model_results.items():
        print(f"\n{result.optimization_name}:")
        print(f"  Original time: {result.original_time:.3f}s")
        print(f"  Optimized time: {result.optimized_time:.3f}s")
        print(f"  Speedup: {result.speedup_factor:.1f}x")
        print(f"  Notes: {result.implementation_notes}")
    
    return optimizer


def generate_comprehensive_report(optimizer: PerformanceOptimizer):
    """Generate and display comprehensive performance report."""
    print("\n" + "="*80)
    print("COMPREHENSIVE PERFORMANCE REPORT")
    print("="*80)
    
    # Generate report
    report = optimizer.generate_benchmark_report()
    
    # Display summary
    print(f"\nSystem Information:")
    print(f"  CPU Cores: {report.system_info['cpu_count']}")
    print(f"  Memory: {report.system_info['memory_total']:.1f} GB")
    
    print(f"\nPerformance Summary:")
    print(f"  Total operations benchmarked: {len(report.performance_metrics)}")
    print(f"  Total optimizations tested: {len(report.optimization_results)}")
    
    # Find best optimizations
    if report.optimization_results:
        best_optimization = max(report.optimization_results, key=lambda x: x.speedup_factor)
        print(f"\nBest Optimization:")
        print(f"  {best_optimization.optimization_name}: {best_optimization.speedup_factor:.1f}x speedup")
    
    # Display recommendations
    print(f"\nTop Recommendations:")
    for i, rec in enumerate(report.recommendations[:5], 1):
        print(f"  {i}. {rec}")
    
    # Save report
    report_path = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    optimizer.export_report(report, report_path)
    print(f"\nFull report saved to: {report_path}")
    
    return report


def main():
    """Run all performance optimization demonstrations."""
    print("="*80)
    print("PERFORMANCE OPTIMIZATION FRAMEWORK DEMONSTRATION")
    print("="*80)
    print("This example demonstrates:")
    print("  • Basic performance benchmarking")
    print("  • Code profiling to identify bottlenecks")
    print("  • Testing optimization strategies")
    print("  • Parallelization techniques")
    print("  • Model-specific optimizations")
    
    # Run demonstrations
    optimizer = demonstrate_basic_benchmarking()
    demonstrate_profiling()
    demonstrate_optimization_testing()
    demonstrate_parallelization()
    demonstrate_model_optimization()
    
    # Generate final report
    report = generate_comprehensive_report(optimizer)
    
    # Summary
    print("\n" + "="*80)
    print("CONCLUSION")
    print("="*80)
    print("Performance optimization is critical for:")
    print("  • Handling large-scale market data efficiently")
    print("  • Enabling real-time analysis and monitoring")
    print("  • Reducing computational costs")
    print("  • Improving user experience")
    print("\nRegular performance testing ensures the system maintains")
    print("the <5 second response time required for operational use.")


if __name__ == "__main__":
    main()