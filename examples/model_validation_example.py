#!/usr/bin/env python3
"""
Example of using the comprehensive model validation suite.

This script demonstrates how to validate econometric models with
diagnostic tests, robustness checks, and performance evaluation.
"""

import pandas as pd
import numpy as np
from datetime import datetime

from src.core.models.validation.model_validation_suite import ModelValidationSuite
from src.core.models.time_series.vecm_model import VECMModel
from src.core.models.regime_switching.markov_switching import MarkovSwitchingCurrencyModel
from src.core.models.interfaces import ModelSpecification


def generate_synthetic_data(n_obs: int = 500) -> pd.DataFrame:
    """Generate synthetic time series data for demonstration."""
    np.random.seed(42)
    dates = pd.date_range(start='2015-01-01', periods=n_obs, freq='D')
    
    # Generate correlated time series
    # Simulate exchange rate spread and market integration
    t = np.arange(n_obs)
    
    # Base trends
    trend1 = 100 + 0.1 * t + 10 * np.sin(2 * np.pi * t / 365)
    trend2 = 110 + 0.08 * t + 8 * np.sin(2 * np.pi * t / 365 + 0.5)
    
    # Add regime changes (structural breaks)
    regime1 = t < 200
    regime2 = (t >= 200) & (t < 350)
    regime3 = t >= 350
    
    # Exchange rate spread
    exchange_spread = np.zeros(n_obs)
    exchange_spread[regime1] = 50 + np.random.randn(regime1.sum()) * 10
    exchange_spread[regime2] = 100 + np.random.randn(regime2.sum()) * 15
    exchange_spread[regime3] = 150 + np.random.randn(regime3.sum()) * 20
    
    # Market integration (inversely related to spread with noise)
    integration = 0.9 - 0.005 * exchange_spread + np.random.randn(n_obs) * 0.05
    integration = np.clip(integration, 0, 1)
    
    # Conflict events (Poisson distributed)
    conflict = np.random.poisson(5 + 0.02 * t)
    
    # Create DataFrame
    data = pd.DataFrame({
        'date': dates,
        'exchange_spread': exchange_spread,
        'integration_index': integration,
        'conflict_events': conflict,
        'price_north': trend1 + np.random.randn(n_obs) * 5,
        'price_south': trend2 + np.random.randn(n_obs) * 5
    })
    
    return data


def validate_time_series_model():
    """Demonstrate validation of a time series model."""
    print("\n" + "="*80)
    print("TIME SERIES MODEL VALIDATION")
    print("="*80)
    
    # Generate data
    data = generate_synthetic_data()
    
    # Create model specification
    spec = ModelSpecification(
        model_type='vecm',
        dependent_vars=['integration_index'],
        independent_vars=['exchange_spread', 'conflict_events'],
        lags=2
    )
    
    # Create and fit a simple model (using VECM as example)
    # In practice, would use actual VECM implementation
    class SimpleTimeSeriesModel:
        def __init__(self, specification):
            self.specification = specification
            self.params = None
            self.residuals = None
            
        def fit(self, data):
            # Simple OLS for demonstration
            from sklearn.linear_model import LinearRegression
            y = data[self.specification.dependent_vars[0]]
            X = data[self.specification.independent_vars]
            
            model = LinearRegression()
            model.fit(X, y)
            
            self.params = {f'coef_{i}': c for i, c in enumerate(model.coef_)}
            self.params['intercept'] = model.intercept_
            self.residuals = y - model.predict(X)
            
        def predict(self, data):
            X = data[self.specification.independent_vars]
            y_pred = self.params['intercept']
            for i, var in enumerate(self.specification.independent_vars):
                y_pred += self.params[f'coef_{i}'] * X[var]
            return y_pred
    
    # Fit model
    model = SimpleTimeSeriesModel(spec)
    model.fit(data)
    
    # Validate model
    validator = ModelValidationSuite()
    report = validator.validate_model(
        model, 
        data,
        model_type='time_series',
        comprehensive=True
    )
    
    # Print report
    print(validator.generate_validation_report_text(report))
    
    return report


def validate_regime_switching_model():
    """Demonstrate validation of a regime-switching model."""
    print("\n" + "="*80)
    print("REGIME-SWITCHING MODEL VALIDATION")
    print("="*80)
    
    # Generate data
    data = generate_synthetic_data()
    
    # Create and fit Markov-switching model
    ms_model = MarkovSwitchingCurrencyModel(n_regimes=3)
    
    # Fit model on exchange spread
    ms_model.fit(data['exchange_spread'])
    
    # Create wrapper for validation
    class RegimeSwitchingWrapper:
        def __init__(self, ms_model):
            self.ms_model = ms_model
            self.specification = ModelSpecification(
                model_type='markov_switching',
                dependent_vars=['exchange_spread'],
                independent_vars=[],
                lags=0
            )
            
        def predict(self, data):
            # Return regime-conditional means
            states = self.ms_model.predict_states(data['exchange_spread'])
            predictions = np.zeros(len(data))
            for regime in range(self.ms_model.n_regimes):
                mask = states == regime
                predictions[mask] = self.ms_model.regime_params[regime]['mean']
            return pd.Series(predictions, index=data.index)
        
        @property
        def residuals(self):
            return self.ms_model.residuals if hasattr(self.ms_model, 'residuals') else None
        
        @property
        def params(self):
            return self.ms_model.regime_params
    
    # Wrap model
    wrapped_model = RegimeSwitchingWrapper(ms_model)
    
    # Validate
    validator = ModelValidationSuite()
    report = validator.validate_model(
        wrapped_model,
        data,
        model_type='regime_switching',
        comprehensive=False  # Quick validation
    )
    
    # Print summary
    print(f"\nValidation Summary:")
    print(f"  Overall Validity: {'PASS' if report.overall_validity else 'FAIL'}")
    print(f"  Confidence Score: {report.confidence_score:.1%}")
    print(f"  Diagnostic Tests Passed: {sum(t.passed for t in report.diagnostic_tests)}/{len(report.diagnostic_tests)}")
    
    return report


def demonstrate_robustness_analysis():
    """Demonstrate detailed robustness analysis."""
    print("\n" + "="*80)
    print("ROBUSTNESS ANALYSIS DEMONSTRATION")
    print("="*80)
    
    # Generate data with outliers and structural break
    data = generate_synthetic_data(n_obs=600)
    
    # Add some outliers
    outlier_indices = np.random.choice(len(data), size=20, replace=False)
    data.loc[outlier_indices, 'exchange_spread'] *= 2
    
    # Create simple model
    class RobustTestModel:
        def __init__(self, specification):
            self.specification = specification
            self.coefficients = None
            
        def fit(self, data):
            # Simple regression
            from sklearn.linear_model import LinearRegression
            y = data['integration_index']
            X = data[['exchange_spread']]
            
            model = LinearRegression()
            model.fit(X, y)
            self.coefficients = model.coef_
            
        def predict(self, data):
            return self.coefficients[0] * data['exchange_spread']
        
        @property
        def params(self):
            return {'exchange_spread_coef': self.coefficients[0]}
    
    # Create specification
    spec = ModelSpecification(
        model_type='linear',
        dependent_vars=['integration_index'],
        independent_vars=['exchange_spread'],
        lags=0
    )
    
    # Fit model
    model = RobustTestModel(spec)
    model.fit(data)
    
    print(f"\nBaseline coefficient: {model.params['exchange_spread_coef']:.4f}")
    
    # Run validation focusing on robustness
    validator = ModelValidationSuite()
    report = validator.validate_model(model, data, comprehensive=True)
    
    # Display robustness results
    print("\nRobustness Check Results:")
    print("-" * 60)
    for check in report.robustness_checks:
        print(f"\n{check.check_name}:")
        print(f"  Baseline Estimate: {check.baseline_estimate:.4f}")
        print(f"  Robust Estimate: {check.robust_estimate:.4f}")
        print(f"  Relative Change: {check.relative_change:.1%}")
        print(f"  Is Robust: {'YES' if check.is_robust else 'NO'}")
        print(f"  95% CI: [{check.confidence_interval[0]:.4f}, {check.confidence_interval[1]:.4f}]")
        
        if 'details' in check.__dict__ and check.details:
            for key, value in check.details.items():
                if key != 'error':
                    print(f"  {key}: {value}")
    
    return report


def main():
    """Run all validation examples."""
    print("=" * 80)
    print("MODEL VALIDATION SUITE EXAMPLES")
    print("Demonstrating comprehensive model validation procedures")
    print("=" * 80)
    
    # Example 1: Time Series Model Validation
    ts_report = validate_time_series_model()
    
    # Example 2: Regime-Switching Model Validation
    rs_report = validate_regime_switching_model()
    
    # Example 3: Robustness Analysis
    robustness_report = demonstrate_robustness_analysis()
    
    # Summary
    print("\n" + "=" * 80)
    print("VALIDATION SUMMARY")
    print("=" * 80)
    
    reports = [
        ("Time Series Model", ts_report),
        ("Regime-Switching Model", rs_report),
        ("Robustness Test Model", robustness_report)
    ]
    
    for model_name, report in reports:
        print(f"\n{model_name}:")
        print(f"  Valid: {'✓' if report.overall_validity else '✗'}")
        print(f"  Confidence: {report.confidence_score:.1%}")
        print(f"  Key Recommendations:")
        for rec in report.recommendations[:2]:
            print(f"    - {rec}")
    
    print("\n" + "=" * 80)
    print("CONCLUSION")
    print("=" * 80)
    print("The validation suite provides comprehensive testing including:")
    print("  • Diagnostic tests (autocorrelation, heteroskedasticity, etc.)")
    print("  • Robustness checks (outliers, sample sensitivity, etc.)")
    print("  • Performance metrics (R², RMSE, information criteria)")
    print("  • Cross-validation for predictive accuracy")
    print("\nThis ensures models meet the highest econometric standards")
    print("required for academic publication and policy implementation.")


if __name__ == "__main__":
    main()