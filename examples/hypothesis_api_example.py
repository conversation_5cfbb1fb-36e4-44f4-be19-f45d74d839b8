#!/usr/bin/env python3
"""
Example of using the Hypothesis Testing API.

This script demonstrates how to:
1. List available hypothesis tests
2. Run individual hypothesis tests
3. Run batch hypothesis tests
4. Stream real-time progress updates
5. Retrieve and interpret results
"""

import asyncio
import json
from datetime import date
from typing import Dict, Any
import httpx
import time

# API configuration
API_BASE_URL = "http://localhost:8000/api/v1"
API_KEY = "your-api-key-here"  # Replace with actual API key if auth is enabled


async def list_hypotheses(client: httpx.AsyncClient) -> Dict[str, Any]:
    """List all available hypothesis tests."""
    print("\n=== Listing Available Hypotheses ===")
    
    response = await client.get(f"{API_BASE_URL}/hypothesis/")
    response.raise_for_status()
    
    data = response.json()
    print(f"Found {data['count']} hypothesis tests:")
    
    for hyp in data['hypotheses']:
        print(f"\n{hyp['id']}: {hyp['name']}")
        print(f"  Category: {hyp['category']}")
        print(f"  Description: {hyp['description']}")
        print(f"  Required data: {', '.join(hyp['required_data'])}")
    
    return data


async def get_hypothesis_info(client: httpx.AsyncClient, hypothesis_id: str) -> Dict[str, Any]:
    """Get detailed information about a specific hypothesis."""
    print(f"\n=== Getting Info for {hypothesis_id} ===")
    
    response = await client.get(f"{API_BASE_URL}/hypothesis/{hypothesis_id}/info")
    response.raise_for_status()
    
    data = response.json()
    print(f"Hypothesis: {data['name']}")
    print(f"Methodology: {data['methodology']}")
    print(f"Policy Relevance: {data['policy_relevance']}")
    
    if 'expected_outcomes' in data and data['expected_outcomes']:
        print("\nExpected Outcomes:")
        for key, value in data['expected_outcomes'].items():
            print(f"  {key}: {value}")
    
    return data


async def run_single_hypothesis_test(
    client: httpx.AsyncClient,
    hypothesis_id: str,
    start_date: str = "2020-01-01",
    end_date: str = "2024-12-31"
) -> str:
    """Run a single hypothesis test."""
    print(f"\n=== Running {hypothesis_id} Test ===")
    
    request_data = {
        "start_date": start_date,
        "end_date": end_date,
        "markets": ["Sana'a", "Aden", "Taiz"],  # Example markets
        "commodities": ["wheat_flour", "rice", "sugar"],  # Example commodities
        "config": {
            "confidence_level": 0.95,
            "include_diagnostics": True
        }
    }
    
    response = await client.post(
        f"{API_BASE_URL}/hypothesis/{hypothesis_id}/test",
        json=request_data
    )
    response.raise_for_status()
    
    data = response.json()
    test_id = data['id']
    print(f"Test started: {test_id}")
    print(f"Status: {data['status']}")
    print(f"Estimated duration: {data['estimated_duration_seconds']}s")
    
    return test_id


async def run_batch_hypothesis_tests(
    client: httpx.AsyncClient,
    hypothesis_ids: list,
    parallel: bool = True
) -> str:
    """Run multiple hypothesis tests in batch."""
    print(f"\n=== Running Batch Tests ({len(hypothesis_ids)} tests) ===")
    
    request_data = {
        "hypothesis_ids": hypothesis_ids,
        "start_date": "2020-01-01",
        "end_date": "2024-12-31",
        "parallel": parallel,
        "config": {
            "confidence_level": 0.95
        }
    }
    
    response = await client.post(
        f"{API_BASE_URL}/hypothesis/batch",
        json=request_data
    )
    response.raise_for_status()
    
    data = response.json()
    batch_id = data['batch_id']
    print(f"Batch started: {batch_id}")
    print(f"Mode: {'Parallel' if parallel else 'Sequential'}")
    print(f"Tests: {[t['hypothesis_id'] for t in data['test_ids']]}")
    print(f"Estimated duration: {data['estimated_duration_seconds']}s")
    
    return batch_id, data['test_ids']


async def monitor_test_progress(client: httpx.AsyncClient, test_id: str):
    """Monitor test progress by polling status."""
    print(f"\n=== Monitoring Test {test_id} ===")
    
    while True:
        response = await client.get(f"{API_BASE_URL}/hypothesis/test/{test_id}/status")
        
        if response.status_code == 404:
            print("Test not found")
            break
            
        response.raise_for_status()
        status = response.json()
        
        print(f"Status: {status['status']} - Progress: {status.get('progress', 0)}%")
        
        if status['status'] in ['completed', 'failed']:
            if status['status'] == 'completed':
                print(f"Test completed! Outcome: {status.get('outcome', 'N/A')}")
            else:
                print(f"Test failed: {status.get('error', 'Unknown error')}")
            break
            
        await asyncio.sleep(2)  # Poll every 2 seconds


async def stream_test_progress(test_id: str):
    """Stream real-time progress updates using SSE."""
    print(f"\n=== Streaming Progress for {test_id} ===")
    
    async with httpx.AsyncClient() as client:
        async with client.stream(
            'GET',
            f"{API_BASE_URL}/hypothesis/test/{test_id}/stream",
            timeout=None
        ) as response:
            async for line in response.aiter_lines():
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])
                        print(f"Event: {data['event']} - Status: {data.get('status')} - Progress: {data.get('progress')}%")
                        
                        if data['event'] == 'complete':
                            print(f"Test completed! Outcome: {data.get('outcome', 'N/A')}")
                            break
                    except json.JSONDecodeError:
                        pass


async def get_test_results(client: httpx.AsyncClient, test_id: str) -> Dict[str, Any]:
    """Get comprehensive test results."""
    print(f"\n=== Getting Results for {test_id} ===")
    
    response = await client.get(f"{API_BASE_URL}/hypothesis/test/{test_id}/results")
    response.raise_for_status()
    
    results = response.json()
    
    print(f"\nHypothesis: {results['hypothesis_id']}")
    print(f"Outcome: {results['outcome']}")
    
    # Statistics
    stats = results['statistics']
    print(f"\nStatistics:")
    print(f"  Test Statistic: {stats['test_statistic']:.4f}")
    print(f"  P-value: {stats['p_value']:.4f}")
    print(f"  Effect Size: {stats.get('effect_size', 'N/A')}")
    print(f"  Confidence Interval: {stats.get('confidence_interval', 'N/A')}")
    
    # Policy Interpretation
    interp = results['policy_interpretation']
    print(f"\nPolicy Summary:")
    print(f"  {interp['summary']}")
    
    print(f"\nKey Implications:")
    for imp in interp['implications']:
        print(f"  - {imp}")
    
    print(f"\nRecommendations:")
    for rec in interp['recommendations']:
        print(f"  - {rec}")
    
    return results


async def main():
    """Run example demonstrations."""
    # Headers for API requests
    headers = {
        "X-API-Key": API_KEY,
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient(headers=headers, timeout=30.0) as client:
        try:
            # 1. List all available hypotheses
            await list_hypotheses(client)
            
            # 2. Get detailed info about H1 (Exchange Rate Mechanism)
            await get_hypothesis_info(client, "H1")
            
            # 3. Run H1 test individually
            test_id_h1 = await run_single_hypothesis_test(client, "H1")
            
            # 4. Monitor progress
            await monitor_test_progress(client, test_id_h1)
            
            # 5. Get results
            await get_test_results(client, test_id_h1)
            
            # 6. Run batch test for core hypotheses
            batch_id, test_ids = await run_batch_hypothesis_tests(
                client,
                ["H1", "H2", "H5", "H9"],
                parallel=True
            )
            
            print("\n=== Batch Test Progress ===")
            # Monitor each test in the batch
            for test_info in test_ids:
                test_id = test_info['test_id']
                hypothesis_id = test_info['hypothesis_id']
                print(f"\nChecking {hypothesis_id} (test: {test_id})...")
                
                # Wait a bit for tests to run
                await asyncio.sleep(5)
                
                # Check status
                response = await client.get(f"{API_BASE_URL}/hypothesis/test/{test_id}/status")
                if response.status_code == 200:
                    status = response.json()
                    print(f"  Status: {status['status']} - Progress: {status.get('progress', 0)}%")
            
            print("\n=== Example Complete ===")
            print("This demonstrates the key features of the Hypothesis Testing API:")
            print("- Listing available tests with metadata")
            print("- Running individual tests with custom parameters")
            print("- Batch testing for comprehensive analysis")
            print("- Real-time progress monitoring")
            print("- Retrieving and interpreting results")
            print("\nThe API enables systematic testing of all 13 hypotheses,")
            print("validating the revolutionary discovery that currency fragmentation")
            print("explains the Yemen Paradox.")
            
        except httpx.HTTPError as e:
            print(f"HTTP Error: {e}")
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())